/**
 * <PERSON>y<PERSON><PERSON>a genelinde kull<PERSON>lan tema değişkenleri
 * <PERSON><PERSON><PERSON>, ikonlar ve diğer görsel öğeler için merkezi tanı<PERSON>r
 */

import { CheckCircle, XCircle, AlertCircle, Clock, Activity, RefreshCw, Eye, Bell, Check, AlertTriangle, Info } from 'lucide-react';
import React from 'react';

// Durum tanımlamaları
export const STATUS_TYPES = {
  // Cihaz durumları
  UP: 'up',
  DOWN: 'down',
  WARNING: 'warning',
  CRITICAL: 'critical',
  DEGRADED: 'degraded',
  FLAPPING: 'flapping',
  PARTIAL: 'partial',
  UNKNOWN: 'unknown',

  // Bildirim durumları
  NEW: 'new',
  READ: 'read',
  ACKNOWLEDGED: 'acknowledged',
  RESOLVED: 'resolved'
};

// Durum etiketleri
export const STATUS_LABELS = {
  // Cihaz durumları
  [STATUS_TYPES.UP]: 'Çevrimiçi',
  [STATUS_TYPES.DOWN]: 'Çevrimdışı',
  [STATUS_TYPES.WARNING]: 'Uyarı Veren',
  [STATUS_TYPES.CRITICAL]: 'Kritik',
  [STATUS_TYPES.DEGRADED]: 'Performans Düşük',
  [STATUS_TYPES.FLAPPING]: 'Kararsız',
  [STATUS_TYPES.PARTIAL]: 'Kısmi Veri',
  [STATUS_TYPES.UNKNOWN]: 'Bilinmiyor',

  // Bildirim durumları
  [STATUS_TYPES.NEW]: 'Yeni',
  [STATUS_TYPES.READ]: 'Okundu',
  [STATUS_TYPES.ACKNOWLEDGED]: 'Onaylandı',
  [STATUS_TYPES.RESOLVED]: 'Çözüldü'
};

// Durum renkleri (Tailwind sınıfları)
export const STATUS_COLORS = {
  // Cihaz durumları
  [STATUS_TYPES.UP]: 'text-green-500 dark:text-green-400',
  [STATUS_TYPES.DOWN]: 'text-red-500 dark:text-red-400',
  [STATUS_TYPES.WARNING]: 'text-yellow-500 dark:text-yellow-300',
  [STATUS_TYPES.CRITICAL]: 'text-orange-500 dark:text-orange-300',
  [STATUS_TYPES.DEGRADED]: 'text-purple-500 dark:text-purple-400',
  [STATUS_TYPES.FLAPPING]: 'text-pink-500 dark:text-pink-400',
  [STATUS_TYPES.PARTIAL]: 'text-blue-500 dark:text-blue-400',
  [STATUS_TYPES.UNKNOWN]: 'text-gray-500 dark:text-gray-400',

  // Bildirim durumları
  [STATUS_TYPES.NEW]: 'text-blue-500 dark:text-blue-400',
  [STATUS_TYPES.READ]: 'text-gray-400 dark:text-gray-500',
  [STATUS_TYPES.ACKNOWLEDGED]: 'text-purple-500 dark:text-purple-400',
  [STATUS_TYPES.RESOLVED]: 'text-green-500 dark:text-green-400'
};

// Durum arka plan renkleri (Tailwind sınıfları)
export const STATUS_BG_COLORS = {
  // Cihaz durumları
  [STATUS_TYPES.UP]: 'bg-green-500 dark:bg-green-600',
  [STATUS_TYPES.DOWN]: 'bg-red-500 dark:bg-red-600',
  [STATUS_TYPES.WARNING]: 'bg-yellow-500 dark:bg-yellow-600',
  [STATUS_TYPES.CRITICAL]: 'bg-orange-500 dark:bg-orange-600',
  [STATUS_TYPES.DEGRADED]: 'bg-purple-500 dark:bg-purple-600',
  [STATUS_TYPES.FLAPPING]: 'bg-pink-500 dark:bg-pink-600',
  [STATUS_TYPES.PARTIAL]: 'bg-blue-500 dark:bg-blue-600',
  [STATUS_TYPES.UNKNOWN]: 'bg-gray-500 dark:bg-gray-600',

  // Bildirim durumları
  [STATUS_TYPES.NEW]: 'bg-blue-500 dark:bg-blue-600',
  [STATUS_TYPES.READ]: 'bg-gray-400 dark:bg-gray-600',
  [STATUS_TYPES.ACKNOWLEDGED]: 'bg-purple-500 dark:bg-purple-600',
  [STATUS_TYPES.RESOLVED]: 'bg-green-500 dark:bg-green-600'
};

// Badge varyantları
export const STATUS_VARIANTS = {
  // Cihaz durumları
  [STATUS_TYPES.UP]: 'success',
  [STATUS_TYPES.DOWN]: 'destructive',
  [STATUS_TYPES.WARNING]: 'warning',
  [STATUS_TYPES.CRITICAL]: 'critical',
  [STATUS_TYPES.DEGRADED]: 'degraded',
  [STATUS_TYPES.FLAPPING]: 'flapping',
  [STATUS_TYPES.PARTIAL]: 'partial',
  [STATUS_TYPES.UNKNOWN]: 'secondary',

  // Bildirim durumları
  [STATUS_TYPES.NEW]: 'outline',
  [STATUS_TYPES.READ]: 'outline',
  [STATUS_TYPES.ACKNOWLEDGED]: 'outline',
  [STATUS_TYPES.RESOLVED]: 'success'
};

// Tailwind renk değerleri referansı
// green-500: #10B981
// red-500: #EF4444
// yellow-500: #F59E0B
// orange-500: #F97316
// purple-500: #8B5CF6
// pink-500: #EC4899
// blue-500: #0EA5E9
// gray-500: #9CA3AF
// indigo-500: #6366F1

// Tema durumunu kontrol eden yardımcı fonksiyon
export const isDarkMode = () => {
  if (typeof document !== 'undefined') {
    return document.documentElement.classList.contains('dark');
  }
  return false;
};

// Chart.js için durum renkleri
export const getChartStatusColors = () => {
  const darkMode = isDarkMode();

  return {
    [STATUS_TYPES.UP]: {
      bg: darkMode ? 'rgba(74, 222, 128, 0.7)' : 'rgba(16, 185, 129, 0.7)', // green-400 : green-500
      border: darkMode ? '#4ADE80' : '#10B981' // green-400 : green-500
    },
    [STATUS_TYPES.DOWN]: {
      bg: darkMode ? 'rgba(248, 113, 113, 0.7)' : 'rgba(239, 68, 68, 0.7)', // red-400 : red-500
      border: darkMode ? '#F87171' : '#EF4444' // red-400 : red-500
    },
    [STATUS_TYPES.WARNING]: {
      bg: darkMode ? 'rgba(250, 204, 21, 0.7)' : 'rgba(245, 158, 11, 0.7)', // yellow-300 : yellow-500
      border: darkMode ? '#FCD34D' : '#F59E0B' // yellow-300 : yellow-500
    },
    [STATUS_TYPES.CRITICAL]: {
      bg: darkMode ? 'rgba(251, 146, 60, 0.7)' : 'rgba(249, 115, 22, 0.7)', // orange-300 : orange-500
      border: darkMode ? '#FB923C' : '#F97316' // orange-300 : orange-500
    },
    [STATUS_TYPES.DEGRADED]: {
      bg: darkMode ? 'rgba(192, 132, 252, 0.7)' : 'rgba(139, 92, 246, 0.7)', // purple-400 : purple-500
      border: darkMode ? '#C084FC' : '#8B5CF6' // purple-400 : purple-500
    },
    [STATUS_TYPES.FLAPPING]: {
      bg: darkMode ? 'rgba(244, 114, 182, 0.7)' : 'rgba(236, 72, 153, 0.7)', // pink-400 : pink-500
      border: darkMode ? '#F472B6' : '#EC4899' // pink-400 : pink-500
    },
    [STATUS_TYPES.PARTIAL]: {
      bg: darkMode ? 'rgba(96, 165, 250, 0.7)' : 'rgba(14, 165, 233, 0.7)', // blue-400 : blue-500
      border: darkMode ? '#60A5FA' : '#0EA5E9' // blue-400 : blue-500
    },
    [STATUS_TYPES.UNKNOWN]: {
      bg: darkMode ? 'rgba(156, 163, 175, 0.7)' : 'rgba(107, 114, 128, 0.7)', // gray-400 : gray-500
      border: darkMode ? '#9CA3AF' : '#6B7280' // gray-400 : gray-500
    }
  };
};

// Chart.js için durum renkleri - CHART_STATUS_COLORS yerine bu fonksiyonu kullanın
export const CHART_STATUS_COLORS = getChartStatusColors();

// Grafik renkleri için yardımcı dizi
export const getChartColors = () => {
  const darkMode = isDarkMode();

  return darkMode ? [
    'rgba(96, 165, 250, 0.7)',  // blue-400
    'rgba(74, 222, 128, 0.7)',  // green-400
    'rgba(250, 204, 21, 0.7)',  // yellow-300
    'rgba(248, 113, 113, 0.7)', // red-400
    'rgba(192, 132, 252, 0.7)', // purple-400
    'rgba(244, 114, 182, 0.7)', // pink-400
    'rgba(56, 189, 248, 0.7)',  // sky-400
    'rgba(45, 212, 191, 0.7)',  // teal-400
  ] : [
    'rgba(59, 130, 246, 0.7)',  // blue-500
    'rgba(16, 185, 129, 0.7)',  // green-500
    'rgba(245, 158, 11, 0.7)',  // yellow-500
    'rgba(239, 68, 68, 0.7)',   // red-500
    'rgba(139, 92, 246, 0.7)',  // purple-500
    'rgba(236, 72, 153, 0.7)',  // pink-500
    'rgba(14, 165, 233, 0.7)',  // sky-500
    'rgba(20, 184, 166, 0.7)',  // teal-500
  ];
};

// Grafik renkleri - CHART_COLORS yerine bu fonksiyonu kullanın
export const CHART_COLORS = getChartColors();

// Durum ikonları
export const STATUS_ICONS = {
  [STATUS_TYPES.UP]: CheckCircle,
  [STATUS_TYPES.DOWN]: XCircle,
  [STATUS_TYPES.WARNING]: AlertCircle,
  [STATUS_TYPES.CRITICAL]: AlertCircle,
  [STATUS_TYPES.DEGRADED]: Activity,
  [STATUS_TYPES.FLAPPING]: RefreshCw,
  [STATUS_TYPES.PARTIAL]: Clock,
  [STATUS_TYPES.UNKNOWN]: Clock,

  // Bildirim durumları
  [STATUS_TYPES.NEW]: Bell,
  [STATUS_TYPES.READ]: Eye,
  [STATUS_TYPES.ACKNOWLEDGED]: Check,
  [STATUS_TYPES.RESOLVED]: CheckCircle
};

// Bildirim durumları için özel badge stilleri
export const NOTIFICATION_BADGE_STYLES = {
  [STATUS_TYPES.NEW]: 'border-blue-500 text-blue-600 bg-white dark:border-blue-400 dark:text-blue-300 dark:bg-gray-800 flex items-center gap-1.5',
  [STATUS_TYPES.READ]: 'border-gray-300 text-gray-600 bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 flex items-center gap-1.5',
  [STATUS_TYPES.ACKNOWLEDGED]: 'border-purple-500 text-purple-600 bg-white dark:border-purple-400 dark:text-purple-300 dark:bg-gray-800 flex items-center gap-1.5',
  [STATUS_TYPES.RESOLVED]: 'border-green-500 text-green-600 bg-white dark:border-green-400 dark:text-green-300 dark:bg-gray-800 flex items-center gap-1.5'
};

// Bildirim önem dereceleri
export const SEVERITY_TYPES = {
  CRITICAL: 'critical',
  WARNING: 'warning',
  SUCCESS: 'success',
  INFO: 'info'
};

// Bildirim önem derecesi etiketleri
export const SEVERITY_LABELS = {
  [SEVERITY_TYPES.CRITICAL]: 'Kritik',
  [SEVERITY_TYPES.WARNING]: 'Uyarı',
  [SEVERITY_TYPES.SUCCESS]: 'Başarılı',
  [SEVERITY_TYPES.INFO]: 'Bilgi'
};

// Bildirim önem derecesi renkleri (severity badge'leri için)
export const SEVERITY_BADGE_STYLES = {
  [SEVERITY_TYPES.CRITICAL]: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
  [SEVERITY_TYPES.WARNING]: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
  [SEVERITY_TYPES.SUCCESS]: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
  [SEVERITY_TYPES.INFO]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
};

// Bildirim önem derecesi ikon renkleri (tutarlılık için)
export const SEVERITY_ICON_COLORS = {
  [SEVERITY_TYPES.CRITICAL]: 'text-red-500',
  [SEVERITY_TYPES.WARNING]: 'text-orange-500',
  [SEVERITY_TYPES.SUCCESS]: 'text-green-500',
  [SEVERITY_TYPES.INFO]: 'text-blue-500'
};

// Bildirim satır stilleri
export const NOTIFICATION_ROW_STYLES = {
  [STATUS_TYPES.NEW]: 'bg-blue-50/50 border-l-4 border-l-blue-500 dark:bg-blue-950/30 dark:border-l-blue-400',
  [STATUS_TYPES.READ]: '',
  [STATUS_TYPES.ACKNOWLEDGED]: 'bg-purple-50/50 border-l-4 border-l-purple-500 dark:bg-purple-950/30 dark:border-l-purple-400',
  [STATUS_TYPES.RESOLVED]: 'bg-green-50/50 border-l-4 border-l-green-500 dark:bg-green-950/30 dark:border-l-green-400'
};

// Bildirim metin stilleri
export const NOTIFICATION_TEXT_STYLES = {
  [STATUS_TYPES.NEW]: 'font-semibold text-gray-900 dark:text-gray-100',
  [STATUS_TYPES.READ]: 'font-medium text-gray-700 dark:text-gray-300',
  [STATUS_TYPES.ACKNOWLEDGED]: 'font-medium text-gray-700 dark:text-gray-300',
  [STATUS_TYPES.RESOLVED]: 'font-medium text-gray-700 dark:text-gray-300'
};

// Durum göstergesi boyut sınıfları
export const DOT_SIZES = {
  'sm': 'h-1.5 w-1.5',
  'md': 'h-2 w-2',
  'lg': 'h-3 w-3'
};

// Durum ikonu alma yardımcı fonksiyonu
export const getStatusIcon = (status, size = 'md', useInheritedColor = false) => {
  const Icon = STATUS_ICONS[status] || STATUS_ICONS[STATUS_TYPES.UNKNOWN];
  const sizeClass = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';
  const colorClass = useInheritedColor ? '' : STATUS_COLORS[status] || STATUS_COLORS[STATUS_TYPES.UNKNOWN];

  return <Icon className={`${sizeClass} ${colorClass}`} />;
};

// Bildirim önem derecesi badge'i alma yardımcı fonksiyonu
export const getSeverityBadgeClass = (severity) => {
  return `text-sm font-medium px-2.5 py-1 rounded-full ${SEVERITY_BADGE_STYLES[severity] || SEVERITY_BADGE_STYLES[SEVERITY_TYPES.INFO]}`;
};

// Bildirim satır stili alma yardımcı fonksiyonu
export const getNotificationRowClass = (status) => {
  const baseClass = 'cursor-pointer hover:bg-muted/50';
  const statusClass = NOTIFICATION_ROW_STYLES[status] || '';
  return `${baseClass} ${statusClass}`.trim();
};

// Bildirim metin stili alma yardımcı fonksiyonu
export const getNotificationTextClass = (status) => {
  const baseClass = 'text-sm leading-tight';
  const statusClass = NOTIFICATION_TEXT_STYLES[status] || NOTIFICATION_TEXT_STYLES[STATUS_TYPES.READ];
  return `${baseClass} ${statusClass}`.trim();
};

// Bildirim önem derecesi etiketi alma yardımcı fonksiyonu
export const getSeverityLabel = (severity) => {
  return SEVERITY_LABELS[severity] || SEVERITY_LABELS[SEVERITY_TYPES.INFO];
};

// Bildirim önem derecesi ikonu alma yardımcı fonksiyonu
export const getSeverityIcon = (severity, size = 'md') => {
  const sizeClass = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5';
  const colorClass = SEVERITY_ICON_COLORS[severity] || SEVERITY_ICON_COLORS[SEVERITY_TYPES.INFO];

  switch (severity) {
    case SEVERITY_TYPES.CRITICAL:
    case SEVERITY_TYPES.WARNING:
      return <AlertTriangle className={`${sizeClass} ${colorClass}`} />;
    case SEVERITY_TYPES.SUCCESS:
      return <CheckCircle className={`${sizeClass} ${colorClass}`} />;
    case SEVERITY_TYPES.INFO:
    default:
      return <Info className={`${sizeClass} ${colorClass}`} />;
  }
};

// Genel metin stilleri
export const TEXT_STYLES = {
  // Ana metin renkleri
  PRIMARY: 'text-gray-900 dark:text-gray-100',
  SECONDARY: 'text-gray-700 dark:text-gray-300',
  MUTED: 'text-gray-500 dark:text-gray-400',

  // Özel durumlar
  RESOLUTION_NOTE: 'text-gray-700 dark:text-gray-300'
};
