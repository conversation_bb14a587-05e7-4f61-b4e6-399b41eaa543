import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Label } from '../../components/ui/label';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import {
  Save,
  RefreshCw,
  Activity,
  Clock,
  Database,
  Braces,
  Globe,
  Server,
  Network,
  Shield,
  Mail,
  HardDrive,
  Container
} from 'lucide-react';
import { settingsService } from '../../services/api';
import { notificationService } from '../../services/notification-service';
import { usePageLoading } from '../../hooks/useSmartLoading';
import { SmartPageLoading } from '../../components/ui/smart-loading';

const MonitoringSettings = () => {
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    defaultPingInterval: '5',
    defaultHttpInterval: '10',
    defaultDnsInterval: '10',
    defaultSslInterval: '60',
    defaultTcpInterval: '5',
    defaultSnmpInterval: '5',
    defaultDatabaseInterval: '10',
    defaultApiInterval: '10',
    defaultSmtpInterval: '15',
    defaultWindowsInterval: '15',
    defaultLinuxInterval: '15',
    defaultIpmiInterval: '30',
    defaultDockerInterval: '15',
    defaultDnsServer: '*******',
    customDnsServer: '',
    pingRetentionDays: '30',
    httpRetentionDays: '30',
    dnsRetentionDays: '30',
    sslRetentionDays: '30',
    tcpRetentionDays: '30',
    snmpRetentionDays: '30',
    databaseRetentionDays: '90',
    apiRetentionDays: '30',
    smtpRetentionDays: '30',
    windowsRetentionDays: '30',
    linuxRetentionDays: '30',
    ipmiRetentionDays: '30',
    dockerRetentionDays: '30',
  });
  const [saving, setSaving] = useState(false);

  // Smart loading hook
  const pageLoading = usePageLoading(formData, loading);

  // Verileri yükle
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const settingsData = await settingsService.getAll();

      // Özel DNS sunucusu kontrolü
      if (settingsData.defaultDnsServer &&
          !['*******', '*******', '*******', '**************', '*********'].includes(settingsData.defaultDnsServer)) {
        // Özel DNS sunucusu varsa
        settingsData.customDnsServer = settingsData.defaultDnsServer;
        settingsData.defaultDnsServer = 'custom';
      }

      setFormData(prevData => ({
        ...prevData,
        ...settingsData
      }));
    } catch (error) {
      console.error('Ayarlar yüklenirken hata oluştu:', error);
      setError('Ayarlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Sayfa yüklendiğinde verileri getir
  useEffect(() => {
    loadData();
  }, []);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    // Başarı mesajını sayfada göstermeyi kaldırdık
    // setSuccess(null);

    try {
      // Özel DNS sunucusu seçilmişse, customDnsServer değerini defaultDnsServer olarak kaydet
      const dataToSave = { ...formData };

      if (dataToSave.defaultDnsServer === 'custom') {
        if (!dataToSave.customDnsServer) {
          setError('Özel DNS sunucusu belirtmelisiniz.');
          setSaving(false);
          return;
        }

        // Özel DNS sunucusunu defaultDnsServer olarak ayarla
        dataToSave.defaultDnsServer = dataToSave.customDnsServer;
      }

      // customDnsServer alanını temizle, bu sadece UI için kullanılıyor
      delete dataToSave.customDnsServer;

      await settingsService.update(dataToSave);
      setSaving(false);
      // Sayfada başarı mesajı göstermeyi kaldırdık
      // setSuccess('Ayarlar başarıyla kaydedildi.');

      // Sadece toast bildirimi göster
      notificationService.success('İzleme ayarları kaydedildi', {
        description: 'İzleme ayarları başarıyla güncellendi.',
        persistent: true, // Bildirim panelinde de göster
        category: 'system'
      });

      // Artık sayfada mesaj göstermediğimiz için timeout'a gerek yok
      // setTimeout(() => {
      //   setSuccess(null);
      // }, 3000);
    } catch (error) {
      console.error('Ayarlar kaydedilirken hata oluştu:', error);
      setError('Ayarlar kaydedilirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('İzleme ayarları kaydedilemedi', {
        description: error.response?.data?.error || 'İzleme ayarları kaydedilirken bir hata oluştu.'
      });

      setSaving(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">İzleme Ayarları</h1>
          <p className="text-muted-foreground mt-1 text-base">
            Cihaz izleme ve veri saklama ayarlarını yapılandırın
          </p>
        </div>
        <div className="flex items-center gap-2 self-end sm:self-auto">
          <Button
            type="button"
            variant="outline"
            onClick={loadData}
            size="default"
          >
            İptal
          </Button>
          <Button
            type="submit"
            disabled={saving}
            size="default"
            form="monitoring-settings-form"
          >
            {saving ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
              </>
            ) : (
              'Kaydet'
            )}
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-lg border border-destructive/30">
          <div className="flex items-start">
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Başarı mesajını kaldırdık */}

      {pageLoading.shouldShowFullPageLoading() ? (
        <SmartPageLoading
          loadingType="initial"
          pageTitle="İzleme Ayarları"
          skeletonLayout="default"
        />
      ) : (
        <form id="monitoring-settings-form" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Ağ İzleme Aralıkları */}
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="border-b border-border">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Network className="h-5 w-5 text-blue-500" /> Ağ İzleme Aralıkları
              </CardTitle>
              <CardDescription>
                Ağ bağlantısı ve protokol izleme aralıklarını yapılandırın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="defaultPingInterval" className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  ICMP Ping Aralığı (dakika)
                </Label>
                <Select
                  value={formData.defaultPingInterval}
                  onValueChange={(value) => handleSelectChange('defaultPingInterval', value)}
                >
                  <SelectTrigger id="defaultPingInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultHttpInterval" className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-500" />
                  HTTP/HTTPS Aralığı (dakika)
                </Label>
                <Select
                  value={formData.defaultHttpInterval}
                  onValueChange={(value) => handleSelectChange('defaultHttpInterval', value)}
                >
                  <SelectTrigger id="defaultHttpInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultDnsInterval" className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-500" />
                  DNS Sorgu Aralığı (dakika)
                </Label>
                <Select
                  value={formData.defaultDnsInterval}
                  onValueChange={(value) => handleSelectChange('defaultDnsInterval', value)}
                >
                  <SelectTrigger id="defaultDnsInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultSslInterval" className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-orange-500" />
                  SSL Sertifika Aralığı (dakika)
                </Label>
                <Select
                  value={formData.defaultSslInterval}
                  onValueChange={(value) => handleSelectChange('defaultSslInterval', value)}
                >
                  <SelectTrigger id="defaultSslInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="60">1 saat</SelectItem>
                    <SelectItem value="120">2 saat</SelectItem>
                    <SelectItem value="360">6 saat</SelectItem>
                    <SelectItem value="720">12 saat</SelectItem>
                    <SelectItem value="1440">24 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultTcpInterval" className="flex items-center gap-2">
                  <Network className="h-4 w-4 text-blue-500" />
                  TCP Port Aralığı (dakika)
                </Label>
                <Select
                  value={formData.defaultTcpInterval}
                  onValueChange={(value) => handleSelectChange('defaultTcpInterval', value)}
                >
                  <SelectTrigger id="defaultTcpInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultSnmpInterval">SNMP İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultSnmpInterval}
                  onValueChange={(value) => handleSelectChange('defaultSnmpInterval', value)}
                >
                  <SelectTrigger id="defaultSnmpInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultDatabaseInterval">Veritabanı İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultDatabaseInterval}
                  onValueChange={(value) => handleSelectChange('defaultDatabaseInterval', value)}
                >
                  <SelectTrigger id="defaultDatabaseInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultApiInterval">API İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultApiInterval}
                  onValueChange={(value) => handleSelectChange('defaultApiInterval', value)}
                >
                  <SelectTrigger id="defaultApiInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 dakika</SelectItem>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultSmtpInterval">SMTP İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultSmtpInterval}
                  onValueChange={(value) => handleSelectChange('defaultSmtpInterval', value)}
                >
                  <SelectTrigger id="defaultSmtpInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                    <SelectItem value="120">2 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultWindowsInterval">Windows İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultWindowsInterval}
                  onValueChange={(value) => handleSelectChange('defaultWindowsInterval', value)}
                >
                  <SelectTrigger id="defaultWindowsInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultLinuxInterval">Linux İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultLinuxInterval}
                  onValueChange={(value) => handleSelectChange('defaultLinuxInterval', value)}
                >
                  <SelectTrigger id="defaultLinuxInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultIpmiInterval">IPMI İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultIpmiInterval}
                  onValueChange={(value) => handleSelectChange('defaultIpmiInterval', value)}
                >
                  <SelectTrigger id="defaultIpmiInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                    <SelectItem value="120">2 saat</SelectItem>
                    <SelectItem value="240">4 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultDockerInterval">Docker İzleme Aralığı (dakika)</Label>
                <Select
                  value={formData.defaultDockerInterval}
                  onValueChange={(value) => handleSelectChange('defaultDockerInterval', value)}
                >
                  <SelectTrigger id="defaultDockerInterval">
                    <SelectValue placeholder="Aralık seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 dakika</SelectItem>
                    <SelectItem value="10">10 dakika</SelectItem>
                    <SelectItem value="15">15 dakika</SelectItem>
                    <SelectItem value="30">30 dakika</SelectItem>
                    <SelectItem value="60">1 saat</SelectItem>
                  </SelectContent>
                </Select>
              </div>

            </div>
            <p className="text-xs text-muted-foreground mt-4">
              Bu ayarlar, yeni izleyici eklendiğinde kullanılacak varsayılan değerlerdir.
            </p>
          </CardContent>
        </Card>

        {/* Veri Saklama */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" /> Veri Saklama
            </CardTitle>
            <CardDescription>
              İzleme verilerinin ne kadar süre saklanacağını yapılandırın
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="pingRetentionDays">Ping Saklama Süresi</Label>
                <Select
                  value={formData.pingRetentionDays}
                  onValueChange={(value) => handleSelectChange('pingRetentionDays', value)}
                >
                  <SelectTrigger id="pingRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="httpRetentionDays">HTTP Saklama Süresi</Label>
                <Select
                  value={formData.httpRetentionDays}
                  onValueChange={(value) => handleSelectChange('httpRetentionDays', value)}
                >
                  <SelectTrigger id="httpRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dnsRetentionDays">DNS Saklama Süresi</Label>
                <Select
                  value={formData.dnsRetentionDays}
                  onValueChange={(value) => handleSelectChange('dnsRetentionDays', value)}
                >
                  <SelectTrigger id="dnsRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sslRetentionDays">SSL Saklama Süresi</Label>
                <Select
                  value={formData.sslRetentionDays}
                  onValueChange={(value) => handleSelectChange('sslRetentionDays', value)}
                >
                  <SelectTrigger id="sslRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tcpRetentionDays">TCP Saklama Süresi</Label>
                <Select
                  value={formData.tcpRetentionDays}
                  onValueChange={(value) => handleSelectChange('tcpRetentionDays', value)}
                >
                  <SelectTrigger id="tcpRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="snmpRetentionDays">SNMP Saklama Süresi</Label>
                <Select
                  value={formData.snmpRetentionDays}
                  onValueChange={(value) => handleSelectChange('snmpRetentionDays', value)}
                >
                  <SelectTrigger id="snmpRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="databaseRetentionDays">Veritabanı Saklama Süresi</Label>
                <Select
                  value={formData.databaseRetentionDays}
                  onValueChange={(value) => handleSelectChange('databaseRetentionDays', value)}
                >
                  <SelectTrigger id="databaseRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiRetentionDays">API Saklama Süresi</Label>
                <Select
                  value={formData.apiRetentionDays}
                  onValueChange={(value) => handleSelectChange('apiRetentionDays', value)}
                >
                  <SelectTrigger id="apiRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="smtpRetentionDays">SMTP Saklama Süresi</Label>
                <Select
                  value={formData.smtpRetentionDays}
                  onValueChange={(value) => handleSelectChange('smtpRetentionDays', value)}
                >
                  <SelectTrigger id="smtpRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="windowsRetentionDays">Windows Saklama Süresi</Label>
                <Select
                  value={formData.windowsRetentionDays}
                  onValueChange={(value) => handleSelectChange('windowsRetentionDays', value)}
                >
                  <SelectTrigger id="windowsRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="linuxRetentionDays">Linux Saklama Süresi</Label>
                <Select
                  value={formData.linuxRetentionDays}
                  onValueChange={(value) => handleSelectChange('linuxRetentionDays', value)}
                >
                  <SelectTrigger id="linuxRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ipmiRetentionDays">IPMI Saklama Süresi</Label>
                <Select
                  value={formData.ipmiRetentionDays}
                  onValueChange={(value) => handleSelectChange('ipmiRetentionDays', value)}
                >
                  <SelectTrigger id="ipmiRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dockerRetentionDays">Docker Saklama Süresi</Label>
                <Select
                  value={formData.dockerRetentionDays}
                  onValueChange={(value) => handleSelectChange('dockerRetentionDays', value)}
                >
                  <SelectTrigger id="dockerRetentionDays">
                    <SelectValue placeholder="Saklama süresi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 gün</SelectItem>
                    <SelectItem value="7">7 gün</SelectItem>
                    <SelectItem value="14">14 gün</SelectItem>
                    <SelectItem value="30">30 gün</SelectItem>
                    <SelectItem value="60">60 gün</SelectItem>
                    <SelectItem value="90">90 gün</SelectItem>
                    <SelectItem value="180">180 gün</SelectItem>
                    <SelectItem value="365">1 yıl</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <p className="text-xs text-muted-foreground mt-4">
              Her izleme türü için ayrı saklama süreleri belirleyebilirsiniz. Bu sürelerden daha eski veriler her saat başı otomatik olarak temizlenecektir. Bu ayarlar, cihaz detay sayfasındaki geçmiş sekmesinde görüntülenen tüm izleme türlerinin verilerini etkiler.
            </p>
          </CardContent>
        </Card>
        </div>

        {/* DNS Ayarları */}
        <div className="grid grid-cols-1 md:grid-cols-1 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" /> DNS Ayarları
              </CardTitle>
              <CardDescription>
                DNS izleme için varsayılan ayarları yapılandırın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="defaultDnsServer">Varsayılan DNS Sunucusu</Label>
                  <Select
                    value={formData.defaultDnsServer === 'custom' ? 'custom' : formData.defaultDnsServer}
                    onValueChange={(value) => {
                      if (value === 'custom') {
                        setFormData(prev => ({
                          ...prev,
                          defaultDnsServer: 'custom',
                          customDnsServer: ''
                        }));
                      } else {
                        handleSelectChange('defaultDnsServer', value);
                        // Özel DNS seçilmediğinde customDnsServer değerini temizle
                        setFormData(prev => ({
                          ...prev,
                          customDnsServer: ''
                        }));
                      }
                    }}
                  >
                    <SelectTrigger id="defaultDnsServer">
                      <SelectValue placeholder="DNS sunucusu seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="*******">Google DNS (*******)</SelectItem>
                      <SelectItem value="*******">Cloudflare DNS (*******)</SelectItem>
                      <SelectItem value="*******">Quad9 DNS (*******)</SelectItem>
                      <SelectItem value="**************">OpenDNS (**************)</SelectItem>
                      <SelectItem value="*********">Verisign DNS (*********)</SelectItem>
                      <SelectItem value="custom">Özel DNS Sunucusu</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    DNS sorguları için kullanılacak varsayılan DNS sunucusu. Özel bir DNS sunucusu belirtilmediğinde bu sunucu kullanılır.
                  </p>

                  {formData.defaultDnsServer === 'custom' && (
                    <div className="mt-2">
                      <Label htmlFor="customDnsServer">Özel DNS Sunucusu</Label>
                      <Input
                        id="customDnsServer"
                        name="customDnsServer"
                        value={formData.customDnsServer || ''}
                        onChange={handleChange}
                        placeholder="Örn: *******"
                      />
                      <p className="text-xs text-muted-foreground">
                        Özel DNS sunucusunun IP adresi
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


      </form>
      )}
    </div>
  );
};

export default MonitoringSettings;
