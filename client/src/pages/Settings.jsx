import React, { useState, useEffect, useCallback } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { But<PERSON> } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Switch } from '../components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import {
  RefreshCw,
  Save,
  AlertTriangle,
  Settings as SettingsIcon,
  Mail,
  Bell,
  CheckCircle,
  Activity,
  Server,
  Database,
  Users
} from 'lucide-react';

// API servisleri
import { settingsService, alertService, deviceService, monitorService } from '../services/api';

// Bileşenler
import SystemStatus from '../components/dashboard/SystemStatus';
import UserManagement from './UserManagement.jsx';

// Theme context
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { usePageLoading } from '../hooks/useSmartLoading';
import { SmartPageLoading } from '../components/ui/smart-loading';

const Settings = () => {
  const { theme, toggleTheme } = useTheme();
  // eslint-disable-next-line no-unused-vars
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  const [loading, setLoading] = useState(true);

  // Smart loading hook
  const pageLoading = usePageLoading(null, loading);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  // URL'den tab parametresini al, yoksa 'general' kullan
  const initialTab = searchParams.get('tab') || 'general';
  const [activeTab, setActiveTab] = useState(initialTab);

  // Tab değiştiğinde URL'yi güncelle
  const handleTabChange = (newTab) => {
    setActiveTab(newTab);
    // URL'yi güncelle ama sayfayı yeniden yükleme
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', newTab);
    window.history.pushState({}, '', `${location.pathname}?${newSearchParams.toString()}`);
  };

  // Sistem bilgileri
  const [stats, setStats] = useState({ total: 0, online: 0, offline: 0, warning: 0 });
  const [alerts, setAlerts] = useState([]);

  // Form state
  const [formData, setFormData] = useState({
    emailNotifications: false,
    emailServer: '',
    emailPort: '',
    emailUser: '',
    emailPassword: '',
    emailFrom: '',
    emailTo: '',
    emailSecure: true,

    notificationRetentionDays: '30', // Bildirim saklama süresi
    darkMode: theme === 'dark',
    // İzleme türleri için saklama süreleri
    icmpRetentionDays: '30',
    httpRetentionDays: '30',
    tcpRetentionDays: '30',
    snmpRetentionDays: '30',
    dnsRetentionDays: '30',
    sslRetentionDays: '30',
    databaseRetentionDays: '30',
    apiRetentionDays: '30'
  });

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Ayarları yükle
      const settingsData = await settingsService.getAll();
      setFormData(prevData => ({
        ...prevData,
        ...settingsData,
        darkMode: theme === 'dark'
      }));

      // Sistem bilgilerini yükle
      if (activeTab === 'system') {
        // Cihaz istatistiklerini yükle
        const statusesData = await monitorService.getAllStatuses();
        const devicesData = await deviceService.getAll();

        // İstatistikleri hesapla
        let online = 0;
        let offline = 0;
        let warning = 0;

        devicesData.forEach(device => {
          const status = statusesData[device.id]?.icmp?.status || 'unknown';
          if (status === 'up') online++;
          else if (status === 'down') offline++;
          else warning++;
        });

        setStats({
          total: devicesData.length,
          online,
          offline,
          warning
        });

        // Uyarıları yükle
        const alertsData = await alertService.getAll();
        setAlerts(alertsData);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error loading settings:', err);
      setError('Ayarlar yüklenirken bir hata oluştu.');
      setLoading(false);
    }
  }, [theme, activeTab]);

  // Sayfa yüklendiğinde verileri yükle
  useEffect(() => {
    loadData();
  }, [loadData]);

  // URL parametresi değiştiğinde activeTab'i güncelle
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Switch değişikliklerini işle
  const handleSwitchChange = (name, checked) => {
    setFormData({
      ...formData,
      [name]: checked
    });

    // Dark mode değişikliği
    if (name === 'darkMode') {
      toggleTheme();
    }
  };

  // E-posta adres sayısını hesapla
  const getEmailCount = () => {
    if (!formData.emailTo) return 0;
    return formData.emailTo
      .split(',')
      .map(email => email.trim())
      .filter(email => email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
      .length;
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      // Dark mode ayarını formdan çıkar
      const { darkMode, ...settingsData } = formData;

      // Ayarları kaydet
      await settingsService.update(settingsData);

      setSaving(false);
      setSuccess('Ayarlar başarıyla kaydedildi.');

      // 3 saniye sonra başarı mesajını kaldır
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Ayarlar kaydedilirken bir hata oluştu.');
      setSaving(false);
    }
  };

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Ayarlar"
        skeletonLayout="default"
      />
    );
  }

  return (
    <div className="p-4 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Ayarlar</h1>
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-lg border border-destructive/30">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
            <p>{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-success/15 text-success p-4 rounded-lg border border-success/30">
          <div className="flex items-start">
            <CheckCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
            <p>{success}</p>
          </div>
        </div>
      )}

      <div className="mb-4 flex flex-col gap-4">
        <div className="w-full">
          <Tabs defaultValue="general" value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="w-full grid grid-cols-5">
              <TabsTrigger value="general" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <SettingsIcon className="h-4 w-4" />
                  Genel
                </span>
              </TabsTrigger>
              <TabsTrigger value="monitoring" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <Activity className="h-4 w-4" />
                  İzleme
                </span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <Bell className="h-4 w-4" />
                  Bildirimler
                </span>
              </TabsTrigger>
              <TabsTrigger value="system" className="text-sm">
                <span className="flex items-center justify-center gap-1">
                  <Server className="h-4 w-4" />
                  Sistem
                </span>
              </TabsTrigger>
              {isAdmin && (
                <TabsTrigger value="users" className="text-sm">
                  <span className="flex items-center justify-center gap-1">
                    <Users className="h-4 w-4" />
                    Kullanıcılar
                  </span>
                </TabsTrigger>
              )}
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs defaultValue="general" value={activeTab} onValueChange={handleTabChange} className="space-y-6 w-full">

        <div className="space-y-6">
          <TabsContent value="general" className="space-y-6">
            <form onSubmit={handleSubmit}>
              {/* Uygulama Ayarları */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" /> Uygulama Ayarları
                </CardTitle>
                <CardDescription>
                  Genel uygulama ayarlarını yapılandırın
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="darkMode">Karanlık Mod</Label>
                    <p className="text-sm text-muted-foreground">
                      Karanlık temayı etkinleştirin
                    </p>
                  </div>
                  <Switch
                    id="darkMode"
                    checked={formData.darkMode}
                    onCheckedChange={(checked) => handleSwitchChange('darkMode', checked)}
                  />
                </div>


              </CardContent>
            </Card>

              {/* Form Butonları */}
              <div className="flex justify-end gap-2 mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={loadData}
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Sıfırla
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" /> Kaydet
                    </>
                  )}
                </Button>
              </div>
              </form>
          </TabsContent>

          <TabsContent value="monitoring" className="space-y-6">
            <form onSubmit={handleSubmit}>
              {/* İzleme Türleri Saklama Süreleri */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" /> İzleme Türleri Saklama Süreleri
                  </CardTitle>
                  <CardDescription>
                    Her izleme türü için veri saklama sürelerini yapılandırın
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* ICMP */}
                    <div className="space-y-2">
                      <Label htmlFor="icmpRetentionDays">ICMP Ping Saklama Süresi</Label>
                      <Select
                        value={formData.icmpRetentionDays}
                        onValueChange={(value) => handleSelectChange('icmpRetentionDays', value)}
                      >
                        <SelectTrigger id="icmpRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* HTTP */}
                    <div className="space-y-2">
                      <Label htmlFor="httpRetentionDays">HTTP/HTTPS Saklama Süresi</Label>
                      <Select
                        value={formData.httpRetentionDays}
                        onValueChange={(value) => handleSelectChange('httpRetentionDays', value)}
                      >
                        <SelectTrigger id="httpRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* TCP */}
                    <div className="space-y-2">
                      <Label htmlFor="tcpRetentionDays">TCP Port Saklama Süresi</Label>
                      <Select
                        value={formData.tcpRetentionDays}
                        onValueChange={(value) => handleSelectChange('tcpRetentionDays', value)}
                      >
                        <SelectTrigger id="tcpRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* SNMP */}
                    <div className="space-y-2">
                      <Label htmlFor="snmpRetentionDays">SNMP Saklama Süresi</Label>
                      <Select
                        value={formData.snmpRetentionDays}
                        onValueChange={(value) => handleSelectChange('snmpRetentionDays', value)}
                      >
                        <SelectTrigger id="snmpRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* DNS */}
                    <div className="space-y-2">
                      <Label htmlFor="dnsRetentionDays">DNS Sorgusu Saklama Süresi</Label>
                      <Select
                        value={formData.dnsRetentionDays}
                        onValueChange={(value) => handleSelectChange('dnsRetentionDays', value)}
                      >
                        <SelectTrigger id="dnsRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* SSL */}
                    <div className="space-y-2">
                      <Label htmlFor="sslRetentionDays">SSL Sertifikası Saklama Süresi</Label>
                      <Select
                        value={formData.sslRetentionDays}
                        onValueChange={(value) => handleSelectChange('sslRetentionDays', value)}
                      >
                        <SelectTrigger id="sslRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Veritabanı */}
                    <div className="space-y-2">
                      <Label htmlFor="databaseRetentionDays">Veritabanı Saklama Süresi</Label>
                      <Select
                        value={formData.databaseRetentionDays}
                        onValueChange={(value) => handleSelectChange('databaseRetentionDays', value)}
                      >
                        <SelectTrigger id="databaseRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* API */}
                    <div className="space-y-2">
                      <Label htmlFor="apiRetentionDays">REST API Saklama Süresi</Label>
                      <Select
                        value={formData.apiRetentionDays}
                        onValueChange={(value) => handleSelectChange('apiRetentionDays', value)}
                      >
                        <SelectTrigger id="apiRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <p className="text-xs text-muted-foreground mt-4">
                    Her izleme türü için ayrı saklama süreleri belirleyebilirsiniz. Bu sürelerden daha eski veriler otomatik olarak temizlenecektir. Bu ayarlar, cihaz detay sayfasındaki geçmiş sekmesinde görüntülenen tüm izleme türlerinin verilerini etkiler.
                  </p>
                </CardContent>
              </Card>

              {/* Form Butonları */}
              <div className="flex justify-end gap-2 mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={loadData}
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Sıfırla
                </Button>
                <Button
                  type="submit"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" /> Kaydet
                    </>
                  )}
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <form onSubmit={handleSubmit}>
              {/* E-posta Bildirimleri */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="h-5 w-5" /> E-posta Bildirimleri
                    </CardTitle>
                    <CardDescription>
                      E-posta bildirim ayarlarını yapılandırın
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="emailNotifications">E-posta Bildirimleri</Label>
                        <p className="text-sm text-muted-foreground">
                          Uyarılar için e-posta bildirimleri alın
                        </p>
                      </div>
                      <Switch
                        id="emailNotifications"
                        checked={formData.emailNotifications}
                        onCheckedChange={(checked) => handleSwitchChange('emailNotifications', checked)}
                      />
                    </div>

                    {formData.emailNotifications && (
                      <div className="space-y-4 pt-4 border-t">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="emailServer">SMTP Sunucusu</Label>
                            <Input
                              id="emailServer"
                              name="emailServer"
                              value={formData.emailServer}
                              onChange={handleChange}
                              placeholder="Örn: smtp.gmail.com"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="emailPort">SMTP Port</Label>
                            <Input
                              id="emailPort"
                              name="emailPort"
                              value={formData.emailPort}
                              onChange={handleChange}
                              placeholder="Örn: 587"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="emailUser">SMTP Kullanıcı Adı</Label>
                            <Input
                              id="emailUser"
                              name="emailUser"
                              value={formData.emailUser}
                              onChange={handleChange}
                              placeholder="Örn: <EMAIL>"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="emailPassword">SMTP Şifresi</Label>
                            <Input
                              id="emailPassword"
                              name="emailPassword"
                              type="password"
                              value={formData.emailPassword}
                              onChange={handleChange}
                              placeholder="••••••••"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="emailFrom">Gönderen E-posta</Label>
                            <Input
                              id="emailFrom"
                              name="emailFrom"
                              value={formData.emailFrom}
                              onChange={handleChange}
                              placeholder="Örn: <EMAIL>"
                            />
                          </div>

                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              <Label htmlFor="emailTo">Alıcı E-posta Adresleri</Label>
                              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                                Çoklu Adres Destekli
                              </span>
                              {getEmailCount() > 0 && (
                                <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                                  {getEmailCount()} Adres
                                </span>
                              )}
                            </div>
                            <Input
                              id="emailTo"
                              name="emailTo"
                              value={formData.emailTo}
                              onChange={handleChange}
                              placeholder="<EMAIL>, <EMAIL>, <EMAIL>"
                              className="min-h-[40px]"
                            />
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label htmlFor="emailSecure">Güvenli Bağlantı (SSL/TLS)</Label>
                            <p className="text-sm text-muted-foreground">
                              SMTP sunucusuna güvenli bağlantı kullanın
                            </p>
                          </div>
                          <Switch
                            id="emailSecure"
                            checked={formData.emailSecure}
                            onCheckedChange={(checked) => handleSwitchChange('emailSecure', checked)}
                          />
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Bildirim Ayarları */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-5 w-5" /> Bildirim Ayarları
                    </CardTitle>
                    <CardDescription>
                      Bildirim saklama ve temizleme ayarlarını yapılandırın
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="notificationRetentionDays">Bildirim Saklama Süresi (gün)</Label>
                      <Select
                        value={formData.notificationRetentionDays}
                        onValueChange={(value) => handleSelectChange('notificationRetentionDays', value)}
                      >
                        <SelectTrigger id="notificationRetentionDays">
                          <SelectValue placeholder="Saklama süresi seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 gün</SelectItem>
                          <SelectItem value="7">7 gün</SelectItem>
                          <SelectItem value="14">14 gün</SelectItem>
                          <SelectItem value="30">30 gün</SelectItem>
                          <SelectItem value="60">60 gün</SelectItem>
                          <SelectItem value="90">90 gün</SelectItem>
                          <SelectItem value="180">180 gün</SelectItem>
                          <SelectItem value="365">1 yıl</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground">
                        Bildirimlerin ne kadar süre saklanacağını belirler. Bu süreden daha eski bildirimler otomatik olarak silinir.
                      </p>
                    </div>
                  </CardContent>
                </Card>



                {/* Form Butonları */}
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={loadData}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" /> Sıfırla
                  </Button>
                  <Button
                    type="submit"
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kaydediliyor...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" /> Kaydet
                      </>
                    )}
                  </Button>
                </div>
              </form>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" /> Sistem Servisleri
                  </CardTitle>
                  <CardDescription>
                    Sistem servislerinin durumunu izleyin
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <SystemStatus />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Server className="h-5 w-5" /> Sistem Bilgileri
                  </CardTitle>
                  <CardDescription>
                    Sunucu ve sistem bilgilerini görüntüleyin
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="p-4 border rounded-lg">
                        <h3 className="text-sm font-medium mb-2 flex items-center">
                          <Database className="h-4 w-4 mr-2" /> Veritabanı Durumu
                        </h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Cihaz Sayısı:</span>
                            <span className="text-sm font-medium">{stats.total}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Uyarı Sayısı:</span>
                            <span className="text-sm font-medium">{alerts.length}</span>
                          </div>
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <h3 className="text-sm font-medium mb-2 flex items-center">
                          <Activity className="h-4 w-4 mr-2" /> İzleme Durumu
                        </h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Çevrimiçi Cihazlar:</span>
                            <span className="text-sm font-medium">{stats.online}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Çevrimdışı Cihazlar:</span>
                            <span className="text-sm font-medium">{stats.offline}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {isAdmin && (
            <TabsContent value="users" className="space-y-6">
              <UserManagement />
            </TabsContent>
          )}


        </div>
      </Tabs>
    </div>
  );
};

export default Settings;
