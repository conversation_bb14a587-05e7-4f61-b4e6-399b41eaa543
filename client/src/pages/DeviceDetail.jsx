import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Progress } from '../components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../components/ui/tooltip';
import { Separator } from '../components/ui/separator';
import { Switch } from '../components/ui/switch';
import PaginationComponent from '../components/PaginationComponent';
import MonitorTimesDisplay from '../components/MonitorTimesDisplay';
import StatusIndicator from '../components/StatusIndicator';
import MonitorSettingsSheet from '../components/MonitorSettingsSheet';
import MonitorCard from '../components/MonitorCard';
import DeviceEditDialog from '../components/DeviceEditDialog';
import { notificationService } from '../services/notification-service';
import MonitorTypeIcon from '../components/MonitorTypeIcon';
import { getDefaultReasonForStatus } from '../utils/statusUtils';
import { STATUS_TYPES, STATUS_COLORS, STATUS_BG_COLORS } from '../lib/theme';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';

import {
  // UI İkonları
  ArrowLeft,
  BarChart,
  CheckCircle,
  Clock,
  Code,
  Edit,
  ExternalLink,
  History,
  Info,
  RefreshCw,
  Tag,
  Trash2,
  XCircle,
  // Durum İkonları
  Activity,
  AlertCircle,
  AlertTriangle,
  // Cihaz İkonları
  Battery,
  Building2,
  Cloud,
  Container,
  Cpu,
  Database as DatabaseIcon,
  Globe,
  Globe2,
  HardDrive,
  Mail,
  MemoryStick,
  Monitor,
  Network,
  Package,
  Radio,
  Router,
  Search,
  Server,
  Settings,
  Share2,
  Shield,
  ShieldCheck,
  Terminal,
  Thermometer,
  Webhook,
  Wifi
} from 'lucide-react';

// API servisleri
import { deviceService, monitorService } from '../services/api';

// Context
import { useDevices } from '../contexts/DeviceContext';
import { usePageLoading } from '../hooks/useSmartLoading';
import { SmartPageLoading } from '../components/ui/smart-loading';

// Yardımcı fonksiyonlar
import { getFormattedSubCategory, getSubCategoryIcon } from '../utils/categoryUtils';

// Byte formatı yardımcı fonksiyonu
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// HTTP durum kodu açıklaması
function getHttpStatusDescription(statusCode) {
  const statusCodes = {
    // 1xx - Bilgilendirme
    100: 'Continue - Devam',
    101: 'Switching Protocols - Protokol Değiştirme',
    102: 'Processing - İşleniyor',
    103: 'Early Hints - Erken İpuçları',

    // 2xx - Başarılı
    200: 'OK - Başarılı',
    201: 'Created - Oluşturuldu',
    202: 'Accepted - Kabul Edildi',
    203: 'Non-Authoritative Information - Yetkisiz Bilgi',
    204: 'No Content - İçerik Yok',
    205: 'Reset Content - İçeriği Sıfırla',
    206: 'Partial Content - Kısmi İçerik',

    // 3xx - Yönlendirme
    300: 'Multiple Choices - Çoklu Seçenek',
    301: 'Moved Permanently - Kalıcı Olarak Taşındı',
    302: 'Found - Bulundu',
    303: 'See Other - Diğerine Bak',
    304: 'Not Modified - Değiştirilmedi',
    307: 'Temporary Redirect - Geçici Yönlendirme',
    308: 'Permanent Redirect - Kalıcı Yönlendirme',

    // 4xx - İstemci Hatası
    400: 'Bad Request - Hatalı İstek',
    401: 'Unauthorized - Yetkisiz',
    402: 'Payment Required - Ödeme Gerekli',
    403: 'Forbidden - Yasak',
    404: 'Not Found - Bulunamadı',
    405: 'Method Not Allowed - Metod İzin Verilmiyor',
    406: 'Not Acceptable - Kabul Edilemez',
    407: 'Proxy Authentication Required - Proxy Kimlik Doğrulaması Gerekli',
    408: 'Request Timeout - İstek Zaman Aşımı',
    409: 'Conflict - Çakışma',
    410: 'Gone - Gitti',
    411: 'Length Required - Uzunluk Gerekli',
    412: 'Precondition Failed - Ön Koşul Başarısız',
    413: 'Payload Too Large - Yük Çok Büyük',
    414: 'URI Too Long - URI Çok Uzun',
    415: 'Unsupported Media Type - Desteklenmeyen Medya Türü',
    416: 'Range Not Satisfiable - Aralık Karşılanamıyor',
    417: 'Expectation Failed - Beklenti Başarısız',
    418: "I'm a teapot - Ben bir çaydanlığım",
    422: 'Unprocessable Entity - İşlenemeyen Varlık',
    429: 'Too Many Requests - Çok Fazla İstek',

    // 5xx - Sunucu Hatası
    500: 'Internal Server Error - Dahili Sunucu Hatası',
    501: 'Not Implemented - Uygulanmadı',
    502: 'Bad Gateway - Hatalı Ağ Geçidi',
    503: 'Service Unavailable - Hizmet Kullanılamıyor',
    504: 'Gateway Timeout - Ağ Geçidi Zaman Aşımı',
    505: 'HTTP Version Not Supported - HTTP Sürümü Desteklenmiyor',
    511: 'Network Authentication Required - Ağ Kimlik Doğrulaması Gerekli'
  };

  return statusCodes[statusCode] || `${statusCode} - Bilinmeyen durum kodu`;
}

// Tabs bileşeni
const TabsComponent = ({ children, defaultValue }) => {
  console.log("TabsComponent rendered with defaultValue:", defaultValue);
  return (
    <Tabs defaultValue={defaultValue} className="w-full">
      {children}
    </Tabs>
  );
};

// Progress bileşeni
const ProgressComponent = ({ value, label, color = "primary" }) => {
  // Renk sınıflarını belirle
  let bgClass = "bg-primary/20";
  let indicatorClass = "bg-primary";

  // Durum renklerini kullan
  switch (color) {
    case "warning":
      bgClass = `${STATUS_BG_COLORS[STATUS_TYPES.WARNING]}/20`;
      indicatorClass = STATUS_BG_COLORS[STATUS_TYPES.WARNING];
      break;
    case "destructive":
      bgClass = `${STATUS_BG_COLORS[STATUS_TYPES.DOWN]}/20`;
      indicatorClass = STATUS_BG_COLORS[STATUS_TYPES.DOWN];
      break;
    case "critical":
      bgClass = `${STATUS_BG_COLORS[STATUS_TYPES.CRITICAL]}/20`;
      indicatorClass = STATUS_BG_COLORS[STATUS_TYPES.CRITICAL];
      break;
    case "degraded":
      bgClass = `${STATUS_BG_COLORS[STATUS_TYPES.DEGRADED]}/20`;
      indicatorClass = STATUS_BG_COLORS[STATUS_TYPES.DEGRADED];
      break;
    case "flapping":
      bgClass = `${STATUS_BG_COLORS[STATUS_TYPES.FLAPPING]}/20`;
      indicatorClass = STATUS_BG_COLORS[STATUS_TYPES.FLAPPING];
      break;
    case "partial":
      bgClass = `${STATUS_BG_COLORS[STATUS_TYPES.PARTIAL]}/20`;
      indicatorClass = STATUS_BG_COLORS[STATUS_TYPES.PARTIAL];
      break;
    default:
      // Varsayılan değerler zaten atandı
      break;
  }

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span>{label}</span>
        <span>{value}%</span>
      </div>
      <Progress value={value} className={`h-2 ${bgClass}`} indicatorClassName={indicatorClass} />
    </div>
  );
};

const DeviceDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { devices, statuses, loading: devicesLoading, error: devicesError, loadData: loadDevicesData, checkDevice, isDeviceChecking } = useDevices();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [icmpHistory, setIcmpHistory] = useState([]);
  const [snmpHistory, setSnmpHistory] = useState([]); // eslint-disable-line no-unused-vars
  // eslint-disable-next-line no-unused-vars
  const [dnsHistory, setDnsHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [sslHistory, setSslHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [databaseHistory, setDatabaseHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [apiHistory, setApiHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [smtpHistory, setSmtpHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [windowsHistory, setWindowsHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [linuxHistory, setLinuxHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [ipmiHistory, setIpmiHistory] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [dockerHistory, setDockerHistory] = useState([]);

  // Sayfalandırma için state'ler
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10); // Sayfa başına gösterilecek kayıt sayısı
  const [activeTab, setActiveTab] = useState("status"); // Aktif sekme



  // DeviceContext'ten cihaz ve durum verilerini al
  const device = devices.find(d => d.id === id);
  const status = statuses[id];

  // Smart loading hook
  const pageLoading = usePageLoading(device, loading || devicesLoading);

  // Verileri yükle
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // DeviceContext'ten verileri yükle
      await loadDevicesData();

      setLoading(false);
    } catch (err) {
      console.error('Error loading device details:', err);
      setError('Cihaz detayları yüklenirken bir hata oluştu: ' + (err.message || 'Bilinmeyen hata'));
      setLoading(false);
    }
  }, [loadDevicesData]);

  // Geçmiş verilerini yükle
  const loadHistoryData = useCallback(async () => {
    if (!device) return;

    console.log('Loading history data for device:', device.name);

    // Aktif sekmeyi kontrol et
    const activeTab = document.querySelector('[data-state="active"][data-radix-collection-item]');
    console.log('Active tab:', activeTab ? activeTab.getAttribute('value') : 'none');

    try {
      // ICMP geçmişini yükle
      try {
        console.log('Loading ICMP history...');
        const icmpHistoryData = await monitorService.getIcmpHistory(id);
        console.log('ICMP history data:', icmpHistoryData);
        console.log('ICMP history loaded:', icmpHistoryData ? icmpHistoryData.length : 0, 'records');
        if (icmpHistoryData) {
          setIcmpHistory(icmpHistoryData);
        } else {
          console.error('ICMP history data is null or undefined');
          setIcmpHistory([]);
        }
      } catch (err) {
        console.error('Error loading ICMP history:', err);
        setIcmpHistory([]);
      }

      // SNMP geçmişini yükle (varsa)
      if (device.monitors?.snmp?.enabled) {
        try {
          console.log('Loading SNMP history...');
          const snmpHistoryData = await monitorService.getSnmpHistory(id);
          console.log('SNMP history data:', snmpHistoryData);
          console.log('SNMP history loaded:', snmpHistoryData ? snmpHistoryData.length : 0, 'records');
          if (snmpHistoryData) {
            setSnmpHistory(snmpHistoryData);
          } else {
            console.error('SNMP history data is null or undefined');
            setSnmpHistory([]);
          }
        } catch (err) {
          console.error('Error loading SNMP history:', err);
          setSnmpHistory([]);
        }
      }

      // Yeni izleme türlerinin geçmişlerini yükle
      const monitors = device.monitors || {};

      if (monitors.dns && monitors.dns.enabled) {
        try {
          console.log('Loading DNS history...');
          const dnsHistoryData = await monitorService.getDnsHistory(id);
          console.log('DNS history data:', dnsHistoryData);
          console.log('DNS history loaded:', dnsHistoryData ? dnsHistoryData.length : 0, 'records');
          if (dnsHistoryData) {
            setDnsHistory(dnsHistoryData);
          } else {
            console.error('DNS history data is null or undefined');
            setDnsHistory([]);
          }
        } catch (err) {
          console.error('Error loading DNS history:', err);
          setDnsHistory([]);
        }
      }

      if (monitors.ssl && monitors.ssl.enabled) {
        try {
          console.log('Loading SSL history...');
          const sslHistoryData = await monitorService.getSslHistory(id);
          console.log('SSL history data:', sslHistoryData);
          console.log('SSL history loaded:', sslHistoryData ? sslHistoryData.length : 0, 'records');
          if (sslHistoryData) {
            setSslHistory(sslHistoryData);
          } else {
            console.error('SSL history data is null or undefined');
            setSslHistory([]);
          }
        } catch (err) {
          console.error('Error loading SSL history:', err);
          setSslHistory([]);
        }
      }

      if (monitors.database && monitors.database.enabled) {
        try {
          console.log('Loading database history...');
          const dbHistoryData = await monitorService.getDatabaseHistory(id);
          console.log('Database history data:', dbHistoryData);
          console.log('Database history loaded:', dbHistoryData ? dbHistoryData.length : 0, 'records');
          if (dbHistoryData) {
            setDatabaseHistory(dbHistoryData);
          } else {
            console.error('Database history data is null or undefined');
            setDatabaseHistory([]);
          }
        } catch (err) {
          console.error('Error loading database history:', err);
          setDatabaseHistory([]);
        }
      }

      if (monitors.api && monitors.api.enabled) {
        try {
          console.log('Loading API history...');
          const apiHistoryData = await monitorService.getApiHistory(id);
          console.log('API history data:', apiHistoryData);
          console.log('API history loaded:', apiHistoryData ? apiHistoryData.length : 0, 'records');
          if (apiHistoryData) {
            setApiHistory(apiHistoryData);
          } else {
            console.error('API history data is null or undefined');
            setApiHistory([]);
          }
        } catch (err) {
          console.error('Error loading API history:', err);
          setApiHistory([]);
        }
      }

      // HTTP geçmişini yükle (varsa)
      if (device.monitors?.http?.enabled) {
        try {
          console.log('Loading HTTP history...');
          const httpHistoryData = await monitorService.getHttpHistory(id);
          console.log('HTTP history data:', httpHistoryData);
          console.log('HTTP history loaded:', httpHistoryData ? httpHistoryData.length : 0, 'records');
          // HTTP geçmişi için ayrı bir state olmadığından, ICMP geçmişine ekleyebiliriz
          if (httpHistoryData && httpHistoryData.length > 0) {
            setIcmpHistory(prev => [...(prev || []), ...httpHistoryData]);
          } else {
            console.error('HTTP history data is null, undefined or empty');
          }
        } catch (err) {
          console.error('Error loading HTTP history:', err);
        }
      }

      // SMTP geçmişini yükle (varsa)
      if (monitors.smtp && monitors.smtp.enabled) {
        try {
          console.log('Loading SMTP history...');
          const smtpHistoryData = await monitorService.getSmtpHistory(id);
          console.log('SMTP history data:', smtpHistoryData);
          console.log('SMTP history loaded:', smtpHistoryData ? smtpHistoryData.length : 0, 'records');
          if (smtpHistoryData) {
            setSmtpHistory(smtpHistoryData);
          } else {
            console.error('SMTP history data is null or undefined');
            setSmtpHistory([]);
          }
        } catch (err) {
          console.error('Error loading SMTP history:', err);
          setSmtpHistory([]);
        }
      }



      // IPMI geçmişini yükle (varsa)
      if (monitors.ipmi && monitors.ipmi.enabled) {
        try {
          console.log('Loading IPMI history...');
          const ipmiHistoryData = await monitorService.getIpmiHistory(id);
          console.log('IPMI history data:', ipmiHistoryData);
          console.log('IPMI history loaded:', ipmiHistoryData ? ipmiHistoryData.length : 0, 'records');
          if (ipmiHistoryData) {
            setIpmiHistory(ipmiHistoryData);
          } else {
            console.error('IPMI history data is null or undefined');
            setIpmiHistory([]);
          }
        } catch (err) {
          console.error('Error loading IPMI history:', err);
          setIpmiHistory([]);
        }
      }

      // Docker geçmişini yükle (varsa)
      if (monitors.docker && monitors.docker.enabled) {
        try {
          console.log('Loading Docker history...');
          const dockerHistoryData = await monitorService.getDockerHistory(id);
          console.log('Docker history data:', dockerHistoryData);
          console.log('Docker history loaded:', dockerHistoryData ? dockerHistoryData.length : 0, 'records');
          if (dockerHistoryData) {
            setDockerHistory(dockerHistoryData);
          } else {
            console.error('Docker history data is null or undefined');
            setDockerHistory([]);
          }
        } catch (err) {
          console.error('Error loading Docker history:', err);
          setDockerHistory([]);
        }
      }

      // Sayfalandırma için sayfa numarasını sıfırla
      setCurrentPage(1);

    } catch (err) {
      console.error('Error loading history data:', err);
    }
  }, [id, device]);

  // Sayfa yüklendiğinde verileri yükle
  useEffect(() => {
    loadData();
  }, [loadData]);





  // Cihaz yüklendiğinde geçmiş verilerini yükle
  useEffect(() => {
    if (device) {
      loadHistoryData();
    }
  }, [device, loadHistoryData]);

  // Cihazı manuel olarak kontrol et
  const handleCheck = async () => {
    try {
      // DeviceContext'teki checkDevice fonksiyonunu kullan
      const success = await checkDevice(id);

      if (success) {
        notificationService.success('Kontrol tamamlandı', {
          description: 'Cihaz durumu güncellendi.'
        });
      } else {
        notificationService.error('Kontrol başarısız', {
          description: 'Cihaz kontrol edilirken bir hata oluştu.'
        });
      }
    } catch (err) {
      console.error('Error checking device:', err);
      setError('Cihaz kontrol edilirken bir hata oluştu.');

      // Hata bildirimi
      notificationService.error('Kontrol başarısız', {
        description: 'Cihaz kontrol edilirken bir hata oluştu.'
      });
    }
  };

  // İzleyici durumunu güncelle (açık/kapalı)
  const toggleMonitor = async (monitorType, enabled) => {
    try {
      if (!device || !device.monitors) return;

      // Sistem izleyicileri için özel kontrol
      const systemMonitors = ['system', 'docker'];

      if (enabled && systemMonitors.includes(monitorType)) {
        // Sistem monitoring için platform kontrolü
        if (monitorType === 'system') {
          const currentSettings = device.monitors?.system || {};
          const platform = currentSettings.platform || device.platform || 'linux';

          // Platform özel kimlik bilgileri kontrolü
          if (platform === 'windows') {
            // Windows için kimlik bilgileri gerekli
            if (!currentSettings.username || !currentSettings.password) {
              // Sheet'i aç - kullanıcı bilgileri girsin
              setSettingsSheet({
                open: true,
                monitorType: 'system'
              });
              return;
            }
          } else if (platform === 'linux') {
            // Linux için SSH bilgileri gerekli
            if (!currentSettings.username || (!currentSettings.password && !currentSettings.privateKey)) {
              setSettingsSheet({
                open: true,
                monitorType: 'system'
              });
              return;
            }
          }
          // Local için kimlik bilgisi gerekmez, direkt devam et
        }

        // Docker için de ayarlar modalını aç
        if (monitorType === 'docker') {
          setSettingsSheet({
            open: true,
            monitorType: 'docker'
          });
          return;
        }
      }

      // Mevcut izleyici ayarlarını kopyala
      const updatedMonitors = { ...device.monitors };

      // İzleyici yoksa oluştur
      if (!updatedMonitors[monitorType]) {
        updatedMonitors[monitorType] = { enabled: false };
      }

      // İzleyici durumunu güncelle
      updatedMonitors[monitorType].enabled = enabled;

      // DNS izleyicisi etkinleştiriliyorsa, akıllı varsayılan değerler ekle
      if (monitorType === 'dns' && enabled) {
        // DNS sunucusu belirtilmemişse, varsayılan olarak Google DNS kullan
        if (!updatedMonitors.dns.server) {
          updatedMonitors.dns.server = '*******';
        }

        // Sorgulanacak alan adı belirtilmemişse ve host bir IP adresi değilse
        if (!updatedMonitors.dns.domain && !isIpAddress(device.host)) {
          updatedMonitors.dns.domain = device.host;
        } else if (!updatedMonitors.dns.domain) {
          // Host bir IP adresi ise ve domain belirtilmemişse, varsayılan olarak google.com kullan
          updatedMonitors.dns.domain = 'google.com';
        }

        // Kayıt türü belirtilmemişse, varsayılan olarak A kullan
        if (!updatedMonitors.dns.recordType) {
          updatedMonitors.dns.recordType = 'A';
        }
      }

      // Cihazı güncelle
      const updatedDevice = {
        ...device,
        monitors: updatedMonitors
      };

      await deviceService.update(id, updatedDevice);

      // Verileri yeniden yükle
      loadData();
    } catch (err) {
      console.error(`Error toggling ${monitorType} monitor:`, err);
      setError(`İzleyici durumu güncellenirken bir hata oluştu: ${err.message}`);
    }
  };

  // IP adresi kontrolü için yardımcı fonksiyon
  const isIpAddress = (host) => {
    if (!host || typeof host !== 'string') return false;

    // IPv4 regex
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    // IPv6 regex (basitleştirilmiş)
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

    return ipv4Regex.test(host) || ipv6Regex.test(host);
  };

  // İzleme türü ayarları sheet'i için state
  const [settingsSheet, setSettingsSheet] = useState({
    open: false,
    monitorType: null
  });

  // İzleme türü ayarları butonuna tıklandığında
  const handleSettingsClick = (monitorType) => {
    setSettingsSheet({
      open: true,
      monitorType
    });
  };

  // İzleme türleri için ikonlar - MonitorTypeIcon bileşenini kullanıyoruz
  const getMonitorIcon = (type) => {
    return <MonitorTypeIcon type={type} size="md" />;
  };

  // İzleme türleri için başlıklar
  const monitorTitles = {
    icmp: 'ICMP Ping',
    tcp: 'TCP Port',
    http: 'HTTP/HTTPS',
    dns: 'DNS Sorgusu',
    ssl: 'SSL Sertifikası'
  };

  // Kategori bazlı izleyici grupları (platform olmayan cihazlar için)
  const getCategoryBasedGroups = (category) => {
    if (!category) {
      return {
        network: {
          title: 'Ağ İzleyicileri',
          icon: Network,
          monitors: ['icmp', 'snmp', 'http', 'tcp']
        }
      };
    }

    if (category.startsWith('Ağ Cihazları/')) {
      return {
        network: {
          title: 'Ağ İzleyicileri',
          icon: Network,
          monitors: ['icmp', 'snmp', 'http', 'tcp']
        }
      };
    }

    if (category === 'Sunucular/Container') {
      return {
        container: {
          title: 'Container İzleyicileri',
          icon: Package,
          monitors: ['icmp', 'docker', 'http']
        }
      };
    }

    if (category.startsWith('IoT/')) {
      return {
        hardware: {
          title: 'Donanım İzleyicileri',
          icon: Cpu,
          monitors: ['icmp', 'snmp', 'http', 'tcp']
        }
      };
    }

    if (category.startsWith('Web/')) {
      return {
        web: {
          title: 'Web İzleyicileri',
          icon: Globe,
          monitors: ['icmp', 'http', 'dns', 'ssl']
        }
      };
    }

    // Varsayılan
    return {
      network: {
        title: 'Ağ İzleyicileri',
        icon: Network,
        monitors: ['icmp', 'snmp', 'http', 'tcp']
      }
    };
  };

  // Yeni sabit grup yapısı - DeviceList ile uyumlu
  const getMonitorGroups = (platform, category) => {
    // Sabit 4 grup yapısı
    const groups = {
      network: {
        title: 'Ağ İzleyicileri',
        icon: 'network', // MonitorTypeIcon grup ikonu
        monitors: []
      },
      system: {
        title: 'Sistem İzleyicileri',
        icon: 'system', // MonitorTypeIcon için
        monitors: []
      },
      services: {
        title: 'Servis İzleyicileri',
        icon: 'services', // MonitorTypeIcon grup ikonu
        monitors: []
      },
      security: {
        title: 'Güvenlik İzleyicileri',
        icon: 'security', // MonitorTypeIcon grup ikonu
        monitors: []
      }
    };

    // Platform bazlı içerik dağılımı
    if (platform === 'windows') {
      groups.network.monitors = ['icmp', 'tcp', 'http'];
      groups.system.monitors = ['system', 'snmp', 'ipmi'];
      groups.services.monitors = ['database', 'api', 'smtp'];
      groups.security.monitors = ['ssl', 'dns'];

    } else if (platform === 'linux') {
      groups.network.monitors = ['icmp', 'tcp', 'http'];
      groups.system.monitors = ['system', 'snmp', 'ipmi'];
      groups.services.monitors = ['database', 'api', 'smtp', 'docker'];
      groups.security.monitors = ['ssl', 'dns'];

    } else {
      // Kategori bazlı içerik dağılımı
      if (category?.startsWith('Ağ Cihazları/')) {
        groups.network.monitors = ['icmp', 'tcp', 'http'];
        groups.system.monitors = ['snmp'];
        groups.services.monitors = []; // Boş - gizlenecek
        groups.security.monitors = ['dns'];
      } else if (category === 'Sunucular/Container') {
        groups.network.monitors = ['icmp', 'tcp', 'http'];
        groups.system.monitors = ['system', 'snmp'];
        groups.services.monitors = ['docker', 'api'];
        groups.security.monitors = ['ssl', 'dns'];
      } else if (category?.startsWith('Web/')) {
        groups.network.monitors = ['icmp', 'tcp', 'http'];
        groups.system.monitors = ['system', 'snmp'];
        groups.services.monitors = ['database', 'api', 'smtp'];
        groups.security.monitors = ['ssl', 'dns'];
      } else if (category?.startsWith('IoT/')) {
        groups.network.monitors = ['icmp', 'tcp', 'http'];
        groups.system.monitors = ['snmp'];
        groups.services.monitors = ['api'];
        groups.security.monitors = ['ssl'];
      } else {
        // Varsayılan dağılım
        groups.network.monitors = ['icmp', 'tcp', 'http'];
        groups.system.monitors = ['system', 'snmp', 'ipmi'];
        groups.services.monitors = ['database', 'api', 'smtp', 'docker'];
        groups.security.monitors = ['ssl', 'dns'];
      }
    }

    // Boş grupları filtrele (gizle)
    const filteredGroups = {};
    Object.entries(groups).forEach(([key, group]) => {
      if (group.monitors.length > 0) {
        filteredGroups[key] = group;
      }
    });

    return filteredGroups;
  };



  // Platform bazlı monitor grupları
  const devicePlatform = device?.platform || device?.monitors?.system?.platform || 'default';
  const monitorGroups = getMonitorGroups(devicePlatform, device?.group);



  // Artık kullanılmayan render fonksiyonları kaldırıldı - metrikler Durum sekmesinde

  // Sistem metrikleri (Windows/Linux/Local) - KALDIRILDI
  const renderSystemMetrics_REMOVED = () => {
    const primaryMonitor = device.monitors?.system?.enabled ? 'system' : 'snmp';
    const primaryStatus = status?.[primaryMonitor];
    const platform = device.platform || device.monitors?.system?.platform || 'local';

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MonitorTypeIcon type="system" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                {platform === 'windows' ? 'Windows Sistem Metrikleri' :
                 platform === 'linux' ? 'Linux Sistem Metrikleri' :
                 'Local Sistem Metrikleri'}
              </CardTitle>
              <CardDescription>
                Sistem performans ve donanım bilgileri
                {primaryStatus && (
                  <MonitorTimesDisplay
                    monitorData={{
                      ...primaryStatus,
                      deviceId: id,
                      type: primaryMonitor
                    }}
                  />
                )}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleSettingsClick(primaryMonitor)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {device.monitors?.system?.enabled && status?.system?.status === 'up' ? (
            <div className="space-y-6">
              {/* Platform Bilgisi */}
              <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    {platform === 'windows' ? (
                      <MonitorTypeIcon type="windows" size="lg" />
                    ) : platform === 'linux' ? (
                      <MonitorTypeIcon type="linux" size="lg" />
                    ) : (
                      <Server className="h-6 w-6" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium">
                      {platform === 'windows' ? 'Windows Sistemi' :
                       platform === 'linux' ? 'Linux Sistemi' :
                       'Local Sistem'}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {status.system.details?.system?.os?.distro || 'Sistem bilgisi'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">Bağlantı Süresi</div>
                  <div className="text-lg font-bold">{status.system.responseTime} ms</div>
                </div>
              </div>

              {/* Sistem Metrikleri Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* CPU Kullanımı */}
                {status.system.details?.system?.cpu && (
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">CPU</CardTitle>
                        <Cpu className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold mb-2">
                        {status.system.details.system.cpu.usage !== undefined ? `${status.system.details.system.cpu.usage}%` : 'N/A'}
                      </div>
                      {status.system.details.system.cpu.usage !== undefined && (
                        <Progress
                          value={status.system.details.system.cpu.usage}
                          className="h-2 bg-primary/20"
                          indicatorClassName={status.system.details.system.cpu.usage > 80 ? "bg-destructive" : status.system.details.system.cpu.usage > 60 ? "bg-warning" : "bg-primary"}
                        />
                      )}
                      <div className="mt-2 text-xs text-muted-foreground">
                        {status.system.details.system.cpu.brand && (
                          <div>{status.system.details.system.cpu.brand}</div>
                        )}
                        {status.system.details.system.cpu.cores && (
                          <div>{status.system.details.system.cpu.cores} çekirdek</div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* RAM Kullanımı */}
                {status.system.details?.system?.memory && (
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">Bellek</CardTitle>
                        <MemoryStick className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold mb-2">
                        {status.system.details.system.memory.usage !== undefined ? `${status.system.details.system.memory.usage}%` : 'N/A'}
                      </div>
                      {status.system.details.system.memory.usage !== undefined && (
                        <Progress
                          value={status.system.details.system.memory.usage}
                          className="h-2 bg-primary/20"
                          indicatorClassName={status.system.details.system.memory.usage > 80 ? "bg-destructive" : status.system.details.system.memory.usage > 60 ? "bg-warning" : "bg-primary"}
                        />
                      )}
                      <div className="mt-2 text-xs text-muted-foreground">
                        {status.system.details.system.memory.used && status.system.details.system.memory.total && (
                          <div>
                            {formatBytes(status.system.details.system.memory.used)} / {formatBytes(status.system.details.system.memory.total)}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Disk Kullanımı */}
                {status.system.details?.system?.disk && Array.isArray(status.system.details.system.disk) && status.system.details.system.disk.length > 0 && (
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">Disk</CardTitle>
                        <HardDrive className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {status.system.details.system.disk.slice(0, 2).map((disk, index) => (
                          <div key={index}>
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-sm font-medium">{disk.device || `Disk ${index + 1}`}</span>
                              <span className="text-sm">{disk.usage !== undefined ? `${disk.usage}%` : 'N/A'}</span>
                            </div>
                            {disk.usage !== undefined && (
                              <Progress
                                value={disk.usage}
                                className="h-1.5"
                                indicatorClassName={disk.usage > 80 ? "bg-destructive" : disk.usage > 60 ? "bg-warning" : "bg-primary"}
                              />
                            )}
                            {disk.size && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {formatBytes(disk.size)}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* IPMI Donanım Metrikleri */}
              {device.monitors?.ipmi?.enabled && status?.ipmi?.status === 'up' && (
                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                    <Cpu className="h-5 w-5" />
                    Donanım Metrikleri (IPMI)
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Sıcaklık */}
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">Sıcaklık</CardTitle>
                          <Thermometer className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-4 text-muted-foreground">
                          IPMI sıcaklık verileri yakında eklenecek
                        </div>
                      </CardContent>
                    </Card>

                    {/* Fan Durumu */}
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">Fan Durumu</CardTitle>
                          <Activity className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-4 text-muted-foreground">
                          IPMI fan verileri yakında eklenecek
                        </div>
                      </CardContent>
                    </Card>

                    {/* Güç Durumu */}
                    <Card>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">Güç</CardTitle>
                          <Battery className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-4 text-muted-foreground">
                          IPMI güç verileri yakında eklenecek
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Server className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Sistem Metrikleri Mevcut Değil</h3>
              <p className="text-muted-foreground max-w-md">
                Bu cihaz için sistem monitoring etkinleştirilmemiş.
                Metrikleri görüntülemek için sistem monitoring'i etkinleştirin.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Network metrikleri (Network cihazları)
  const renderNetworkMetrics = () => {
    const primaryStatus = status?.snmp;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MonitorTypeIcon type="snmp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                Ağ Cihazı Metrikleri
              </CardTitle>
              <CardDescription>
                SNMP protokolü ile ağ cihazı performans bilgileri
                {primaryStatus && (
                  <MonitorTimesDisplay
                    monitorData={{
                      ...primaryStatus,
                      deviceId: id,
                      type: 'snmp'
                    }}
                  />
                )}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleSettingsClick('snmp')}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {device.monitors?.snmp?.enabled && status?.snmp?.status === 'up' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">CPU</CardTitle>
                    <Cpu className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold mb-2">
                    {status.snmp.cpuUsage !== undefined ? `${status.snmp.cpuUsage}%` : 'N/A'}
                  </div>
                  {status.snmp.cpuUsage !== undefined && (
                    <Progress
                      value={status.snmp.cpuUsage}
                      className="h-2 bg-primary/20"
                      indicatorClassName={status.snmp.cpuUsage > 80 ? "bg-destructive" : status.snmp.cpuUsage > 60 ? "bg-warning" : "bg-primary"}
                    />
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">Bellek</CardTitle>
                    <MemoryStick className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold mb-2">
                    {status.snmp.memoryUsage !== undefined ? `${status.snmp.memoryUsage}%` : 'N/A'}
                  </div>
                  {status.snmp.memoryUsage !== undefined && (
                    <Progress
                      value={status.snmp.memoryUsage}
                      className="h-2 bg-primary/20"
                      indicatorClassName={status.snmp.memoryUsage > 80 ? "bg-destructive" : status.snmp.memoryUsage > 60 ? "bg-warning" : "bg-primary"}
                    />
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">Ağ Trafiği</CardTitle>
                    <Network className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  {status.snmp.networkIn !== undefined && status.snmp.networkOut !== undefined ? (
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Gelen</span>
                        <span className="text-sm font-mono">{formatBytes(status.snmp.networkIn)}/s</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Giden</span>
                        <span className="text-sm font-mono">{formatBytes(status.snmp.networkOut)}/s</span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      Ağ verisi mevcut değil
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">Port Durumu</CardTitle>
                    <Share2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-4 text-muted-foreground">
                    Port bilgileri yakında eklenecek
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Network className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Ağ Metrikleri Mevcut Değil</h3>
              <p className="text-muted-foreground max-w-md">
                Bu cihaz için SNMP monitoring etkinleştirilmemiş.
                Metrikleri görüntülemek için SNMP monitoring'i etkinleştirin.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Hardware metrikleri (IoT cihazları)
  const renderHardwareMetrics = () => {
    const primaryStatus = status?.hardware || status?.snmp;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Cpu className="h-5 w-5 text-muted-foreground" />
                IoT/Hardware Metrikleri
              </CardTitle>
              <CardDescription>
                Donanım sensörleri ve IoT cihaz bilgileri
                {primaryStatus && (
                  <MonitorTimesDisplay
                    monitorData={{
                      ...primaryStatus,
                      deviceId: id,
                      type: device.monitors?.hardware?.enabled ? 'hardware' : 'snmp'
                    }}
                  />
                )}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleSettingsClick(device.monitors?.hardware?.enabled ? 'hardware' : 'snmp')}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {(device.monitors?.hardware?.enabled || device.monitors?.snmp?.enabled) && primaryStatus?.status === 'up' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">Güç Durumu</CardTitle>
                    <Battery className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-4 text-muted-foreground">
                    Güç bilgileri yakında eklenecek
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">Sıcaklık</CardTitle>
                    <Thermometer className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-4 text-muted-foreground">
                    Sıcaklık bilgileri yakında eklenecek
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">Sensörler</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-4 text-muted-foreground">
                    Sensör bilgileri yakında eklenecek
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Cpu className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Hardware Metrikleri Mevcut Değil</h3>
              <p className="text-muted-foreground max-w-md">
                Bu cihaz için hardware monitoring etkinleştirilmemiş.
                Metrikleri görüntülemek için hardware monitoring'i etkinleştirin.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Container metrikleri
  const renderContainerMetrics = () => {
    const primaryStatus = status?.docker;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MonitorTypeIcon type="docker" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                Container Metrikleri
              </CardTitle>
              <CardDescription>
                Docker container performans bilgileri
                {primaryStatus && (
                  <MonitorTimesDisplay
                    monitorData={{
                      ...primaryStatus,
                      deviceId: id,
                      type: 'docker'
                    }}
                  />
                )}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleSettingsClick('docker')}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {device.monitors?.docker?.enabled ? (
            <div className="text-center py-12 text-muted-foreground">
              Docker container metrikleri yakında eklenecek
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Package className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Container Metrikleri Mevcut Değil</h3>
              <p className="text-muted-foreground max-w-md">
                Bu cihaz için Docker monitoring etkinleştirilmemiş.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Web metrikleri
  const renderWebMetrics = () => {
    const primaryStatus = status?.http;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MonitorTypeIcon type="http" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                Web Servisi Metrikleri
              </CardTitle>
              <CardDescription>
                HTTP endpoint performans bilgileri
                {primaryStatus && (
                  <MonitorTimesDisplay
                    monitorData={{
                      ...primaryStatus,
                      deviceId: id,
                      type: 'http'
                    }}
                  />
                )}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleSettingsClick('http')}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {device.monitors?.http?.enabled ? (
            <div className="text-center py-12 text-muted-foreground">
              Web servisi metrikleri yakında eklenecek
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Globe className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Web Metrikleri Mevcut Değil</h3>
              <p className="text-muted-foreground max-w-md">
                Bu cihaz için HTTP monitoring etkinleştirilmemiş.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Varsayılan metrikler (Eski platform'lar için)
  const renderDefaultMetrics = () => {
    const primaryMonitor = device.monitors?.system?.enabled ? 'system' : 'snmp';
    const primaryStatus = status?.[primaryMonitor];

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MonitorTypeIcon type="system" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                Sistem Metrikleri
              </CardTitle>
              <CardDescription>
                Cihazın performans ve sistem bilgileri
                {primaryStatus && (
                  <MonitorTimesDisplay
                    monitorData={{
                      ...primaryStatus,
                      deviceId: id,
                      type: primaryMonitor
                    }}
                  />
                )}
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleSettingsClick(primaryMonitor)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <Server className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">Platform Tanımlanmamış</h3>
            <p className="text-muted-foreground max-w-md">
              Bu cihaz için platform bilgisi eksik. Cihazı düzenleyerek platform seçimi yapın.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Grup için aktif izleyici sayısını hesapla
  const getActiveMonitorCount = (groupMonitors) => {
    if (!device?.monitors) return { active: 0, total: groupMonitors.length };

    const active = groupMonitors.filter(monitor => device.monitors[monitor]?.enabled).length;
    return { active, total: groupMonitors.length };
  };

  // Cihazı düzenle
  const [editDialog, setEditDialog] = useState({
    open: false
  });

  const handleEditClick = () => {
    setEditDialog({
      open: true
    });
  };

  // Cihazı sil
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    deviceId: null,
    deviceName: ''
  });

  const handleDeleteClick = () => {
    setDeleteDialog({
      open: true,
      deviceId: id,
      deviceName: device?.name || 'Cihaz'
    });
  };

  const handleDelete = async () => {
    try {
      await deviceService.delete(id);
      setDeleteDialog({ open: false, deviceId: null, deviceName: '' });

      // Başarılı silme bildirimi göster
      notificationService.success(`${device?.name || 'Cihaz'} silindi`, {
        description: 'Cihaz başarıyla silindi.'
      });

      navigate('/devices');
    } catch (err) {
      console.error('Error deleting device:', err);
      setError('Cihaz silinirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('Cihaz silinemedi', {
        description: err.response?.data?.message || 'Cihaz silinirken bir hata oluştu.'
      });
    }
  };

  // Durum badge'i
  const StatusBadge = ({ status, reason }) => {
    // Durum tooltip'i için kullanılacak açıklama
    const tooltipContent = reason || getDefaultReasonForStatus(status);

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span>
              <StatusIndicator status={status} type="badge" size="sm" />
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipContent}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  // Smart loading - sayfa seviyesi
  if (pageLoading.shouldShowFullPageLoading()) {
    return (
      <SmartPageLoading
        loadingType="initial"
        pageTitle="Cihaz Detayları"
        skeletonLayout="detail"
      />
    );
  }

  if (error || devicesError) {
    return (
      <div className="p-6 max-w-2xl mx-auto mt-10">
        <div className="bg-destructive/15 text-destructive p-6 rounded-lg mb-6 border border-destructive/30 shadow-sm">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-lg mb-2">Veri Yükleme Hatası</h3>
              <p>
                {typeof (error || devicesError) === 'string'
                  ? (error || devicesError)
                  : typeof (error || devicesError) === 'object'
                  ? (error || devicesError)?.message || JSON.stringify(error || devicesError)
                  : 'Bilinmeyen hata oluştu'}
              </p>
            </div>
          </div>
        </div>
        <Button onClick={() => navigate('/devices')} className="mr-2">
          <ArrowLeft className="mr-2 h-4 w-4" /> Cihazlara Dön
        </Button>
        <Button onClick={loadData}>
          <RefreshCw className="mr-2 h-4 w-4" /> Yeniden Dene
        </Button>
      </div>
    );
  }

  if (!device && !loading && !devicesLoading) {
    return (
      <div className="p-6 max-w-2xl mx-auto mt-10">
        <div className="bg-destructive/15 text-destructive p-6 rounded-lg mb-6 border border-destructive/30 shadow-sm">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-lg mb-2">Cihaz Bulunamadı</h3>
              <p>İstenen cihaz bulunamadı veya silinmiş olabilir.</p>
            </div>
          </div>
        </div>
        <Button onClick={() => navigate('/devices')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Cihazlara Dön
        </Button>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      {/* Başlık */}
      <div className="space-y-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">{device?.name}</h1>
            {/* Durum rozeti kaldırıldı */}
          </div>
          <div className="flex items-center gap-2 self-end sm:self-auto">
            <Button variant="outline" size="sm" onClick={handleCheck} disabled={isDeviceChecking(id)}>
              {isDeviceChecking(id) ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Kontrol Ediliyor...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" /> Kontrol Et
                </>
              )}
            </Button>
            <Button variant="outline" size="sm" onClick={handleEditClick}>
              <Edit className="mr-2 h-4 w-4" /> Düzenle
            </Button>
            <Button variant="destructive" size="sm" onClick={handleDeleteClick}>
              <Trash2 className="mr-2 h-4 w-4" /> Sil
            </Button>
          </div>
        </div>
      </div>

      {/* Ana İçerik - İki Sütunlu Düzen */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sol Sütun - Cihaz Bilgileri ve Özet */}
        <div className="space-y-6 lg:col-span-1">
          {/* Cihaz Bilgileri Kartı */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5 text-muted-foreground" />
                Cihaz Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Host</h3>
                <div className="flex items-center gap-2">
                  <p className="font-mono">{device?.host}</p>
                  <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => window.open(`http://${device?.host}`, '_blank')}>
                    <ExternalLink className="h-3.5 w-3.5 text-muted-foreground" />
                  </Button>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Kategori</h3>
                <div className="flex items-center gap-2">
                  {device?.group ? (
                    <Badge variant="outline" className="font-normal flex items-center gap-2 py-1.5 px-3">
                      {device.group.includes('/') ? (
                        <>
                          {/* Sadece alt kategoriyi göster */}
                          {(() => {
                            const [, subCategory] = device.group.split('/');

                            // Alt kategoriye göre ikon seç
                            const iconName = getSubCategoryIcon(subCategory);
                            let IconComponent;

                            // İkon bileşenini seç
                            switch (iconName) {
                              case 'Router': IconComponent = Router; break;
                              case 'Share2': IconComponent = Share2; break;
                              case 'Shield': IconComponent = Shield; break;
                              case 'Wifi': IconComponent = Wifi; break;
                              case 'Radio': IconComponent = Radio; break;
                              case 'Server': IconComponent = Server; break;
                              case 'Cloud': IconComponent = Cloud; break;
                              case 'Package': IconComponent = Package; break;
                              case 'Database': IconComponent = DatabaseIcon; break;
                              case 'HardDrive': IconComponent = HardDrive; break;
                              case 'Globe2': IconComponent = Globe2; break;
                              case 'Webhook': IconComponent = Webhook; break;
                              case 'Mail': IconComponent = Mail; break;
                              case 'Network': IconComponent = Network; break;
                              case 'Search': IconComponent = Search; break;
                              case 'Activity': IconComponent = Activity; break;
                              case 'Cog': IconComponent = Cog; break;
                              case 'Building2': IconComponent = Building2; break;
                              case 'Battery': IconComponent = Battery; break;
                              default: IconComponent = Tag; break;
                            }

                            return (
                              <div className="flex items-center font-medium">
                                <IconComponent className="h-4 w-4 mr-2" />
                                {getFormattedSubCategory(device.group)}
                              </div>
                            );
                          })()}
                        </>
                      ) : (
                        <>
                          <Tag className="h-4 w-4 mr-2" />
                          {device.group}
                        </>
                      )}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="font-normal">
                      <Tag className="h-4 w-4 mr-2" />
                      Varsayılan
                    </Badge>
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Açıklama</h3>
                <p className="text-sm">{device?.description || '-'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Konum</h3>
                <p className="text-sm">{device?.location || '-'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Durum Özeti Kartı */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-muted-foreground" />
                Durum Özeti
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 rounded-lg border bg-card">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Durum</h3>
                    <p className="text-sm text-muted-foreground">
                      {status && status.reason ?
                        status.reason :
                        status && status.calculatedStatus ?
                          getDefaultReasonForStatus(status.calculatedStatus) :
                          'Durum bilgisi bulunamadı'}
                    </p>
                  </div>
                  {status && status.calculatedStatus ? (
                    <StatusBadge status={status.calculatedStatus} reason={status.reason} />
                  ) : (
                    <Badge variant="outline">Veri Yok</Badge>
                  )}
                </div>

              </div>

              {/* İzleyici Ayarları */}
              <div className="space-y-4">
                {Object.entries(monitorGroups).map(([groupKey, group]) => {
                  const { active, total } = getActiveMonitorCount(group.monitors);

                  return (
                    <Card key={groupKey}>
                      <CardHeader className="py-4 border-b border-border">
                        <CardTitle className="text-base flex items-center gap-2">
                          {group.icon && (
                            typeof group.icon === 'string' ? (
                              <MonitorTypeIcon
                                type={group.icon}
                                size="md"
                                useInheritedColor={true}
                                showTooltip={false}
                                className="text-muted-foreground"
                              />
                            ) : (
                              <group.icon className="h-4 w-4 text-muted-foreground" />
                            )
                          )}
                          <span>{group.title}</span>
                          <Badge variant="outline" className="text-xs ml-auto">
                            {active}/{total}
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <div className="space-y-3 pl-2">
                          {group.monitors.map((monitorType) => {
                            // Sistem monitoring için platform kontrolü
                            const needsCredentials = monitorType === 'system' && (() => {
                              const currentSettings = device?.monitors?.system || {};
                              const platform = currentSettings.platform || device?.platform || 'linux';

                              if (platform === 'windows') {
                                return !currentSettings.username || !currentSettings.password;
                              } else if (platform === 'linux') {
                                return !currentSettings.username || (!currentSettings.password && !currentSettings.privateKey);
                              }
                              return false; // Local için kimlik bilgisi gerekmez
                            })();

                            return (
                              <div key={monitorType} className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <MonitorTypeIcon
                                    type={monitorType}
                                    size="md"
                                    active={monitorType === 'icmp' ? true : device?.monitors?.[monitorType]?.enabled || false}
                                  />
                                  <div>
                                    <span className="text-sm">{monitorTitles[monitorType]}</span>
                                    {needsCredentials && (
                                      <div className="text-xs text-warning flex items-center gap-1">
                                        <AlertCircle className="h-3 w-3" />
                                        Kimlik bilgileri gerekli
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <Switch
                                  checked={monitorType === 'icmp' ? true : device?.monitors?.[monitorType]?.enabled || false}
                                  disabled={monitorType === 'icmp'}
                                  onCheckedChange={(checked) => toggleMonitor(monitorType, checked)}
                                />
                              </div>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sağ Sütun - Detaylı Bilgiler */}
        <div className="lg:col-span-2">
          {/* Sekmeler */}
          <TabsComponent defaultValue={activeTab} onValueChange={(value) => {
            console.log("Tab değişti:", value);
            setActiveTab(value);
            if (value === "history") {
              loadHistoryData();
            }
          }}>
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="status" className="flex items-center gap-2">
                <Activity className="h-4 w-4" /> Durum
              </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center gap-2">
                <History className="h-4 w-4" /> Geçmiş
              </TabsTrigger>
            </TabsList>

            {/* Durum Sekmesi */}
            <TabsContent value="status">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-muted-foreground" />
                    Detaylı Durum Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* ICMP Durumu */}
                    <Card className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base flex items-center gap-2">
                            <MonitorTypeIcon type="icmp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                            ICMP Ping
                          </CardTitle>
                          <div className="flex items-center gap-2">
                            {status && status.icmp ? (
                              <StatusBadge status={status.icmp.status} />
                            ) : (
                              <Badge variant="outline">Veri Yok</Badge>
                            )}
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSettingsClick('icmp')}
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          Erişilebilirlik durumu
                          {status && status.icmp && (
                            <MonitorTimesDisplay
                              monitorData={{
                                ...status.icmp,
                                deviceId: id,
                                type: 'icmp'
                              }}
                            />
                          )}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {status && status.icmp && status.icmp.status === 'up' ? (
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Yanıt Süresi</span>
                              <span className="font-mono text-lg font-bold">{status.icmp.responseTime} ms</span>
                            </div>
                            <Progress
                              value={Math.min(100, (status.icmp.responseTime / 200) * 100)}
                              className="h-2"
                              indicatorClassName={status.icmp.responseTime > 150 ? "bg-destructive" :
                                                status.icmp.responseTime > 80 ? "bg-warning" : "bg-success"}
                            />
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>İyi (&lt;80ms)</span>
                              <span>Orta (&lt;150ms)</span>
                              <span>Yavaş (&gt;150ms)</span>
                            </div>

                            {status.icmp.packetLoss !== undefined && (
                              <div className="mt-4 pt-4 border-t">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Paket Kaybı</span>
                                  <span className="font-mono">{status.icmp.packetLoss}%</span>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : status && status.icmp && status.icmp.status !== 'up' ? (
                          <div className="py-4 text-center">
                            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                            <p className="text-sm font-medium">Cihaza erişilemiyor</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              Son kontrol: {status.icmp.lastCheck ? new Date(parseInt(status.icmp.lastCheck)).toLocaleString() : '-'}
                            </p>
                          </div>
                        ) : (
                          <div className="py-4 text-center">
                            <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                            <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* SNMP kartı kaldırıldı - Metrikler sekmesinde gösteriliyor */}

                    {/* DNS Durumu */}
                    {device?.monitors?.dns?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="dns" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              DNS Sorgusu
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.dns ? (
                                <StatusBadge status={status.dns.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('dns')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            DNS sorgu durumu
                            {status && status.dns && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.dns,
                                  deviceId: id,
                                  type: 'dns'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.dns && status.dns.status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Yanıt Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.dns.responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.dns.responseTime / 200) * 100)}
                                className="h-2"
                                indicatorClassName={status.dns.responseTime > 150 ? "bg-destructive" :
                                                  status.dns.responseTime > 80 ? "bg-warning" : "bg-success"}
                              />

                              {status.dns.details && (
                                <div className="mt-4 pt-4 border-t">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Çözümlenen IP</span>
                                    <span className="font-mono">
                                      {status.dns.details.answers &&
                                       Array.isArray(status.dns.details.answers) && status.dns.details.answers.length > 0
                                        ? status.dns.details.answers.map(a => a.data).join(', ')
                                        : status.dns.details.records && Array.isArray(status.dns.details.records)
                                          ? status.dns.details.records.join(', ')
                                          : status.dns.details.resolvedIp || '-'}
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : status && status.dns && status.dns.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">DNS sorgusu başarısız</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.dns.lastCheck ? new Date(parseInt(status.dns.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* SSL Durumu */}
                    {device?.monitors?.ssl?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="ssl" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              SSL Sertifikası
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.ssl ? (
                                <StatusBadge status={status.ssl.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('ssl')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            Sertifika durumu
                            {status && status.ssl && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.ssl,
                                  deviceId: id,
                                  type: 'ssl'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.ssl && status.ssl.status === 'up' && status.ssl.details ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Geçerlilik</span>
                                <span className="font-mono text-lg font-bold">
                                  {status.ssl.details.daysRemaining > 0 ?
                                    `${status.ssl.details.daysRemaining} gün kaldı` :
                                    'Süresi dolmuş'}
                                </span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.ssl.details.daysRemaining / 90) * 100)}
                                className="h-2"
                                indicatorClassName={status.ssl.details.daysRemaining < 7 ? "bg-destructive" :
                                                  status.ssl.details.daysRemaining < 30 ? "bg-warning" : "bg-success"}
                              />

                              <div className="mt-4 pt-4 border-t space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Geçerlilik Başlangıcı</span>
                                  <span className="font-mono text-sm">{status.ssl.details.validFrom ? new Date(status.ssl.details.validFrom).toLocaleDateString() : '-'}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Geçerlilik Bitişi</span>
                                  <span className="font-mono text-sm">{status.ssl.details.validTo ? new Date(status.ssl.details.validTo).toLocaleDateString() : '-'}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Durum</span>
                                  <span className="font-mono text-sm">
                                    {status.ssl.details.expiryStatus === 'expired' ? 'Süresi Dolmuş' :
                                     status.ssl.details.expiryStatus === 'critical' ? 'Kritik (< 7 gün)' :
                                     status.ssl.details.expiryStatus === 'warning' ? 'Uyarı (< 30 gün)' : 'Geçerli'}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ) : status && status.ssl && status.ssl.status !== 'up' ? (
                            <div className="space-y-4">
                              <div className="py-4 text-center">
                                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                                <p className="text-sm font-medium">SSL sertifikası geçersiz</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Son kontrol: {status.ssl.lastCheck ? new Date(parseInt(status.ssl.lastCheck)).toLocaleString() : '-'}
                                </p>
                              </div>

                              {/* Geçersiz olsa bile sertifika bilgilerini göster */}
                              {status.ssl.details && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  {status.ssl.details.validFrom && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Geçerlilik Başlangıcı</span>
                                      <span className="font-mono text-sm">{new Date(status.ssl.details.validFrom).toLocaleDateString()}</span>
                                    </div>
                                  )}

                                  {status.ssl.details.validTo && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Geçerlilik Bitişi</span>
                                      <span className="font-mono text-sm">{new Date(status.ssl.details.validTo).toLocaleDateString()}</span>
                                    </div>
                                  )}

                                  {status.ssl.details.expiryStatus && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Durum</span>
                                      <span className="font-mono text-sm">
                                        {status.ssl.details.expiryStatus === 'expired' ? 'Süresi Dolmuş' :
                                         status.ssl.details.expiryStatus === 'critical' ? 'Kritik (< 7 gün)' :
                                         status.ssl.details.expiryStatus === 'warning' ? 'Uyarı (< 30 gün)' : 'Geçerli'}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* TCP Port Durumu */}
                    {device?.monitors?.tcp?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="tcp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              TCP Port
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.tcp && Object.keys(status.tcp).length > 0 && Object.values(status.tcp)[0] ? (
                                <StatusBadge status={Object.values(status.tcp)[0].status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('tcp')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            Port durumu
                            {status && status.tcp && Object.keys(status.tcp).length > 0 && Object.values(status.tcp)[0] && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...Object.values(status.tcp)[0],
                                  deviceId: id,
                                  type: 'tcp'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.tcp && Object.keys(status.tcp).length > 0 && Object.values(status.tcp)[0] && Object.values(status.tcp)[0].status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Bağlantı Süresi</span>
                                <span className="font-mono text-lg font-bold">{Object.values(status.tcp)[0].responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (Object.values(status.tcp)[0].responseTime / 200) * 100)}
                                className="h-2"
                                indicatorClassName={Object.values(status.tcp)[0].responseTime > 150 ? "bg-destructive" :
                                                  Object.values(status.tcp)[0].responseTime > 80 ? "bg-warning" : "bg-success"}
                              />

                              <div className="mt-4 pt-4 border-t space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Port</span>
                                  <span className="font-mono text-sm">{Object.keys(status.tcp)[0]}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Durum</span>
                                  <span className="font-mono text-sm">Açık</span>
                                </div>
                              </div>
                            </div>
                          ) : status && status.tcp && Object.keys(status.tcp).length > 0 && Object.values(status.tcp)[0] && Object.values(status.tcp)[0].status !== 'up' ? (
                            <div className="space-y-4">
                              <div className="py-4 text-center">
                                <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                                <p className="text-sm font-medium">Port kapalı veya erişilemiyor</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Son kontrol: {Object.values(status.tcp)[0].lastCheck ? new Date(parseInt(Object.values(status.tcp)[0].lastCheck)).toLocaleString() : '-'}
                                </p>
                              </div>

                              {/* Hata durumunda da port bilgilerini göster */}
                              <div className="mt-4 pt-4 border-t space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Port</span>
                                  <span className="font-mono text-sm">{Object.keys(status.tcp)[0]}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Durum</span>
                                  <span className="font-mono text-sm">Kapalı</span>
                                </div>
                                {Object.values(status.tcp)[0].error && (
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Hata</span>
                                    <span className="font-mono text-sm text-destructive">
                                      {typeof Object.values(status.tcp)[0].error === 'string'
                                        ? Object.values(status.tcp)[0].error
                                        : typeof Object.values(status.tcp)[0].error === 'object'
                                        ? Object.values(status.tcp)[0].error.message || JSON.stringify(Object.values(status.tcp)[0].error)
                                        : 'Bilinmeyen hata'}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* HTTP Durumu */}
                    {device.monitors?.http?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="http" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              HTTP/HTTPS
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.http ? (
                                <StatusBadge status={status.http.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('http')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            Web servis durumu
                            {status && status.http && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.http,
                                  deviceId: id,
                                  type: 'http'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.http ? (
                            <div className="space-y-4">
                              {/* Yanıt Süresi */}
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Yanıt Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.http.responseTime || 0} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, ((status.http.responseTime || 0) / 1000) * 100)}
                                className="h-2"
                                indicatorClassName={(status.http.responseTime || 0) > 500 ? "bg-destructive" :
                                                  (status.http.responseTime || 0) > 200 ? "bg-warning" : "bg-success"}
                              />

                              {/* Durum Bilgisi */}
                              <div className="mt-4 pt-4 border-t space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Durum</span>
                                  <span className="font-mono text-sm font-bold">
                                    {status.http.details && status.http.details.statusCode && status.http.details.statusCode !== 0 ?
                                      `${status.http.details.statusCode} (${status.http.message || status.http.details.statusText || 'Bilinmiyor'})`
                                      : `Bilinmiyor (${status.http.message || 'Bağlantı hatası'})`}
                                  </span>
                                </div>



                                {/* HTTP 400 için özel öneri */}
                                {status.http.details && status.http.details.statusCode === 400 && (
                                  <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                                    <p className="text-xs text-blue-800">
                                      💡 <strong>Öneri:</strong> HTTP yerine HTTPS deneyin: <code className="bg-blue-100 px-1 rounded">https://</code>
                                    </p>
                                  </div>
                                )}

                                {/* HTTP 404 için özel öneri */}
                                {status.http.details && status.http.details.statusCode === 404 && (
                                  <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                                    <p className="text-xs text-blue-800">
                                      💡 <strong>Öneri:</strong> URL'yi kontrol edin veya farklı bir path deneyin
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">HTTP izleme verisi bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Veritabanı Durumu */}
                    {device.monitors?.database?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="database" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              Veritabanı
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.database ? (
                                <StatusBadge status={status.database.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('database')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            {device.monitors.database.type === 'mysql' ? 'MySQL' :
                             device.monitors.database.type === 'postgresql' ? 'PostgreSQL' :
                             device.monitors.database.type === 'mongodb' ? 'MongoDB' : 'Veritabanı'}
                            {status && status.database && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.database,
                                  deviceId: id,
                                  type: 'database'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.database && status.database.status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Bağlantı Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.database.responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.database.responseTime / 500) * 100)}
                                className="h-2"
                                indicatorClassName={status.database.responseTime > 300 ? "bg-destructive" :
                                                  status.database.responseTime > 150 ? "bg-warning" : "bg-success"}
                              />
                            </div>
                          ) : status && status.database && status.database.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">Veritabanı bağlantısı başarısız</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.database.lastCheck ? new Date(parseInt(status.database.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* API Durumu */}
                    {device.monitors?.api?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="api" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              REST API
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.api ? (
                                <StatusBadge status={status.api.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('api')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            REST API endpoint durumu
                            {status && status.api && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.api,
                                  deviceId: id,
                                  type: 'api'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.api && status.api.status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Yanıt Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.api.responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.api.responseTime / 1000) * 100)}
                                className="h-2"
                                indicatorClassName={status.api.responseTime > 500 ? "bg-destructive" :
                                                  status.api.responseTime > 200 ? "bg-warning" : "bg-success"}
                              />

                              {status.api.details && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Durum Kodu</span>
                                    <span className="font-mono text-sm font-bold">
                                      {status.api.details.statusCode ?
                                        `${status.api.details.statusCode} - ${status.api.details.statusText || 'Bilinmiyor'}`
                                        : '-'}
                                    </span>
                                  </div>
                                  {/* Durum kodu açıklaması */}
                                  <p className="text-xs text-muted-foreground">
                                    {status.api.details.statusCode ?
                                      getHttpStatusDescription(status.api.details.statusCode) :
                                      'Durum kodu bilgisi bulunamadı'}
                                  </p>
                                </div>
                              )}
                            </div>
                          ) : status && status.api && status.api.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">API yanıt vermiyor</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.api.lastCheck ? new Date(parseInt(status.api.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* SMTP/Email Durumu */}
                    {device?.monitors?.smtp?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="smtp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              SMTP E-posta
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.smtp ? (
                                <StatusBadge status={status.smtp.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('smtp')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            SMTP e-posta sunucu durumu
                            {status && status.smtp && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.smtp,
                                  deviceId: id,
                                  type: 'smtp'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.smtp && status.smtp.status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Bağlantı Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.smtp.responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.smtp.responseTime / 1000) * 100)}
                                className="h-2"
                                indicatorClassName={status.smtp.responseTime > 500 ? "bg-destructive" :
                                                  status.smtp.responseTime > 200 ? "bg-warning" : "bg-success"}
                              />
                              <div className="mt-4 pt-4 border-t space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">SMTP Sunucu</span>
                                  <span className="font-mono text-sm">{device.monitors.smtp.host || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">Port</span>
                                  <span className="font-mono text-sm">{device.monitors.smtp.port || '-'}</span>
                                </div>
                              </div>
                            </div>
                          ) : status && status.smtp && status.smtp.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">SMTP sunucuya bağlanılamıyor</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.smtp.lastCheck ? new Date(parseInt(status.smtp.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* System Monitoring Durumu */}
                    {device?.monitors?.system?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="system" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              Sistem Monitoring
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.system ? (
                                <StatusBadge status={status.system.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('system')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            Sistem performans metrikleri
                            {status && status.system && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.system,
                                  deviceId: id,
                                  type: 'system'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.system && status.system.status === 'up' && status.system.details ? (
                            <div className="space-y-4">
                              {/* CPU Kullanımı */}
                              {status.system.details.cpu !== undefined && (
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">CPU Kullanımı</span>
                                    <span className="font-mono text-lg font-bold">{status.system.details.cpu}%</span>
                                  </div>
                                  <Progress
                                    value={status.system.details.cpu}
                                    className="h-2"
                                    indicatorClassName={status.system.details.cpu > 80 ? "bg-destructive" :
                                                      status.system.details.cpu > 60 ? "bg-warning" : "bg-success"}
                                  />
                                </div>
                              )}

                              {/* RAM Kullanımı */}
                              {status.system.details.memory !== undefined && (
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">RAM Kullanımı</span>
                                    <span className="font-mono text-lg font-bold">{status.system.details.memory}%</span>
                                  </div>
                                  <Progress
                                    value={status.system.details.memory}
                                    className="h-2"
                                    indicatorClassName={status.system.details.memory > 85 ? "bg-destructive" :
                                                      status.system.details.memory > 70 ? "bg-warning" : "bg-success"}
                                  />
                                </div>
                              )}

                              {/* Disk Kullanımı */}
                              {status.system.details.disk !== undefined && (
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Disk Kullanımı</span>
                                    <span className="font-mono text-lg font-bold">{status.system.details.disk}%</span>
                                  </div>
                                  <Progress
                                    value={status.system.details.disk}
                                    className="h-2"
                                    indicatorClassName={status.system.details.disk > 90 ? "bg-destructive" :
                                                      status.system.details.disk > 75 ? "bg-warning" : "bg-success"}
                                  />
                                </div>
                              )}

                              {/* Ek Sistem Bilgileri */}
                              {(status.system.details.uptime || status.system.details.platform || status.system.details.hostname) && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  {status.system.details.hostname && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Hostname</span>
                                      <span className="font-mono text-sm">{status.system.details.hostname}</span>
                                    </div>
                                  )}
                                  {status.system.details.platform && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Platform</span>
                                      <span className="font-mono text-sm">{status.system.details.platform}</span>
                                    </div>
                                  )}
                                  {status.system.details.uptime && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Uptime</span>
                                      <span className="font-mono text-sm">{Math.floor(status.system.details.uptime / 3600)} saat</span>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          ) : status && status.system && status.system.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">Sistem metriklerine erişilemiyor</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.system.lastCheck ? new Date(parseInt(status.system.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* SNMP Monitoring Durumu */}
                    {device?.monitors?.snmp?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="snmp" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              SNMP Monitoring
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.snmp ? (
                                <StatusBadge status={status.snmp.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('snmp')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            SNMP ağ cihazı metrikleri
                            {status && status.snmp && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.snmp,
                                  deviceId: id,
                                  type: 'snmp'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.snmp && status.snmp.status === 'up' && status.snmp.details ? (
                            <div className="space-y-4">
                              {/* CPU Kullanımı (SNMP) */}
                              {status.snmp.details.cpu !== undefined && (
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">CPU Kullanımı</span>
                                    <span className="font-mono text-lg font-bold">{status.snmp.details.cpu}%</span>
                                  </div>
                                  <Progress
                                    value={status.snmp.details.cpu}
                                    className="h-2"
                                    indicatorClassName={status.snmp.details.cpu > 80 ? "bg-destructive" :
                                                      status.snmp.details.cpu > 60 ? "bg-warning" : "bg-success"}
                                  />
                                </div>
                              )}

                              {/* Memory Kullanımı (SNMP) */}
                              {status.snmp.details.memory !== undefined && (
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Memory Kullanımı</span>
                                    <span className="font-mono text-lg font-bold">{status.snmp.details.memory}%</span>
                                  </div>
                                  <Progress
                                    value={status.snmp.details.memory}
                                    className="h-2"
                                    indicatorClassName={status.snmp.details.memory > 85 ? "bg-destructive" :
                                                      status.snmp.details.memory > 70 ? "bg-warning" : "bg-success"}
                                  />
                                </div>
                              )}

                              {/* Ağ Trafiği */}
                              {(status.snmp.details.interfaceTraffic || status.snmp.details.interfaces) && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  <div className="text-sm font-medium mb-2">Ağ Arayüzleri</div>
                                  {status.snmp.details.interfaces && Array.isArray(status.snmp.details.interfaces) ? (
                                    status.snmp.details.interfaces.slice(0, 3).map((iface, index) => (
                                      <div key={index} className="flex justify-between items-center">
                                        <span className="text-sm">{iface.name || `Interface ${index + 1}`}</span>
                                        <span className="font-mono text-sm">
                                          {iface.status === 'up' ? '✅ Aktif' : '❌ Pasif'}
                                        </span>
                                      </div>
                                    ))
                                  ) : (
                                    <div className="text-sm text-muted-foreground">Arayüz bilgisi bulunamadı</div>
                                  )}
                                </div>
                              )}

                              {/* Sistem Bilgileri */}
                              {(status.snmp.details.sysDescr || status.snmp.details.sysUpTime || status.snmp.details.sysName) && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  {status.snmp.details.sysName && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Sistem Adı</span>
                                      <span className="font-mono text-sm">{status.snmp.details.sysName}</span>
                                    </div>
                                  )}
                                  {status.snmp.details.sysUpTime && (
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm font-medium">Uptime</span>
                                      <span className="font-mono text-sm">{Math.floor(status.snmp.details.sysUpTime / 8640000)} gün</span>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          ) : status && status.snmp && status.snmp.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">SNMP'ye erişilemiyor</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.snmp.lastCheck ? new Date(parseInt(status.snmp.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Hardware (IPMI) Durumu */}
                    {device?.monitors?.ipmi?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="ipmi" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              IPMI Donanım
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.ipmi ? (
                                <StatusBadge status={status.ipmi.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('ipmi')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            IPMI donanım durumu
                            {status && status.ipmi && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.ipmi,
                                  deviceId: id,
                                  type: 'ipmi'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.ipmi && status.ipmi.status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Bağlantı Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.ipmi.responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.ipmi.responseTime / 3000) * 100)}
                                className="h-2"
                                indicatorClassName={status.ipmi.responseTime > 2000 ? "bg-destructive" :
                                                  status.ipmi.responseTime > 1000 ? "bg-warning" : "bg-success"}
                              />
                              {status.ipmi.details && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Sıcaklık</span>
                                    <span className="font-mono text-sm">{status.ipmi.details.temperature || '-'}°C</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Fan Durumu</span>
                                    <span className="font-mono text-sm">{status.ipmi.details.fanStatus || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Güç Durumu</span>
                                    <span className="font-mono text-sm">{status.ipmi.details.powerStatus || '-'}</span>
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : status && status.ipmi && status.ipmi.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">IPMI/BMC'ye bağlanılamıyor</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.ipmi.lastCheck ? new Date(parseInt(status.ipmi.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Container (Docker) Durumu */}
                    {device?.monitors?.docker?.enabled && (
                      <Card className="border shadow-sm">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base flex items-center gap-2">
                              <MonitorTypeIcon type="docker" size="md" useInheritedColor={true} showTooltip={false} className="text-muted-foreground" />
                              Docker Konteyner
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              {status && status.docker ? (
                                <StatusBadge status={status.docker.status} />
                              ) : (
                                <Badge variant="outline">Veri Yok</Badge>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleSettingsClick('docker')}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            Docker konteyner durumu
                            {status && status.docker && (
                              <MonitorTimesDisplay
                                monitorData={{
                                  ...status.docker,
                                  deviceId: id,
                                  type: 'docker'
                                }}
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          {status && status.docker && status.docker.status === 'up' ? (
                            <div className="space-y-4">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Bağlantı Süresi</span>
                                <span className="font-mono text-lg font-bold">{status.docker.responseTime} ms</span>
                              </div>
                              <Progress
                                value={Math.min(100, (status.docker.responseTime / 1000) * 100)}
                                className="h-2"
                                indicatorClassName={status.docker.responseTime > 500 ? "bg-destructive" :
                                                  status.docker.responseTime > 200 ? "bg-warning" : "bg-success"}
                              />
                              {status.docker.details && (
                                <div className="mt-4 pt-4 border-t space-y-2">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Çalışan Container</span>
                                    <span className="font-mono text-sm">{status.docker.details.runningContainers || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Toplam Container</span>
                                    <span className="font-mono text-sm">{status.docker.details.totalContainers || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">Docker Version</span>
                                    <span className="font-mono text-sm">
                                      {typeof status.docker.details.version === 'string'
                                        ? status.docker.details.version
                                        : typeof status.docker.details.version === 'object' && status.docker.details.version?.version
                                        ? status.docker.details.version.version
                                        : '-'}
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          ) : status && status.docker && status.docker.status !== 'up' ? (
                            <div className="py-4 text-center">
                              <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2 opacity-80" />
                              <p className="text-sm font-medium">Docker API'ye bağlanılamıyor</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                Son kontrol: {status.docker.lastCheck ? new Date(parseInt(status.docker.lastCheck)).toLocaleString() : '-'}
                              </p>
                            </div>
                          ) : (
                            <div className="py-4 text-center">
                              <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
                              <p className="text-sm text-muted-foreground">Veri bulunamadı</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>



            {/* Geçmiş Sekmesi */}
            <TabsContent value="history">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-muted-foreground" />
                      İzleme Geçmişi
                    </CardTitle>
                    <CardDescription>
                      Ayarlarda belirtilen saklama sürelerine göre tüm izleme türlerinin geçmişi
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {console.log("Geçmiş sekmesi içeriği render ediliyor")}
                    {(() => {
                      // İzleme türleri için MonitorTypeIcon bileşenini kullan
                      const getMonitorIcon = (type) => {
                        return <MonitorTypeIcon type={type} size="md" useInheritedColor={true} showTooltip={false} />;
                      };

                      // Tüm geçmiş verilerini birleştir
                      const allHistory = [];

                      // ICMP geçmişi
                      if (icmpHistory && Array.isArray(icmpHistory) && icmpHistory.length > 0) {
                        console.log('Processing ICMP history for display:', icmpHistory.length, 'records');
                        icmpHistory.forEach(record => {
                          if (record && typeof record === 'object') {
                            allHistory.push({
                              ...record,
                              type: 'icmp',
                              typeLabel: 'ICMP',
                              icon: getMonitorIcon('icmp'),
                              statusLabel: record.status === 'up' ? 'Çevrimiçi' : 'Çevrimdışı'
                            });
                          } else {
                            console.error('Invalid ICMP history record:', record);
                          }
                        });
                      } else {
                        console.log('No ICMP history to display');
                      }

                      // SNMP geçmişi
                      if (device.monitors?.snmp?.enabled && snmpHistory && Array.isArray(snmpHistory) && snmpHistory.length > 0) {
                        console.log('Processing SNMP history for display:', snmpHistory.length, 'records');
                        snmpHistory.forEach(record => {
                          if (record && typeof record === 'object') {
                            allHistory.push({
                              ...record,
                              type: 'snmp',
                              typeLabel: 'SNMP',
                              icon: getMonitorIcon('snmp'),
                              statusLabel: record.status === 'up' ? 'Aktif' : 'Devre Dışı'
                            });
                          } else {
                            console.error('Invalid SNMP history record:', record);
                          }
                        });
                      } else if (device.monitors?.snmp?.enabled) {
                        console.log('No SNMP history to display');
                      }

                      // DNS geçmişi
                      if (device.monitors?.dns?.enabled && dnsHistory && Array.isArray(dnsHistory) && dnsHistory.length > 0) {
                        console.log('Processing DNS history for display:', dnsHistory.length, 'records');
                        dnsHistory.forEach(record => {
                          if (record && typeof record === 'object') {
                            allHistory.push({
                              ...record,
                              type: 'dns',
                              typeLabel: 'DNS',
                              icon: getMonitorIcon('dns'),
                              statusLabel: record.status === 'up' ? 'Başarılı' : 'Başarısız'
                            });
                          } else {
                            console.error('Invalid DNS history record:', record);
                          }
                        });
                      } else if (device.monitors?.dns?.enabled) {
                        console.log('No DNS history to display');
                      }

                      // SSL geçmişi
                      if (device.monitors?.ssl?.enabled && sslHistory && Array.isArray(sslHistory) && sslHistory.length > 0) {
                        console.log('Processing SSL history for display:', sslHistory.length, 'records');
                        sslHistory.forEach(record => {
                          if (record && typeof record === 'object') {
                            allHistory.push({
                              ...record,
                              type: 'ssl',
                              typeLabel: 'SSL',
                              icon: getMonitorIcon('ssl'),
                              statusLabel: record.status === 'up' ? 'Geçerli' : 'Geçersiz'
                            });
                          } else {
                            console.error('Invalid SSL history record:', record);
                          }
                        });
                      } else if (device.monitors?.ssl?.enabled) {
                        console.log('No SSL history to display');
                      }

                      // Veritabanı geçmişi
                      if (device.monitors?.database?.enabled && databaseHistory && Array.isArray(databaseHistory) && databaseHistory.length > 0) {
                        console.log('Processing database history for display:', databaseHistory.length, 'records');
                        databaseHistory.forEach(record => {
                          if (record && typeof record === 'object') {
                            allHistory.push({
                              ...record,
                              type: 'database',
                              typeLabel: 'Veritabanı',
                              icon: getMonitorIcon('database'),
                              statusLabel: record.status === 'up' ? 'Bağlantı Başarılı' : 'Bağlantı Başarısız'
                            });
                          } else {
                            console.error('Invalid database history record:', record);
                          }
                        });
                      } else if (device.monitors?.database?.enabled) {
                        console.log('No database history to display');
                      }

                      // API geçmişi
                      if (device.monitors?.api?.enabled && apiHistory && Array.isArray(apiHistory) && apiHistory.length > 0) {
                        console.log('Processing API history for display:', apiHistory.length, 'records');
                        apiHistory.forEach(record => {
                          if (record && typeof record === 'object') {
                            allHistory.push({
                              ...record,
                              type: 'api',
                              typeLabel: 'API',
                              icon: getMonitorIcon('api'),
                              statusLabel: record.status === 'up' ? 'Başarılı' : 'Başarısız'
                            });
                          } else {
                            console.error('Invalid API history record:', record);
                          }
                        });
                      } else if (device.monitors?.api?.enabled) {
                        console.log('No API history to display');
                      }

                      // Zaman damgasına göre sırala (en yeniden en eskiye)
                      allHistory.sort((a, b) => parseInt(b.timestamp) - parseInt(a.timestamp));

                      if (allHistory.length === 0) {
                        return (
                          <div className="flex flex-col items-center justify-center py-12 text-center">
                            <Clock className="h-12 w-12 text-muted-foreground mb-4 opacity-50" />
                            <h3 className="text-lg font-medium mb-2">Geçmiş Veri Yok</h3>
                            <p className="text-muted-foreground">
                              Bu cihaz için henüz geçmiş veri bulunmuyor.
                            </p>
                          </div>
                        );
                      }

                      // Sayfalandırma için hesaplamalar
                      const indexOfLastItem = currentPage * itemsPerPage;
                      const indexOfFirstItem = indexOfLastItem - itemsPerPage;
                      const currentItems = allHistory.slice(indexOfFirstItem, indexOfLastItem);
                      const totalPages = Math.ceil(allHistory.length / itemsPerPage);

                      // Sayfa değiştirme fonksiyonu
                      const paginate = (pageNumber) => setCurrentPage(pageNumber);

                      // Sayfa başına öğe sayısını değiştirme fonksiyonu
                      const handlePageSizeChange = (newPageSize) => {
                        // Yeni sayfa boyutuna göre mevcut sayfayı ayarla
                        const newTotalPages = Math.ceil(allHistory.length / newPageSize);
                        const newCurrentPage = Math.min(currentPage, newTotalPages);

                        setItemsPerPage(newPageSize);
                        setCurrentPage(newCurrentPage);
                      };

                      return (
                        <div className="space-y-4">
                          {/* Geçmiş kayıtları */}
                          {currentItems.map((record, index) => (
                            <div key={index} className="flex items-start border-b pb-3 last:border-0">
                              <div className={`h-3 w-3 rounded-full mt-1.5 mr-3 flex-shrink-0 ${record.status === 'up' ? 'bg-success' : 'bg-destructive'}`}></div>
                              <div className="flex-grow">
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="flex items-center gap-1">
                                      {record.icon}
                                      <span>{record.typeLabel}</span>
                                    </Badge>
                                    <span className="text-sm font-medium">
                                      {record.statusLabel}
                                    </span>
                                  </div>
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(parseInt(record.timestamp)).toLocaleString()}
                                  </span>
                                </div>

                                {/* İzleme türüne göre özel detaylar */}
                                <div className="mt-1 text-xs text-muted-foreground">
                                  {record.type === 'icmp' && record.status === 'up' && record.responseTime && (
                                    <div>Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span></div>
                                  )}

                                  {record.type === 'snmp' && record.status === 'up' && (
                                    <div className="space-y-1">
                                      {record.cpuUsage !== undefined && (
                                        <div>CPU: <span className="font-mono">{record.cpuUsage}%</span></div>
                                      )}
                                      {record.memoryUsage !== undefined && (
                                        <div>Bellek: <span className="font-mono">{record.memoryUsage}%</span></div>
                                      )}
                                      {record.diskUsage !== undefined && (
                                        <div>Disk: <span className="font-mono">{record.diskUsage}%</span></div>
                                      )}
                                    </div>
                                  )}

                                  {record.type === 'dns' && record.status === 'up' && (
                                    <div>
                                      {record.responseTime && (
                                        <div>Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span></div>
                                      )}
                                      {record.resolvedIp && (
                                        <div>Çözümlenen IP: <span className="font-mono">{record.resolvedIp}</span></div>
                                      )}
                                      {record.records && Array.isArray(record.records) && (
                                        <div>Çözümlenen IP: <span className="font-mono">{record.records.join(', ')}</span></div>
                                      )}
                                    </div>
                                  )}

                                  {record.type === 'ssl' && record.status === 'up' && record.daysRemaining !== undefined && (
                                    <div>Kalan süre: <span className="font-mono">{record.daysRemaining} gün</span></div>
                                  )}

                                  {record.type === 'database' && record.status === 'up' && record.responseTime && (
                                    <div>Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span></div>
                                  )}

                                  {record.type === 'api' && record.status === 'up' && (
                                    <div>
                                      {record.responseTime && (
                                        <div>Yanıt süresi: <span className="font-mono">{record.responseTime} ms</span></div>
                                      )}
                                      {record.statusCode && (
                                        <div>Durum kodu: <span className="font-mono">{record.statusCode}</span></div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}

                          {/* Sayfalandırma */}
                          <PaginationComponent
                            currentPage={currentPage}
                            totalPages={totalPages}
                            totalItems={allHistory.length}
                            onPageChange={paginate}
                            pageSize={itemsPerPage}
                            onPageSizeChange={handlePageSizeChange}
                            pageSizeOptions={[5, 10, 25, 50, 100]}
                            showPageSizeOptions={true}
                          />
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </TabsComponent>
        </div>
        {/* Silme Onay Dialogu */}
        <AlertDialog open={deleteDialog.open} onOpenChange={(open) => !open && setDeleteDialog({ ...deleteDialog, open: false })}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Cihazı Sil</AlertDialogTitle>
              <AlertDialogDescription>
                <strong>{deleteDialog.deviceName}</strong> cihazını silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDeleteDialog({ ...deleteDialog, open: false })}>
                İptal
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-destructive text-destructive-foreground"
              >
                Sil
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* İzleme Türü Ayarları Sheet */}
        <MonitorSettingsSheet
          open={settingsSheet.open}
          onOpenChange={(open) => setSettingsSheet({ ...settingsSheet, open })}
          monitorType={settingsSheet.monitorType}
          deviceId={id}
          currentSettings={device?.monitors?.[settingsSheet.monitorType] || {}}
          onSuccess={() => {
            // Cihaz başarıyla güncellendiğinde verileri yenile
            loadData();
          }}
        />

        {/* Cihaz Düzenleme Dialogu */}
        <DeviceEditDialog
          open={editDialog.open}
          onOpenChange={(open) => setEditDialog({ open })}
          device={device}
          onSuccess={() => {
            // Cihaz başarıyla güncellendiğinde verileri yenile
            loadData();
          }}
        />
      </div>
    </div>
  );
};

export default DeviceDetail;
