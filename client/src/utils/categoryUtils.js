/**
 * <PERSON><PERSON><PERSON> isimlerini daha okunabilir hale getiren yardımcı fonksiyonlar
 */

/**
 * Alt kategori ismini daha okunabilir hale getirir
 * @param {string} subCategory - Alt kategori ismi
 * @returns {string} - Daha okunabilir alt kategori ismi
 */
export const formatSubCategory = (subCategory) => {
  if (!subCategory) return '';

  switch (subCategory) {
    case 'WebServer':
      return 'Web Sunucusu';
    case 'AccessPoint':
      return 'Access Point';
    case 'BinaSistemi':
      return 'Bina Sistemi';
    default:
      return subCategory;
  }
};

/**
 * Kategori grubundan alt kategoriyi çıkarır ve formatlar
 * @param {string} group - <PERSON><PERSON>i grubu (örn. "Web/WebServer")
 * @returns {string} - Formatlanmış alt kategori
 */
export const getFormattedSubCategory = (group) => {
  if (!group) return '';
  
  if (group.includes('/')) {
    const [, subCategory] = group.split('/');
    return formatSubCategory(subCategory);
  }
  
  return group;
};

/**
 * Kategori grubundan ana kategoriyi çıkarır
 * @param {string} group - Kategori grubu (örn. "Web/WebServer")
 * @returns {string} - Ana kategori
 */
export const getMainCategory = (group) => {
  if (!group) return '';
  
  if (group.includes('/')) {
    const [mainCategory] = group.split('/');
    return mainCategory;
  }
  
  return group;
};

/**
 * Alt kategori için uygun ikonu döndüren fonksiyon
 * @param {string} subCategory - Alt kategori ismi
 * @returns {string} - İkon ismi
 */
export const getSubCategoryIcon = (subCategory) => {
  if (!subCategory) return 'Tag';

  switch (subCategory) {
    // Ağ Cihazları
    case 'Router':
      return 'Router';
    case 'Switch':
      return 'Share2';
    case 'Firewall':
      return 'Shield';
    case 'AccessPoint':
      return 'Wifi';
    case 'Modem':
      return 'Radio';
    
    // Sunucular
    case 'Fiziksel':
      return 'Server';
    case 'Sanal':
      return 'Cloud';
    case 'Container':
      return 'Package';
    case 'Veritabanı':
      return 'Database';
    case 'Depolama':
      return 'HardDrive';
    
    // Web
    case 'WebServer':
      return 'Globe2';
    case 'API':
      return 'Webhook';
    case 'Mail':
      return 'Mail';
    case 'CDN':
      return 'Network';
    case 'DNS':
      return 'Search';
    
    // IoT
    case 'Sensör':
      return 'Activity';
    case 'PLC':
      return 'Cog';
    case 'BinaSistemi':
      return 'Building2';
    case 'UPS':
      return 'Battery';
    
    default:
      return 'Tag';
  }
};

/**
 * Ana kategori için uygun ikonu döndüren fonksiyon
 * @param {string} mainCategory - Ana kategori ismi
 * @returns {string} - İkon ismi
 */
export const getMainCategoryIcon = (mainCategory) => {
  if (!mainCategory) return 'Tag';

  switch (mainCategory) {
    case 'Ağ Cihazları':
      return 'Network';
    case 'Sunucular':
      return 'Server';
    case 'Web':
      return 'Globe';
    case 'IoT':
      return 'Cpu';
    default:
      return 'Tag';
  }
};
