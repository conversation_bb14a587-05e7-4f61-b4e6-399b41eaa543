import axios from 'axios';
import serverConfigService from './serverConfig';
import enhancedToastService from './enhanced-toast-service';
import {
  parseJsonDetails,
  parseJsonDetailsInArray,
  parseJsonDetailsInNestedObjects,
  parseJsonDetailsInAllDevices
} from './apiUtils';

// API temel URL'si
// Proxy kullanıldığında doğrudan /api yolunu kullan
let API_URL = '/api';

// Axios instance - basitleştirilmiş yapılandırma
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: false,
  timeout: 120000,
  timeoutErrorMessage: 'İstek zaman aşımına uğradı'
});

// Proxy kullanıldığında API URL'sini güncellemeye gerek yok
// API URL zaten '/api' olarak ayarlandı

// Token'ı al
const getToken = () => {
  return localStorage.getItem('auth_token');
};

// Konsola token bilgilerini yazdır (debug için)
console.log('Token:', getToken());
console.log('LocalStorage:', localStorage);

// Request interceptor - her istekte Authorization header'ını ekle
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor - 401 hatası durumunda token yenileme
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Eğer response yoksa (ağ hatası, zaman aşımı vb.)
    if (!error.response) {
      console.error('Network error or timeout:', error.message);
      return Promise.reject(error);
    }

    // Oturum zaman aşımı kontrolü
    if (error.response.status === 401 &&
        error.response.data &&
        error.response.data.code === 'SESSION_TIMEOUT') {
      console.log('Oturum zaman aşımı tespit edildi');

      // Token'ları ve kullanıcı bilgilerini temizle
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');

      // Toast bildirimi göster
      enhancedToastService.warning(
        'Oturum Zaman Aşımı',
        'Oturum süreniz doldu. Güvenlik nedeniyle otomatik olarak çıkış yapıldı.',
        { duration: 6000, playSound: true }
      );

      // Kullanıcıyı login sayfasına yönlendir
      window.location.href = '/login';
      return Promise.reject(error);
    }

    // Hesap devre dışı bırakılma kontrolü
    if (error.response.status === 401 &&
        error.response.data &&
        error.response.data.code === 'ACCOUNT_DISABLED') {
      console.log('Hesap devre dışı bırakılma tespit edildi');

      // Token'ları ve kullanıcı bilgilerini temizle
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');

      // Toast bildirimi göster
      enhancedToastService.error(
        'Hesap Devre Dışı Bırakıldı',
        error.response.data.error || 'Hesabınız yönetici tarafından devre dışı bırakıldı. Lütfen sistem yöneticisi ile iletişime geçin.',
        { duration: 8000, playSound: true }
      );

      // Kullanıcıyı login sayfasına yönlendir
      window.location.href = '/login';
      return Promise.reject(error);
    }

    // Hesap kilitlenme kontrolü
    if (error.response.status === 401 &&
        error.response.data &&
        error.response.data.code === 'ACCOUNT_LOCKED') {
      console.log('Hesap kilitlenme tespit edildi');

      // Token'ları ve kullanıcı bilgilerini temizle
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');

      // Toast bildirimi göster
      enhancedToastService.warning(
        'Hesap Kilitlendi',
        error.response.data.error || 'Hesabınız kilitlendi. Lütfen daha sonra tekrar deneyin.',
        { duration: 6000, playSound: true }
      );

      // Kullanıcıyı login sayfasına yönlendir
      window.location.href = '/login';
      return Promise.reject(error);
    }

    // Token süresi dolmuşsa ve bu istek daha önce yenilenmemişse
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Token yenileme işlemi
        // Not: Döngüsel bağımlılığı önlemek için auth servisini burada import etmiyoruz
        // Bunun yerine localStorage'dan doğrudan token'ları alıyoruz
        const refreshToken = localStorage.getItem('refresh_token');
        const user = JSON.parse(localStorage.getItem('user') || '{}');

        if (refreshToken && user.id) {
          const response = await axios.post(`${API_URL}/auth/refresh-token`, {
            refreshToken,
            userId: user.id
          });

          // Yeni token'ları sakla
          localStorage.setItem('auth_token', response.data.accessToken);
          localStorage.setItem('refresh_token', response.data.refreshToken);

          // Orijinal isteği yeni token ile tekrarla
          originalRequest.headers['Authorization'] = `Bearer ${response.data.accessToken}`;
          return axios(originalRequest);
        }
      } catch (refreshError) {
        // Token yenileme başarısız olursa çıkış yap
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');

        // Kullanıcıyı login sayfasına yönlendir
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Cihaz API servisleri
export const deviceService = {
  // Tüm cihazları getir - AbortController ile
  getAll: async (signal = null) => {
    const config = signal ? { signal } : {};
    const response = await api.get('/devices', config);
    return response.data;
  },

  // Belirli bir cihazı getir
  getById: async (id) => {
    const response = await api.get(`/devices/${id}`);
    return response.data;
  },

  // Yeni cihaz ekle
  create: async (device) => {
    const response = await api.post('/devices', device);
    return response.data;
  },

  // Cihazı güncelle
  update: async (id, device) => {
    const response = await api.put(`/devices/${id}`, device);
    return response.data;
  },

  // Cihazı sil
  delete: async (id) => {
    const response = await api.delete(`/devices/${id}`);
    return response.data;
  },

  // Tüm cihaz gruplarını getir
  getAllGroups: async () => {
    const response = await api.get('/devices/groups/all');
    return response.data;
  },

  // Birden fazla cihazın grubunu toplu olarak değiştir
  bulkUpdateGroup: async (deviceIds, group) => {
    // Grup null ise boş string olarak gönder (varsayılan grup için)
    const groupValue = group === null ? '' : group;
    const response = await api.put('/devices/bulk/group', { deviceIds, group: groupValue });
    return response.data;
  },

  // Birden fazla cihazı toplu olarak sil
  bulkDelete: async (deviceIds) => {
    try {
      // POST isteği olarak gönder (DELETE yerine)
      const response = await api.post('/devices/bulk/delete', { deviceIds });
      return response.data;
    } catch (error) {
      console.error('Error deleting devices:', error);
      throw error;
    }
  }
};

// İzleme API servisleri

export const monitorService = {
  // Bir cihazı manuel olarak kontrol et
  checkDevice: async (id) => {
    const response = await api.post(`/monitors/check/${id}`);
    return response.data;
  },

  // Birden fazla cihazı toplu olarak kontrol et
  bulkCheckDevices: async (deviceIds) => {
    // Tek tek cihazları kontrol et
    const results = {};
    for (const id of deviceIds) {
      try {
        const response = await api.post(`/monitors/check/${id}`);
        results[id] = response.data;
      } catch (error) {
        results[id] = { error: error.message || 'Error checking device' };
      }
    }
    return results;
  },

  // Genel izleme durumu getirme fonksiyonu
  getMonitorStatus: async (type, id, port = null) => {
    const url = port ? `/monitors/${type}/${id}/${port}` : `/monitors/${type}/${id}`;
    const response = await api.get(url);

    // JSON parse gerektiren türler için
    if (['ssl', 'dns'].includes(type)) {
      return parseJsonDetails(response.data);
    }

    return response.data;
  },

  // Genel izleme geçmişi getirme fonksiyonu
  getMonitorHistory: async (type, id, port = null, timeRange = null) => {
    let url = `/monitors/history/${type}/${id}`;
    if (port) url += `/${port}`;
    if (timeRange) url += `?timeRange=${timeRange}`;

    const response = await api.get(url);

    // JSON parse gerektiren türler için
    if (['ssl', 'dns'].includes(type)) {
      return parseJsonDetailsInArray(response.data);
    }

    return response.data;
  },

  // Kısa yol fonksiyonlar (geriye dönük uyumluluk için)
  getIcmpStatus: async (id) => monitorService.getMonitorStatus('icmp', id),
  getHttpStatus: async (id) => monitorService.getMonitorStatus('http', id),
  getTcpStatus: async (id, port) => monitorService.getMonitorStatus('tcp', id, port),
  getSnmpStatus: async (id) => monitorService.getMonitorStatus('snmp', id),
  getDnsStatus: async (id) => monitorService.getMonitorStatus('dns', id),
  getSslStatus: async (id) => monitorService.getMonitorStatus('ssl', id),
  getDatabaseStatus: async (id) => monitorService.getMonitorStatus('database', id),
  getApiStatus: async (id) => monitorService.getMonitorStatus('api', id),
  getSmtpStatus: async (id) => monitorService.getMonitorStatus('smtp', id),

  getIpmiStatus: async (id) => monitorService.getMonitorStatus('ipmi', id),
  getDockerStatus: async (id) => monitorService.getMonitorStatus('docker', id),

  // Geçmiş veriler için kısa yol fonksiyonlar
  getIcmpHistory: async (id, timeRange) => monitorService.getMonitorHistory('icmp', id, null, timeRange),
  getHttpHistory: async (id) => monitorService.getMonitorHistory('http', id),
  getTcpHistory: async (id, port) => monitorService.getMonitorHistory('tcp', id, port),
  getSnmpHistory: async (id) => monitorService.getMonitorHistory('snmp', id),
  getDnsHistory: async (id) => monitorService.getMonitorHistory('dns', id),
  getSslHistory: async (id) => monitorService.getMonitorHistory('ssl', id),
  getDatabaseHistory: async (id) => monitorService.getMonitorHistory('database', id),
  getApiHistory: async (id) => monitorService.getMonitorHistory('api', id),
  getSmtpHistory: async (id) => monitorService.getMonitorHistory('smtp', id),
  getIpmiHistory: async (id) => monitorService.getMonitorHistory('ipmi', id),
  getDockerHistory: async (id) => monitorService.getMonitorHistory('docker', id),

  // Bir cihazın tüm durumlarını getir
  getStatus: async (id) => {
    const response = await api.get(`/monitors/status/${id}`);
    return parseJsonDetailsInNestedObjects(response.data, ['ssl', 'dns', 'http', 'api']);
  },

  // Tüm cihazların durumunu getir
  getAllStatuses: async () => {
    const response = await api.get('/monitors/status/all');
    return parseJsonDetailsInAllDevices(response.data, ['ssl', 'dns', 'http', 'api']);
  },

  // Durum geçmişini getir (zaman içindeki durum değişimleri)
  getStatusHistory: async (timeRangeMinutes = 1440) => {
    try {
      const response = await api.get(`/monitors/status/history?timeRange=${timeRangeMinutes}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching status history:', error);
      throw error; // Hatayı yukarıya ilet
    }
  },

  // Cihaz bazında performans özeti getir
  getDevicePerformanceSummary: async (deviceIds) => {
    try {
      const queryParams = deviceIds.map(id => `deviceId=${id}`).join('&');
      const response = await api.get(`/monitors/performance/summary?${queryParams}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching device performance summary:', error);
      throw error; // Hatayı yukarıya ilet
    }
  }
};



// Uyarı API servisleri
export const alertService = {
  // Tüm uyarıları getir
  getAll: async () => {
    const response = await api.get('/alerts');
    return response.data;
  },

  // Bir cihazın uyarılarını getir
  getByDeviceId: async (id) => {
    const response = await api.get(`/alerts/device/${id}`);
    return response.data;
  },

  // Bir uyarıyı onayla
  acknowledge: async (deviceId, alertId) => {
    const response = await api.post(`/alerts/acknowledge/${deviceId}/${alertId}`);
    return response.data;
  },

  // Bildirim yapılandırmasını getir
  getConfig: async () => {
    const response = await api.get('/alerts/config');
    return response.data;
  },

  // Bildirim yapılandırmasını güncelle
  updateConfig: async (config) => {
    const response = await api.put('/alerts/config', config);
    return response.data;
  },

  // Test bildirimi gönder
  sendTest: async (type) => {
    const response = await api.post('/alerts/test', { type });
    return response.data;
  }
};

// Ayarlar API servisleri
export const settingsService = {
  // Tüm ayarları getir
  getAll: async () => {
    const response = await api.get('/settings');
    return response.data;
  },

  // Ayarları güncelle
  update: async (settings) => {
    const response = await api.put('/settings', settings);
    return response.data;
  }
};

// Kullanıcı API servisleri
export const userService = {
  // Tüm kullanıcıları getir (sadece admin)
  getAll: async () => {
    const response = await api.get('/users');
    return response.data;
  },

  // Kullanıcı detaylarını getir
  getById: async (id) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // Yeni kullanıcı oluştur (admin tarafından)
  create: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  // Kullanıcı güncelle
  update: async (id, userData) => {
    const response = await api.put(`/users/${id}`, userData);
    return response.data;
  },

  // Kullanıcı sil
  delete: async (id) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  },

  // Kullanıcı kilidini kaldır (geçici kilitleme)
  unlock: async (id) => {
    const response = await api.post(`/users/${id}/unlock`);
    return response.data;
  },

  // Kullanıcıyı devre dışı bırak
  disable: async (id) => {
    const response = await api.post(`/users/${id}/disable`);
    return response.data;
  },

  // Kullanıcıyı aktif et
  enable: async (id) => {
    const response = await api.post(`/users/${id}/enable`);
    return response.data;
  }
};

// API nesnesini doğrudan export et
export { api };

// Sistem API servisleri
export const systemService = {
  // Sistem servislerinin durumunu getir
  getStatus: async () => {
    const response = await api.get('/system/status');
    return response.data;
  },

  // Sistem istatistiklerini getir
  getStats: async () => {
    const response = await api.get('/system/stats');
    return response.data;
  }
};

// Servis nesnesini oluştur
const apiService = {
  deviceService,
  monitorService,
  alertService,
  settingsService,
  userService,
  systemService,
  api
};

// Servis nesnesini export et
export default apiService;
