import { toast } from 'sonner';
import axios from 'axios';
import enhancedToastService from './enhanced-toast-service';
import {
  NOTIFICATION_TYPES,
  NOTIFICATION_CATEGORIES,
  NOTIFICATION_SEVERITY
} from '../constants/notifications';

/**
 * ✅ Basit Kategori Doğrulama - Sadece source türleri
 * @param {string} category - Doğrulanacak kategori
 * @param {string} defaultCategory - Varsayılan kategori
 * @returns {string} - Doğrulanmış kategori (device/system)
 */
export const validateCategory = (category, defaultCategory = 'system') => {
  // Kategori yoksa varsayılan kategoriyi döndür
  if (!category) return defaultCategory;

  // Legacy kategorileri basit türlere dönüştür
  const categoryMapping = NOTIFICATION_CATEGORIES[category.toUpperCase()];
  if (categoryMapping) return categoryMapping;

  // Direkt source türü kontrol<PERSON>
  if (['device', 'system', 'user'].includes(category)) return category;

  return defaultCategory;
};

/**
 * Önem derecesi doğrulama fonksiyonu
 * @param {string} severity - Doğrulanacak önem derecesi
 * @param {string} defaultSeverity - Varsayılan önem derecesi
 * @returns {string} - Doğrulanmış önem derecesi
 */
export const validateSeverity = (severity, defaultSeverity = NOTIFICATION_SEVERITY.INFO) => {
  // Önem derecesi yoksa varsayılan önem derecesini döndür
  if (!severity) return defaultSeverity;

  // Önem derecesi varsa ve geçerliyse, olduğu gibi döndür
  const validSeverities = Object.values(NOTIFICATION_SEVERITY);
  return validSeverities.includes(severity) ? severity : defaultSeverity;
};

/**
 * Bildirim kaynağı oluşturma fonksiyonu
 * @param {Object} source - Kaynak nesnesi
 * @param {string} type - Bildirim türü
 * @returns {Object} - Oluşturulan kaynak nesnesi
 */
export const createNotificationSource = (source = {}, type = NOTIFICATION_TYPES.SYSTEM) => {
  // Bildirim türüne göre varsayılan değerler
  let defaultId = 'system';
  let defaultName = 'Sistem';
  let defaultType = 'system';

  if (type === NOTIFICATION_TYPES.DEVICE) {
    defaultId = 'unknown';
    defaultName = 'Bilinmeyen Cihaz';
    defaultType = 'device';
  } else if (type === NOTIFICATION_TYPES.USER) {
    defaultId = 'unknown';
    defaultName = 'Bilinmeyen Kullanıcı';
    defaultType = 'user';
  }

  // Kaynak nesnesini oluştur
  return {
    id: source.id || defaultId,
    name: source.name || defaultName,
    type: source.type || defaultType,
    monitorType: source.monitorType || 'system'
  };
};

/**
 * Bildirim Servisi
 *
 * Bu servis, uygulama genelinde bildirim yönetimini sağlar.
 * - Toast bildirimleri (Sonner)
 * - Kalıcı bildirimler (Bildirim paneli ve sayfası)
 */
class NotificationService {
  /**
   * Bildirim oluştur ve yönlendir
   * @param {Object} notification - Bildirim nesnesi
   * @param {string} notification.type - Bildirim türü: 'success', 'error', 'warning', 'info'
   * @param {string} notification.message - Bildirim mesajı
   * @param {string} notification.description - Detaylı açıklama (opsiyonel)
   * @param {boolean} notification.showAsToast - Toast olarak gösterilsin mi?
   * @param {boolean} notification.persistent - Kalıcı bildirim olarak kaydedilsin mi?
   * @param {Object} notification.source - Bildirim kaynağı (opsiyonel)
   * @param {Array} notification.actions - Bildirim eylemleri (opsiyonel)
   * @returns {string} Bildirim ID'si
   */
  createNotification(notification) {
    // Bildirim ID'si oluştur
    const id = Date.now().toString();

    // Bildirim nesnesini oluştur
    const notificationObj = {
      id,
      timestamp: Date.now(),
      status: 'new',
      ...notification
    };

    // Bildirim türüne göre yönlendir
    if (notification.showAsToast) {
      this.showToast(notificationObj);
    }

    // Kullanıcı giriş yapmışsa ve bildirim kalıcı olarak kaydedilecekse
    if (notification.persistent) {
      // Kullanıcı giriş yapmış mı kontrol et
      const user = localStorage.getItem('user');
      if (user) {
        this.addToPersistentNotifications(notificationObj);
      } else {
        console.log('Kullanıcı giriş yapmadığı için kalıcı bildirim kaydedilmiyor');
      }
    }

    return id;
  }

  /**
   * Toast bildirimi göster - Enhanced Toast Service'e yönlendir
   * @param {Object} notification - Bildirim nesnesi
   * @param {string} notification.type - Bildirim türü: 'success', 'error', 'warning', 'info'
   * @param {string} notification.message - Bildirim mesajı
   * @param {string} notification.description - Detaylı açıklama (opsiyonel)
   * @param {number} notification.duration - Bildirim süresi (ms) (opsiyonel, varsayılan: 5000)
   * @param {Array} notification.actions - Bildirim eylemleri (opsiyonel)
   */
  showToast(notification) {
    const { type, message, description, duration } = notification;

    // Enhanced Toast Service'e yönlendir - Ultra temiz tasarım
    const options = {
      duration,
      playSound: true // Ses çalmayı etkinleştir
      // Hiç etkileşim yok - sadece bilgi
    };

    // Enhanced Toast Service kullan
    enhancedToastService.showToast(type, message, description, options);
  }

  /**
   * Kalıcı bildirimlere ekle (bildirim paneli ve sayfası için)
   * @param {Object} notification - Bildirim nesnesi
   */
  async addToPersistentNotifications(notification) {
    try {
      // Kullanıcı giriş yapmış mı kontrol et
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('Kullanıcı giriş yapmadığı için kalıcı bildirim kaydedilmiyor');
        return null;
      }

      // Bildirim türüne göre doğru endpoint'i belirle
      const type = this.mapTypeToBackendType(notification.type, notification);
      let endpoint;

      if (type === 'device') {
        endpoint = '/api/notifications/device';
      } else if (type === 'user') {
        endpoint = '/api/notifications/user';
      } else {
        endpoint = '/api/notifications/system';
      }

      // API'ye bildirim gönder
      let response;

      // Bildirim kaynağını oluştur
      const source = createNotificationSource(notification.source, type);

      if (type === 'device') {
        // Cihaz bildirimi için
        response = await axios.post(endpoint, {
          deviceId: source.id,
          deviceName: source.name,
          monitorType: source.monitorType,
          status: this.mapTypeToSeverity(notification.type) === 'critical' ? 'down' : 'warning',
          message: notification.description || ''
        });
      } else if (type === 'user') {
        // Kullanıcı bildirimi için
        response = await axios.post(endpoint, {
          title: notification.message,
          message: notification.description || '',
          userId: source.id,
          username: source.name,
          action: notification.action || 'info',
          severity: validateSeverity(this.mapTypeToSeverity(notification.type)),
          metadata: notification.metadata || {}
        });
      } else {
        // Sistem bildirimi için
        response = await axios.post(endpoint, {
          title: notification.message,
          message: notification.description || '',
          category: validateCategory(notification.category),
          severity: validateSeverity(this.mapTypeToSeverity(notification.type)),
          sourceName: source.name,
          metadata: notification.metadata || {},
          actions: notification.actions || []
        });
      }

      return response.data;
    } catch (error) {
      console.error('Bildirim kaydedilirken hata:', error);
      // Hata durumunda toast göster
      toast.error('Bildirim kaydedilirken bir hata oluştu', {
        description: error.message
      });
      return null;
    }
  }

  /**
   * ✅ Basit Frontend → Backend Tür Dönüştürme
   * @param {string} type - Frontend bildirim türü
   * @param {Object} options - Ek seçenekler
   * @returns {string} Backend bildirim türü
   */
  mapTypeToBackendType(type, options = {}) {
    // 1. Kaynak tipine göre öncelik ver
    if (options.source && options.source.type) {
      const sourceType = options.source.type;
      if (['device', 'system', 'user'].includes(sourceType)) {
        return sourceType;
      }
    }

    // 2. Kategori bazlı basit mapping
    const category = validateCategory(options.category);
    if (category === 'device') return NOTIFICATION_TYPES.DEVICE;
    if (category === 'user') return NOTIFICATION_TYPES.USER;

    // 3. Bildirim türüne göre varsayılan
    switch (type) {
      case 'error':
      case 'warning':
        // Hata/uyarı bildirimleri için kaynak tipine bak
        if (options.source && options.source.type === 'device') {
          return NOTIFICATION_TYPES.DEVICE;
        }
        return NOTIFICATION_TYPES.SYSTEM;
      case 'success':
      case 'info':
      default:
        return NOTIFICATION_TYPES.SYSTEM;
    }
  }

  /**
   * Frontend bildirim türünü önem derecesine dönüştür
   * @param {string} type - Frontend bildirim türü
   * @returns {string} Önem derecesi
   */
  mapTypeToSeverity(type) {
    switch (type) {
      case 'error':
        return NOTIFICATION_SEVERITY.CRITICAL;
      case 'warning':
        return NOTIFICATION_SEVERITY.WARNING;
      case 'success':
        return NOTIFICATION_SEVERITY.SUCCESS;
      case 'info':
      default:
        return NOTIFICATION_SEVERITY.INFO;
    }
  }

  /**
   * Başarı bildirimi göster - Enhanced Toast Service kullan
   * @param {string} message - Bildirim mesajı
   * @param {Object} options - Bildirim seçenekleri
   */
  success(message, options = {}) {
    // Enhanced Toast Service ile toast göster - Ultra temiz tasarım
    enhancedToastService.success(message, options.description, {
      duration: options.duration
      // Hiç etkileşim yok - sadece bilgi
    });

    // Eğer persistent ise, bildirim oluştur
    if (options.persistent) {
      return this.createNotification({
        type: 'success',
        message,
        showAsToast: false, // Toast zaten gösterildi
        persistent: true,
        ...options
      });
    }
  }

  /**
   * Hata bildirimi göster - Enhanced Toast Service kullan
   * @param {string} message - Bildirim mesajı
   * @param {Object} options - Bildirim seçenekleri
   */
  error(message, options = {}) {
    // Enhanced Toast Service ile toast göster - Ultra temiz tasarım
    enhancedToastService.error(message, options.description, {
      duration: options.duration
      // Hiç etkileşim yok - sadece bilgi
    });

    // Eğer persistent ise, bildirim oluştur
    if (options.persistent) {
      return this.createNotification({
        type: 'error',
        message,
        showAsToast: false, // Toast zaten gösterildi
        persistent: true,
        ...options
      });
    }
  }

  /**
   * Uyarı bildirimi göster - Enhanced Toast Service kullan
   * @param {string} message - Bildirim mesajı
   * @param {Object} options - Bildirim seçenekleri
   */
  warning(message, options = {}) {
    // Enhanced Toast Service ile toast göster - Ultra temiz tasarım
    enhancedToastService.warning(message, options.description, {
      duration: options.duration
      // Hiç etkileşim yok - sadece bilgi
    });

    // Eğer persistent ise, bildirim oluştur
    if (options.persistent) {
      return this.createNotification({
        type: 'warning',
        message,
        showAsToast: false, // Toast zaten gösterildi
        persistent: true,
        ...options
      });
    }
  }

  /**
   * Bilgi bildirimi göster - Enhanced Toast Service kullan
   * @param {string} message - Bildirim mesajı
   * @param {Object} options - Bildirim seçenekleri
   */
  info(message, options = {}) {
    // Enhanced Toast Service ile toast göster - Ultra temiz tasarım
    enhancedToastService.info(message, options.description, {
      duration: options.duration
      // Hiç etkileşim yok - sadece bilgi
    });

    // Eğer persistent ise, bildirim oluştur
    if (options.persistent) {
      return this.createNotification({
        type: 'info',
        message,
        showAsToast: false, // Toast zaten gösterildi
        persistent: true,
        ...options
      });
    }
  }
}

// Singleton instance
export const notificationService = new NotificationService();

// Bildirim nesnesinden toast göster - Enhanced Toast Service kullan
export const showNotificationToast = (notification) => {
  if (!notification) return;

  // Enhanced Toast Service'e yönlendir
  enhancedToastService.showNotificationToast(notification);
};

// Kısayol fonksiyonları
export const showSuccess = (message, options) => notificationService.success(message, options);
export const showError = (message, options) => notificationService.error(message, options);
export const showWarning = (message, options) => notificationService.warning(message, options);
export const showInfo = (message, options) => notificationService.info(message, options);
