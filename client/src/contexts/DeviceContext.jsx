import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { deviceService, monitorService } from '../services/api';
import { useSocket } from './SocketContext';
import { useAuth } from './AuthContext';

// Context oluştur
const DeviceContext = createContext();

// Context Provider bileşeni
export const DeviceProvider = ({ children }) => {
  const [devices, setDevices] = useState([]);
  const [statuses, setStatuses] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [socketConnected, setSocketConnected] = useState(false); // Socket.io bağlantı durumu
  const [lastUpdated, setLastUpdated] = useState(new Date()); // Son güncelleme zamanı
  const [isChecking, setIsChecking] = useState(false); // <PERSON> kontrol durumu
  const [checkingDevices, setCheckingDevices] = useState(new Set()); // Kontrol edilen cihazlar
  const socket = useSocket();
  const { isAuthenticated } = useAuth();

  // Verileri yükle
  const loadData = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      console.log('DeviceContext: Cihaz verileri yükleniyor...');

      // Cihazları yükle
      const devicesData = await deviceService.getAll();
      setDevices(devicesData);

      // Durumları yükle
      const statusesData = await monitorService.getAllStatuses();
      setStatuses(statusesData);

      console.log('DeviceContext: Cihaz verileri başarıyla yüklendi');
    } catch (err) {
      console.error('DeviceContext: Cihaz verileri yüklenirken hata:', err);
      setError('Cihaz verileri yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Kullanıcı giriş yaptığında verileri yükle
  useEffect(() => {
    if (isAuthenticated) {
      loadData();
    }
  }, [isAuthenticated, loadData]);

  // Socket.io olaylarını dinle
  useEffect(() => {
    if (!socket || !isAuthenticated) return;

    console.log('DeviceContext: Socket.io olayları dinleniyor...');

    // Cihaz güncellemelerini dinle
    socket.on('device:new', (data) => {
      console.log('DeviceContext: Yeni cihaz eklendi:', data);
      if (data && data.device) {
        // Cihazlar listesini güncelle
        setDevices(prev => [...prev, data.device]);
      } else {
        // Veri yoksa tüm listeyi yenile
        loadData();
      }
    });

    socket.on('device:update', (data) => {
      console.log('DeviceContext: Cihaz güncellendi:', data);
      if (data && data.device) {
        // Cihazlar listesini güncelle
        setDevices(prev => prev.map(d => d.id === data.device.id ? data.device : d));
      } else {
        // Veri yoksa tüm listeyi yenile
        loadData();
      }
    });

    socket.on('device:delete', (data) => {
      console.log('DeviceContext: Cihaz silindi:', data);
      if (data && data.deviceId) {
        // Cihazlar listesinden sil
        setDevices(prev => prev.filter(d => d.id !== (data.deviceId || data.id)));
        // Durumlar listesinden de sil
        setStatuses(prev => {
          const newStatuses = {...prev};
          delete newStatuses[data.deviceId || data.id];
          return newStatuses;
        });
        // Son güncelleme zamanını güncelle
        setLastUpdated(new Date());
        console.log(`DeviceContext: Cihaz ${data.deviceId || data.id} durumlar listesinden silindi`);
      } else {
        // Veri yoksa tüm listeyi yenile
        loadData();
      }
    });

    socket.on('monitor:update', (data) => {
      console.log('DeviceContext: Cihaz durumu güncellendi:', data);
      // Sadece etkilenen cihazın durumunu güncelle
      if (data && data.deviceId) {
        setStatuses(prev => {
          // SSL details'i JSON'dan parse et
          if (data.type === 'ssl' && data.data && data.data.details && typeof data.data.details === 'string') {
            try {
              data.data.details = JSON.parse(data.data.details);
            } catch (e) {
              console.error('Error parsing SSL details:', e);
            }
          }

          // DNS details'i JSON'dan parse et
          if (data.type === 'dns' && data.data && data.data.details && typeof data.data.details === 'string') {
            try {
              data.data.details = JSON.parse(data.data.details);
            } catch (e) {
              console.error('Error parsing DNS details:', e);
            }
          }

          // HTTP details'i JSON'dan parse et
          if (data.type === 'http' && data.data && data.data.details && typeof data.data.details === 'string') {
            try {
              data.data.details = JSON.parse(data.data.details);
              console.log('HTTP details parsed:', data.data.details);
            } catch (e) {
              console.error('Error parsing HTTP details:', e);
            }
          }

          // API details'i JSON'dan parse et
          if (data.type === 'api' && data.data && data.data.details && typeof data.data.details === 'string') {
            try {
              data.data.details = JSON.parse(data.data.details);
              console.log('API details parsed:', data.data.details);
            } catch (e) {
              console.error('Error parsing API details:', e);
            }
          }

          // Artık sadece lastCheck ve nextCheck kullanılıyor

          // TCP için özel işlem - port bilgisini anahtar olarak kullan
          if (data.type === 'tcp' && data.port) {
            const currentStatus = prev[data.deviceId] || {};
            const currentTcp = currentStatus.tcp || {};

            return {
              ...prev,
              [data.deviceId]: {
                ...currentStatus,
                tcp: {
                  ...currentTcp,
                  [data.port]: data.data
                }
              }
            };
          } else {
            // Diğer monitor türleri için normal işlem
            return {
              ...prev,
              [data.deviceId]: {
                ...prev[data.deviceId],
                [data.type]: data.data
              }
            };
          }
        });

        // Eğer bu cihaz kontrol ediliyorsa, kontrol durumunu sonlandır
        if (checkingDevices.has(data.deviceId)) {
          setCheckingDevices(prev => {
            const newSet = new Set(prev);
            newSet.delete(data.deviceId);
            return newSet;
          });
        }

        // Son güncelleme zamanını güncelle
        setLastUpdated(new Date());
      }
    });

    // Cihaz kontrolü tamamlandığında
    socket.on('device:checked', (data) => {
      console.log('DeviceContext: Cihaz kontrolü tamamlandı:', data);

      // Belirli bir cihaz kontrol edildiyse, sadece o cihazın verilerini güncelle
      if (data && data.deviceId) {
        // Önce cihazın hala mevcut olup olmadığını kontrol et
        const deviceExists = devices.some(device => device.id === data.deviceId);

        if (deviceExists) {
          // Eğer hesaplanmış durum varsa, doğrudan kullan
          if (data.calculatedStatus && data.lastCalculated) {
            console.log(`DeviceContext: Cihaz ${data.deviceId} için hesaplanmış durum kullanılıyor:`, data.calculatedStatus);

            // Mevcut durumu al ve hesaplanmış durumu ekle
            setStatuses(prev => {
              const currentStatus = prev[data.deviceId] || {};
              return {
                ...prev,
                [data.deviceId]: {
                  ...currentStatus,
                  calculatedStatus: data.calculatedStatus,
                  reason: data.reason,
                  rawStatus: data.rawStatus,
                  lastCalculated: data.lastCalculated
                }
              };
            });

            // Eğer bu cihaz kontrol ediliyorsa, kontrol durumunu sonlandır
            if (checkingDevices.has(data.deviceId)) {
              setCheckingDevices(prev => {
                const newSet = new Set(prev);
                newSet.delete(data.deviceId);
                return newSet;
              });
            }

            // Son güncelleme zamanını güncelle
            setLastUpdated(new Date());
          } else {
            // Hesaplanmış durum yoksa, API'den al
            monitorService.getStatus(data.deviceId)
              .then(status => {
                setStatuses(prev => ({
                  ...prev,
                  [data.deviceId]: status
                }));
                // Son güncelleme zamanını güncelle
                setLastUpdated(new Date());
              })
              .catch(err => {
                // 404 hatası alınırsa, cihaz silinmiş olabilir
                if (err.response && err.response.status === 404) {
                  console.log(`DeviceContext: Cihaz ${data.deviceId} bulunamadı, muhtemelen silinmiş`);
                  // Durumlar listesinden sil
                  setStatuses(prev => {
                    const newStatuses = {...prev};
                    delete newStatuses[data.deviceId];
                    return newStatuses;
                  });
                } else {
                  console.error('Error updating device status:', err);
                }
              });
          }
        } else {
          console.log(`DeviceContext: Cihaz ${data.deviceId} bulunamadı, muhtemelen silinmiş`);
        }
      } else {
        // Tüm cihazlar kontrol edildiyse, throttled güncelleme yap
        console.log('DeviceContext: Tüm cihazlar kontrol edildi, güncelleme atlanıyor (throttled)');
        // API çağrısını azaltmak için bu güncellemeyi atlıyoruz
      }
    });

    // Bağlantı durumunu izle
    socket.on('connect', () => {
      console.log('DeviceContext: Socket.io bağlantısı kuruldu');

      // Socket.io bağlantı durumunu güncelle
      setSocketConnected(true);
      console.log('DeviceContext: Socket bağlantısı kuruldu, mevcut durumları isteniyor...');

      // Sadece mevcut durumları al, cihaz listesini güncelleme
      socket.emit('device:status:get');
    });

    socket.on('disconnect', () => {
      console.log('DeviceContext: Socket.io bağlantısı kesildi');
    });

    // Cihaz durumu güncellemelerini dinle
    socket.on('device:status:update', (data) => {
      console.log('DeviceContext: Cihaz durumu güncellendi:', data);

      if (data && data.deviceId && data.calculatedStatus) {
        // Mevcut durumu al ve hesaplanmış durumu ekle
        setStatuses(prev => {
          const currentStatus = prev[data.deviceId] || {};
          return {
            ...prev,
            [data.deviceId]: {
              ...currentStatus,
              calculatedStatus: data.calculatedStatus,
              reason: data.reason,
              rawStatus: data.rawStatus,
              lastCalculated: data.lastCalculated
            }
          };
        });

        // Son güncelleme zamanını güncelle
        setLastUpdated(new Date());
      }
    });

    // Temizlik fonksiyonu
    return () => {
      socket.off('device:new');
      socket.off('device:update');
      socket.off('device:delete');
      socket.off('monitor:update');
      socket.off('device:checked');
      socket.off('device:status:update');
      socket.off('connect');
      socket.off('disconnect');
    };
  }, [socket, loadData, isAuthenticated]);

  // Socket.io ile gerçek zamanlı güncellemeler kullanılıyor

  // Cihazları manuel olarak kontrol et (Socket.io kullanarak)
  const checkAllDevices = useCallback(async () => {
    if (!isAuthenticated || !socket) return false;

    try {
      console.log('DeviceContext: Tüm cihazlar kontrol ediliyor (Socket.io)...');
      setIsChecking(true);

      // Socket.io ile cihaz kontrol isteği gönder
      return new Promise((resolve, reject) => {
        // Yanıt için bir kerelik olay dinleyicisi ekle
        const responseHandler = (response) => {
          console.log('DeviceContext: Cihaz kontrol yanıtı alındı:', response);
          socket.off('device:check:response', responseHandler);
          setIsChecking(false);

          if (response.success) {
            resolve(true);
          } else {
            reject(new Error(response.error || 'Cihaz kontrolü başarısız oldu'));
          }
        };

        // Yanıtı dinle
        socket.on('device:check:response', responseHandler);

        // İsteği gönder
        socket.emit('device:check', { deviceIds: [], monitorTypes: [] });

        // 15 saniye timeout
        setTimeout(() => {
          socket.off('device:check:response', responseHandler);
          setIsChecking(false);
          reject(new Error('Cihaz kontrol isteği zaman aşımına uğradı'));
        }, 15000);
      });
    } catch (err) {
      console.error('DeviceContext: Cihazlar kontrol edilirken hata:', err);
      setIsChecking(false);
      return false;
    }
  }, [isAuthenticated, socket]);

  // Belirli cihazları kontrol et (Socket.io kullanarak)
  const checkDevices = useCallback(async (deviceIds, monitorTypes = []) => {
    if (!isAuthenticated || !socket) return false;

    try {
      console.log(`DeviceContext: ${deviceIds.length} cihaz kontrol ediliyor (Socket.io)...`);

      // Socket.io ile cihaz kontrol isteği gönder
      return new Promise((resolve, reject) => {
        // Yanıt için bir kerelik olay dinleyicisi ekle
        const responseHandler = (response) => {
          console.log('DeviceContext: Cihaz kontrol yanıtı alındı:', response);
          socket.off('device:check:response', responseHandler);

          if (response.success) {
            resolve(true);
          } else {
            reject(new Error(response.error || 'Cihaz kontrolü başarısız oldu'));
          }
        };

        // Yanıtı dinle
        socket.on('device:check:response', responseHandler);

        // İsteği gönder
        socket.emit('device:check', { deviceIds, monitorTypes });

        // 15 saniye timeout
        setTimeout(() => {
          socket.off('device:check:response', responseHandler);
          reject(new Error('Cihaz kontrol isteği zaman aşımına uğradı'));
        }, 15000);
      });
    } catch (err) {
      console.error('DeviceContext: Cihazlar kontrol edilirken hata:', err);
      return false;
    }
  }, [isAuthenticated, socket]);

  // Tek bir cihazı kontrol et
  const checkDevice = useCallback(async (deviceId) => {
    if (!isAuthenticated || !socket || !deviceId) return false;

    try {
      console.log(`DeviceContext: Cihaz ${deviceId} kontrol ediliyor (Socket.io)...`);

      // Bu cihazı kontrol edilen listesine ekle
      setCheckingDevices(prev => new Set([...prev, deviceId]));

      // Socket.io ile cihaz kontrol isteği gönder
      return new Promise((resolve, reject) => {
        // Yanıt için bir kerelik olay dinleyicisi ekle
        const responseHandler = (response) => {
          console.log('DeviceContext: Cihaz kontrol yanıtı alındı:', response);
          socket.off('device:check:response', responseHandler);

          // Kontrol edilen cihazı listeden çıkar
          setCheckingDevices(prev => {
            const newSet = new Set(prev);
            newSet.delete(deviceId);
            return newSet;
          });

          if (response.success) {
            resolve(true);
          } else {
            reject(new Error(response.error || 'Cihaz kontrolü başarısız oldu'));
          }
        };

        // Yanıtı dinle
        socket.on('device:check:response', responseHandler);

        // İsteği gönder
        socket.emit('device:check', { deviceIds: [deviceId], monitorTypes: [] });

        // 15 saniye timeout
        setTimeout(() => {
          socket.off('device:check:response', responseHandler);
          setCheckingDevices(prev => {
            const newSet = new Set(prev);
            newSet.delete(deviceId);
            return newSet;
          });
          reject(new Error('Cihaz kontrol isteği zaman aşımına uğradı'));
        }, 15000);
      });
    } catch (err) {
      console.error(`DeviceContext: Cihaz ${deviceId} kontrol edilirken hata:`, err);
      setCheckingDevices(prev => {
        const newSet = new Set(prev);
        newSet.delete(deviceId);
        return newSet;
      });
      return false;
    }
  }, [isAuthenticated, socket]);

  // Cihazın kontrol edilip edilmediğini kontrol et
  const isDeviceChecking = useCallback((deviceId) => {
    return checkingDevices.has(deviceId);
  }, [checkingDevices]);

  // Context değerleri
  const value = {
    devices,
    setDevices,
    statuses,
    loading,
    error,
    socketConnected,
    lastUpdated,
    isChecking,
    checkingDevices,
    loadData,
    checkAllDevices,
    checkDevices,
    checkDevice,
    isDeviceChecking
  };

  return <DeviceContext.Provider value={value}>{children}</DeviceContext.Provider>;
};

// Custom hook
export const useDevices = () => {
  const context = useContext(DeviceContext);

  if (!context) {
    throw new Error('useDevices hook must be used within a DeviceProvider');
  }

  return context;
};

export default DeviceContext;
