import React from 'react';
import {
  Wifi,
  Server,
  Globe,
  ShieldCheck,
  Database,
  Network,
  ChevronsLeftRight,
  Braces,
  Mail,
  Monitor,
  Terminal,
  HardDrive,
  Container,
  Activity,
  Globe2,
  Router
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';

/**
 * İzleyici türü için ikon bileşeni
 * @param {string} type - İzleyici türü (icmp, snmp, dns, ssl, database, tcp, http, api)
 * @param {string} size - İkon boyutu (sm, md, lg)
 * @param {boolean} active - İzleyici aktif mi?
 * @param {boolean} useInheritedColor - Badge içinde kullanıldığında true olmalı
 * @param {boolean} showTooltip - Tooltip gösterilsin mi? (varsayılan: true)
 * @param {string} className - Ek CSS sınıfları
 */
const MonitorTypeIcon = ({ type, size = 'sm', active = false, useInheritedColor = false, showTooltip = true, className = '' }) => {
  // Boyut sınıfını belirle
  const sizeClass = size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-5 w-5' : 'h-6 w-6';

  // Renk sınıfı
  let colorClass;
  if (useInheritedColor) {
    colorClass = '';
  } else if (active) {
    colorClass = 'text-success';
  } else {
    colorClass = 'text-muted-foreground';
  }

  // İkon sınıfı
  const iconClass = `${sizeClass} ${colorClass} ${className}`;

  // İzleyici türlerine göre isimler
  const monitorNames = {
    icmp: 'ICMP Ping',
    tcp: 'TCP Port',
    http: 'HTTP/HTTPS',
    dns: 'DNS Sorgusu',
    ssl: 'SSL Sertifikası',
    // Grup ikonları
    network: 'Ağ İzleyicileri',
    services: 'Servis İzleyicileri',
    security: 'Güvenlik İzleyicileri'
  };

  // İzleyici türüne göre ikon
  const getIcon = (type) => {
    switch (type.toLowerCase()) {
      case 'icmp':
        return <Activity className={iconClass} />; // Ping için daha uygun
      case 'snmp':
        return <Router className={iconClass} />; // SNMP için ağ cihazı ikonu
      case 'dns':
        return <Globe className={iconClass} />;
      case 'ssl':
        return <ShieldCheck className={iconClass} />;
      case 'database':
        return <Database className={iconClass} />;
      case 'tcp':
        return <Network className={iconClass} />;
      case 'http':
        return <Globe2 className={iconClass} />; // HTTP için daha uygun
      case 'api':
        return <Braces className={iconClass} />;
      case 'smtp':
        return <Mail className={iconClass} />;
      case 'system':
        return <Server className={iconClass} />; // Sistem için server ikonu
      case 'windows':
        return <Monitor className={iconClass} />;
      case 'linux':
        return <Terminal className={iconClass} />;
      case 'ipmi':
        return <HardDrive className={iconClass} />;
      case 'docker':
        return <Container className={iconClass} />;
      // Grup ikonları
      case 'network':
        return <Activity className={iconClass} />; // Ağ İzleyicileri için
      case 'services':
        return <Braces className={iconClass} />; // Servis İzleyicileri için
      case 'security':
        return <ShieldCheck className={iconClass} />; // Güvenlik İzleyicileri için
      default:
        return null;
    }
  };

  // Tooltip içeriği
  const tooltipContent = active
    ? `${monitorNames[type.toLowerCase()] || type} (Aktif)`
    : `${monitorNames[type.toLowerCase()] || type} (Pasif)`;

  // Tooltip ile veya tooltip olmadan ikon döndür
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span>
              {getIcon(type)}
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipContent}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Tooltip olmadan sadece ikon döndür
  return getIcon(type);
};

export default MonitorTypeIcon;
