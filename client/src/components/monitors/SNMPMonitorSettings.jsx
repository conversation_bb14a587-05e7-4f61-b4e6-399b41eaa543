import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * SNMP izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const SNMPMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="version">SNMP Versiyonu</Label>
        <Select
          value={settings.version || '2c'}
          onValueChange={(value) => handleSelectChange('version', value)}
        >
          <SelectTrigger id="version">
            <SelectValue placeholder="SNMP versiyonu seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">Version 1</SelectItem>
            <SelectItem value="2c">Version 2c</SelectItem>
            <SelectItem value="3">Version 3</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="community">Community String</Label>
        <Input
          id="community"
          name="community"
          value={settings.community || 'netmonitor'}
          onChange={handleInputChange}
          placeholder="Örn: netmonitor"
        />
        <p className="text-xs text-muted-foreground">
          SNMP community string
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '60'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 dakika</SelectItem>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          SNMP kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default SNMPMonitorSettings;
