import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Switch } from "../ui/switch";
import { Textarea } from "../ui/textarea";

/**
 * Docker Container izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const DockerMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  // Switch değişikliklerini işle
  const handleSwitchChange = (name, checked) => {
    onChange({ [name]: checked });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="host">
          Docker Host <span className="text-destructive">*</span>
        </Label>
        <Input
          id="host"
          name="host"
          value={settings.host || ''}
          onChange={handleInputChange}
          placeholder="Örn: ************* veya docker.example.com"
          className={errors.host ? "border-destructive" : ""}
        />
        {errors.host && (
          <p className="text-xs text-destructive">{errors.host}</p>
        )}
        <p className="text-xs text-muted-foreground">
          Docker daemon çalışan sunucu IP adresi veya hostname
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="port">Docker API Port</Label>
        <Select
          value={String(settings.port || '2376')}
          onValueChange={(value) => handleSelectChange('port', parseInt(value))}
        >
          <SelectTrigger id="port">
            <SelectValue placeholder="Port seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="2375">2375 (HTTP - Güvensiz)</SelectItem>
            <SelectItem value="2376">2376 (HTTPS - Güvenli)</SelectItem>
            <SelectItem value="2377">2377 (Docker Swarm)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Docker API port numarası
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="secure"
            checked={settings.secure || false}
            onCheckedChange={(checked) => handleSwitchChange('secure', checked)}
          />
          <Label htmlFor="secure">HTTPS Kullan</Label>
        </div>
        <p className="text-xs text-muted-foreground">
          Güvenli HTTPS bağlantısı kullanılsın mı? (TLS)
        </p>
      </div>

      {settings.secure && (
        <div className="space-y-2">
          <Label htmlFor="certPath">TLS Sertifika Yolu</Label>
          <Input
            id="certPath"
            name="certPath"
            value={settings.certPath || ''}
            onChange={handleInputChange}
            placeholder="Örn: /path/to/certs veya sertifika içeriği"
            className={errors.certPath ? "border-destructive" : ""}
          />
          {errors.certPath && (
            <p className="text-xs text-destructive">{errors.certPath}</p>
          )}
          <p className="text-xs text-muted-foreground">
            TLS sertifika dosya yolu veya sertifika içeriği
          </p>
        </div>
      )}

      {settings.secure && (
        <div className="space-y-2">
          <Label htmlFor="clientCert">Client Sertifikası (Opsiyonel)</Label>
          <Textarea
            id="clientCert"
            name="clientCert"
            value={settings.clientCert || ''}
            onChange={handleInputChange}
            placeholder="-----BEGIN CERTIFICATE-----&#10;...&#10;-----END CERTIFICATE-----"
            className={`min-h-[80px] ${errors.clientCert ? "border-destructive" : ""}`}
          />
          {errors.clientCert && (
            <p className="text-xs text-destructive">{errors.clientCert}</p>
          )}
          <p className="text-xs text-muted-foreground">
            Client sertifikası (mutual TLS için)
          </p>
        </div>
      )}

      {settings.secure && (
        <div className="space-y-2">
          <Label htmlFor="clientKey">Client Private Key (Opsiyonel)</Label>
          <Textarea
            id="clientKey"
            name="clientKey"
            value={settings.clientKey || ''}
            onChange={handleInputChange}
            placeholder="-----BEGIN PRIVATE KEY-----&#10;...&#10;-----END PRIVATE KEY-----"
            className={`min-h-[80px] ${errors.clientKey ? "border-destructive" : ""}`}
          />
          {errors.clientKey && (
            <p className="text-xs text-destructive">{errors.clientKey}</p>
          )}
          <p className="text-xs text-muted-foreground">
            Client private key (mutual TLS için)
          </p>
        </div>
      )}

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="ignoreCertificate"
            checked={settings.ignoreCertificate || false}
            onCheckedChange={(checked) => handleSwitchChange('ignoreCertificate', checked)}
          />
          <Label htmlFor="ignoreCertificate">Sertifika Hatalarını Yoksay</Label>
        </div>
        <p className="text-xs text-muted-foreground">
          TLS sertifika hatalarını yoksay (self-signed sertifikalar için)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="timeout">Timeout (saniye)</Label>
        <Select
          value={String(settings.timeout || '15')}
          onValueChange={(value) => handleSelectChange('timeout', parseInt(value))}
        >
          <SelectTrigger id="timeout">
            <SelectValue placeholder="Timeout seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10 saniye</SelectItem>
            <SelectItem value="15">15 saniye</SelectItem>
            <SelectItem value="30">30 saniye</SelectItem>
            <SelectItem value="60">60 saniye</SelectItem>
            <SelectItem value="120">2 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Docker API bağlantı timeout süresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '15'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Docker container kontrolü ne sıklıkla yapılacak?
        </p>
      </div>

      <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          Docker Host Gereksinimleri:
        </h4>
        <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
          <li>• Docker daemon çalışıyor olmalı</li>
          <li>• Docker API portu açık olmalı</li>
          <li>• Network erişimi mevcut olmalı</li>
          <li>• TLS kullanılıyorsa geçerli sertifikalar</li>
        </ul>
      </div>

      <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
          İzlenen Container Bilgileri:
        </h4>
        <ul className="text-xs text-green-800 dark:text-green-200 space-y-1">
          <li>• <strong>Container'lar:</strong> Durum, isim, image, port'lar</li>
          <li>• <strong>Sistem:</strong> Docker version, CPU, memory</li>
          <li>• <strong>Image'lar:</strong> Boyut, oluşturma tarihi</li>
          <li>• <strong>Network'ler:</strong> Docker network'leri</li>
          <li>• <strong>Sağlık:</strong> Container sağlık durumu</li>
        </ul>
      </div>

      <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-amber-900 dark:text-amber-100 mb-2">
          Docker API Aktifleştirme:
        </h4>
        <div className="text-xs text-amber-800 dark:text-amber-200 space-y-1">
          <p>Docker daemon'u API ile erişim için:</p>
          <code className="block bg-amber-100 dark:bg-amber-900/30 p-2 rounded mt-1">
            # HTTP (Güvensiz)<br/>
            dockerd -H tcp://0.0.0.0:2375<br/><br/>
            # HTTPS (Güvenli)<br/>
            dockerd -H tcp://0.0.0.0:2376 --tls --tlscert=cert.pem --tlskey=key.pem
          </code>
        </div>
      </div>

      <div className="bg-purple-50 dark:bg-purple-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
          Test Komutu:
        </h4>
        <div className="text-xs text-purple-800 dark:text-purple-200 space-y-1">
          <p>Docker API bağlantısını test etmek için:</p>
          <code className="block bg-purple-100 dark:bg-purple-900/30 p-2 rounded mt-1">
            curl http://[HOST]:2375/containers/json<br/>
            curl https://[HOST]:2376/containers/json
          </code>
        </div>
      </div>
    </div>
  );
};

export default DockerMonitorSettings;
