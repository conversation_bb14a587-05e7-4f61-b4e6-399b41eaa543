import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * API izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const APIMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="url">
          API URL <span className="text-destructive">*</span>
        </Label>
        <Input
          id="url"
          name="url"
          value={settings.url || ''}
          onChange={handleInputChange}
          placeholder="Örn: https://api.example.com/status"
          className={errors.url ? "border-destructive" : ""}
        />
        {errors.url && (
          <p className="text-xs text-destructive">{errors.url}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="method">HTTP Metodu</Label>
        <Select
          value={settings.method || 'GET'}
          onValueChange={(value) => handleSelectChange('method', value)}
        >
          <SelectTrigger id="method">
            <SelectValue placeholder="HTTP metodu seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="GET">GET</SelectItem>
            <SelectItem value="POST">POST</SelectItem>
            <SelectItem value="PUT">PUT</SelectItem>
            <SelectItem value="DELETE">DELETE</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="expectedStatus">Beklenen Durum Kodu</Label>
        <Input
          id="expectedStatus"
          name="expectedStatus"
          value={settings.expectedStatus || '200'}
          onChange={handleInputChange}
          placeholder="Örn: 200"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="headers">Headers (JSON formatında)</Label>
        <Textarea
          id="headers"
          name="headers"
          value={settings.headers ? JSON.stringify(settings.headers) : ''}
          onChange={handleInputChange}
          placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'
          rows={2}
        />
        <p className="text-xs text-muted-foreground">
          JSON formatında HTTP başlıkları
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="body">Request Body</Label>
        <Textarea
          id="body"
          name="body"
          value={settings.body || ''}
          onChange={handleInputChange}
          placeholder="POST, PUT istekleri için istek gövdesi"
          rows={2}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="expectedContent">Beklenen İçerik (Opsiyonel)</Label>
        <Input
          id="expectedContent"
          name="expectedContent"
          value={settings.expectedContent || ''}
          onChange={handleInputChange}
          placeholder="Yanıtta bulunması gereken metin"
        />
      </div>

      <div className="border-t pt-4 mt-4">
        <h3 className="font-medium mb-2">API Kimlik Doğrulama</h3>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Token/Anahtar</Label>
            <Input
              id="apiKey"
              name="apiKey"
              value={settings.apiKey || ''}
              onChange={handleInputChange}
              placeholder="API token veya anahtar değeri"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="apiKeyLocation">Token/Anahtar Konumu</Label>
            <Select
              value={settings.apiKeyLocation || 'header'}
              onValueChange={(value) => handleSelectChange('apiKeyLocation', value)}
            >
              <SelectTrigger id="apiKeyLocation">
                <SelectValue placeholder="Konum seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="query">URL Query Parametresi</SelectItem>
                <SelectItem value="header">HTTP Header</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {settings.apiKeyLocation === 'query' && (
            <div className="space-y-2">
              <Label htmlFor="apiKeyParamName">Query Parametre Adı</Label>
              <Input
                id="apiKeyParamName"
                name="apiKeyParamName"
                value={settings.apiKeyParamName || 'api_key'}
                onChange={handleInputChange}
                placeholder="Örn: api_key, token, key"
              />
            </div>
          )}

          {settings.apiKeyLocation === 'header' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="apiKeyHeaderName">Header Adı</Label>
                <Input
                  id="apiKeyHeaderName"
                  name="apiKeyHeaderName"
                  value={settings.apiKeyHeaderName || 'Authorization'}
                  onChange={handleInputChange}
                  placeholder="Örn: Authorization, X-API-Key"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiKeyPrefix">Header Değeri Öneki</Label>
                <Input
                  id="apiKeyPrefix"
                  name="apiKeyPrefix"
                  value={settings.apiKeyPrefix || ''}
                  onChange={handleInputChange}
                  placeholder="Örn: Bearer, Token (boş bırakılabilir)"
                />
                <p className="text-xs text-muted-foreground">
                  Örnek: "Bearer" girerseniz, "Bearer {token}" şeklinde gönderilir
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '10'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 dakika</SelectItem>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          API kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default APIMonitorSettings;
