import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * IPMI Hardware izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const IPMIMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="host">
          IPMI/BMC Adresi <span className="text-destructive">*</span>
        </Label>
        <Input
          id="host"
          name="host"
          value={settings.host || ''}
          onChange={handleInputChange}
          placeholder="Örn: ************* veya idrac.server.com"
          className={errors.host ? "border-destructive" : ""}
        />
        {errors.host && (
          <p className="text-xs text-destructive">{errors.host}</p>
        )}
        <p className="text-xs text-muted-foreground">
          IPMI/BMC IP adresi veya hostname (iDRAC, iLO, IPMI)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="username">
          IPMI Kullanıcı Adı <span className="text-destructive">*</span>
        </Label>
        <Input
          id="username"
          name="username"
          value={settings.username || ''}
          onChange={handleInputChange}
          placeholder="Örn: root, admin, ADMIN"
          className={errors.username ? "border-destructive" : ""}
        />
        {errors.username && (
          <p className="text-xs text-destructive">{errors.username}</p>
        )}
        <p className="text-xs text-muted-foreground">
          IPMI/BMC kullanıcı adı
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="password">
          IPMI Şifresi <span className="text-destructive">*</span>
        </Label>
        <Input
          id="password"
          name="password"
          type="password"
          value={settings.password || ''}
          onChange={handleInputChange}
          placeholder="IPMI/BMC şifresi"
          className={errors.password ? "border-destructive" : ""}
        />
        {errors.password && (
          <p className="text-xs text-destructive">{errors.password}</p>
        )}
        <p className="text-xs text-muted-foreground">
          IPMI/BMC kullanıcı şifresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="port">IPMI Port</Label>
        <Select
          value={String(settings.port || '623')}
          onValueChange={(value) => handleSelectChange('port', parseInt(value))}
        >
          <SelectTrigger id="port">
            <SelectValue placeholder="Port seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="623">623 (Varsayılan IPMI)</SelectItem>
            <SelectItem value="664">664 (IPMI over LAN+)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          IPMI port numarası (UDP)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interface">IPMI Interface</Label>
        <Select
          value={settings.interface || 'lanplus'}
          onValueChange={(value) => handleSelectChange('interface', value)}
        >
          <SelectTrigger id="interface">
            <SelectValue placeholder="Interface seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="lanplus">LAN+ (Önerilen)</SelectItem>
            <SelectItem value="lan">LAN</SelectItem>
            <SelectItem value="open">Open (Yerel)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          IPMI bağlantı interface türü
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="timeout">Timeout (saniye)</Label>
        <Select
          value={String(settings.timeout || '15')}
          onValueChange={(value) => handleSelectChange('timeout', parseInt(value))}
        >
          <SelectTrigger id="timeout">
            <SelectValue placeholder="Timeout seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10 saniye</SelectItem>
            <SelectItem value="15">15 saniye</SelectItem>
            <SelectItem value="30">30 saniye</SelectItem>
            <SelectItem value="60">60 saniye</SelectItem>
            <SelectItem value="120">2 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          IPMI komut timeout süresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '30'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
            <SelectItem value="120">2 saat</SelectItem>
            <SelectItem value="240">4 saat</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          IPMI donanım kontrolü ne sıklıkla yapılacak?
        </p>
      </div>

      <div className="bg-orange-50 dark:bg-orange-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-orange-900 dark:text-orange-100 mb-2">
          IPMI/BMC Gereksinimleri:
        </h4>
        <ul className="text-xs text-orange-800 dark:text-orange-200 space-y-1">
          <li>• IPMI/BMC interface aktif olmalı</li>
          <li>• Network üzerinden IPMI erişimi açık olmalı</li>
          <li>• IPMI kullanıcısı tanımlı ve yetkili olmalı</li>
          <li>• Port 623 (UDP) açık olmalı</li>
          <li>• ipmitool monitoring sunucuda kurulu olmalı</li>
        </ul>
      </div>

      <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          Desteklenen Donanımlar:
        </h4>
        <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
          <li>• <strong>Dell:</strong> iDRAC (Integrated Dell Remote Access Controller)</li>
          <li>• <strong>HP/HPE:</strong> iLO (Integrated Lights-Out)</li>
          <li>• <strong>IBM:</strong> IMM (Integrated Management Module)</li>
          <li>• <strong>Supermicro:</strong> IPMI BMC</li>
          <li>• <strong>Diğer:</strong> IPMI 2.0 uyumlu BMC'ler</li>
        </ul>
      </div>

      <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
          İzlenen Donanım Bilgileri:
        </h4>
        <ul className="text-xs text-green-800 dark:text-green-200 space-y-1">
          <li>• <strong>Sıcaklık:</strong> CPU, sistem, inlet sıcaklıkları</li>
          <li>• <strong>Fan:</strong> Fan hızları ve durumları</li>
          <li>• <strong>Güç:</strong> Güç durumu ve tüketimi</li>
          <li>• <strong>Voltaj:</strong> Sistem voltaj seviyeleri</li>
          <li>• <strong>Sistem:</strong> Üretici, model, seri numarası</li>
        </ul>
      </div>

      <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-amber-900 dark:text-amber-100 mb-2">
          Test Komutu:
        </h4>
        <div className="text-xs text-amber-800 dark:text-amber-200 space-y-1">
          <p>IPMI bağlantısını test etmek için:</p>
          <code className="block bg-amber-100 dark:bg-amber-900/30 p-2 rounded mt-1">
            ipmitool -I lanplus -H [IP] -U [USER] -P [PASS] sensor list
          </code>
        </div>
      </div>
    </div>
  );
};

export default IPMIMonitorSettings;
