import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * Sistem monitoring ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const SystemMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  // Platform değiştiğinde ayarları güncelle
  const handlePlatformChange = (platform) => {
    let updatedSettings = {
      ...settings,
      platform: platform
    };

    // Platform özel varsayılan değerler
    if (platform === 'windows') {
      updatedSettings = {
        ...updatedSettings,
        timeout: settings.timeout || 30,
        // Windows kimlik bilgileri gerekli
        username: settings.username || '',
        password: settings.password || '',
        domain: settings.domain || ''
      };
    } else if (platform === 'linux') {
      updatedSettings = {
        ...updatedSettings,
        port: settings.port || 22,
        timeout: settings.timeout || 30,
        // Linux kimlik bilgileri gerekli
        username: settings.username || '',
        password: settings.password || '',
        privateKey: settings.privateKey || ''
      };
    } else if (platform === 'local') {
      // Local için kimlik bilgileri gerekli değil
      updatedSettings = {
        ...updatedSettings,
        timeout: settings.timeout || 10
      };
      // Kimlik bilgilerini temizle
      delete updatedSettings.username;
      delete updatedSettings.password;
      delete updatedSettings.domain;
      delete updatedSettings.privateKey;
      delete updatedSettings.port;
    }

    onChange(updatedSettings);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="platform">
          Platform <span className="text-destructive">*</span>
        </Label>
        <Select
          value={settings.platform || ''}
          onValueChange={handlePlatformChange}
        >
          <SelectTrigger id="platform">
            <SelectValue placeholder="Platform seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="local">Local Sistem (Önerilen)</SelectItem>
            <SelectItem value="windows">Windows</SelectItem>
            <SelectItem value="linux">Linux</SelectItem>
          </SelectContent>
        </Select>
        {errors.platform && (
          <p className="text-xs text-destructive">{errors.platform}</p>
        )}
        <p className="text-xs text-muted-foreground">
          İzlenecek sistemin türünü seçin
        </p>
      </div>

      {settings.platform === 'windows' && (
        <>
          <div className="space-y-2">
            <Label htmlFor="username">
              Kullanıcı Adı <span className="text-destructive">*</span>
            </Label>
            <Input
              id="username"
              name="username"
              value={settings.username || ''}
              onChange={handleInputChange}
              placeholder="Administrator"
              className={errors.username ? "border-destructive" : ""}
            />
            {errors.username && (
              <p className="text-xs text-destructive">{errors.username}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Windows kimlik doğrulama kullanıcı adı
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">
              Şifre <span className="text-destructive">*</span>
            </Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={settings.password || ''}
              onChange={handleInputChange}
              placeholder="••••••••"
              className={errors.password ? "border-destructive" : ""}
            />
            {errors.password && (
              <p className="text-xs text-destructive">{errors.password}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Windows kimlik doğrulama şifresi
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="domain">Domain (Opsiyonel)</Label>
            <Input
              id="domain"
              name="domain"
              value={settings.domain || ''}
              onChange={handleInputChange}
              placeholder="DOMAIN"
            />
            <p className="text-xs text-muted-foreground">
              Windows domain adı (opsiyonel)
            </p>
          </div>
        </>
      )}

      {settings.platform === 'linux' && (
        <>
          <div className="space-y-2">
            <Label htmlFor="username">
              Kullanıcı Adı <span className="text-destructive">*</span>
            </Label>
            <Input
              id="username"
              name="username"
              value={settings.username || ''}
              onChange={handleInputChange}
              placeholder="monitoruser"
              className={errors.username ? "border-destructive" : ""}
            />
            {errors.username && (
              <p className="text-xs text-destructive">{errors.username}</p>
            )}
            <p className="text-xs text-muted-foreground">
              SSH bağlantısı için kullanıcı adı
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">
              Şifre <span className="text-destructive">*</span>
            </Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={settings.password || ''}
              onChange={handleInputChange}
              placeholder="••••••••"
              className={errors.password ? "border-destructive" : ""}
            />
            {errors.password && (
              <p className="text-xs text-destructive">{errors.password}</p>
            )}
            <p className="text-xs text-muted-foreground">
              SSH bağlantısı için şifre
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="port">SSH Port</Label>
            <Select
              value={String(settings.port || '22')}
              onValueChange={(value) => handleSelectChange('port', parseInt(value))}
            >
              <SelectTrigger id="port">
                <SelectValue placeholder="SSH port seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="22">22 (Varsayılan)</SelectItem>
                <SelectItem value="2222">2222</SelectItem>
                <SelectItem value="2022">2022</SelectItem>
                <SelectItem value="22000">22000</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              SSH bağlantı port numarası
            </p>
          </div>
        </>
      )}

      <div className="space-y-2">
        <Label htmlFor="timeout">Timeout (saniye)</Label>
        <Select
          value={String(settings.timeout || (settings.platform === 'local' ? '10' : '30'))}
          onValueChange={(value) => handleSelectChange('timeout', parseInt(value))}
        >
          <SelectTrigger id="timeout">
            <SelectValue placeholder="Timeout seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 saniye</SelectItem>
            <SelectItem value="10">10 saniye</SelectItem>
            <SelectItem value="15">15 saniye</SelectItem>
            <SelectItem value="30">30 saniye</SelectItem>
            <SelectItem value="60">60 saniye</SelectItem>
            <SelectItem value="120">2 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Sistem monitoring bağlantı timeout süresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '30'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
            <SelectItem value="120">2 saat</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Sistem monitoring kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default SystemMonitorSettings;
