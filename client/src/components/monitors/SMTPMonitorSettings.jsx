import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Switch } from "../ui/switch";

/**
 * SMTP/Email izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const SMTPMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  // Switch değişikliklerini işle
  const handleSwitchChange = (name, checked) => {
    onChange({ [name]: checked });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="host">
          SMTP Sunucu <span className="text-destructive">*</span>
        </Label>
        <Input
          id="host"
          name="host"
          value={settings.host || ''}
          onChange={handleInputChange}
          placeholder="Örn: smtp.gmail.com"
          className={errors.host ? "border-destructive" : ""}
        />
        {errors.host && (
          <p className="text-xs text-destructive">{errors.host}</p>
        )}
        <p className="text-xs text-muted-foreground">
          SMTP sunucu adresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="port">SMTP Port</Label>
        <Select
          value={String(settings.port || '587')}
          onValueChange={(value) => handleSelectChange('port', parseInt(value))}
        >
          <SelectTrigger id="port">
            <SelectValue placeholder="Port seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="25">25 (SMTP)</SelectItem>
            <SelectItem value="587">587 (SMTP TLS)</SelectItem>
            <SelectItem value="465">465 (SMTP SSL)</SelectItem>
            <SelectItem value="2525">2525 (Alternative)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          SMTP sunucu port numarası
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="username">
          Kullanıcı Adı
        </Label>
        <Input
          id="username"
          name="username"
          value={settings.username || ''}
          onChange={handleInputChange}
          placeholder="Örn: <EMAIL>"
          className={errors.username ? "border-destructive" : ""}
        />
        {errors.username && (
          <p className="text-xs text-destructive">{errors.username}</p>
        )}
        <p className="text-xs text-muted-foreground">
          SMTP kimlik doğrulama kullanıcı adı
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="password">
          Şifre
        </Label>
        <Input
          id="password"
          name="password"
          type="password"
          value={settings.password || ''}
          onChange={handleInputChange}
          placeholder="SMTP şifresi"
          className={errors.password ? "border-destructive" : ""}
        />
        {errors.password && (
          <p className="text-xs text-destructive">{errors.password}</p>
        )}
        <p className="text-xs text-muted-foreground">
          SMTP kimlik doğrulama şifresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="from">
          Gönderen E-posta
        </Label>
        <Input
          id="from"
          name="from"
          value={settings.from || ''}
          onChange={handleInputChange}
          placeholder="Örn: <EMAIL>"
          className={errors.from ? "border-destructive" : ""}
        />
        {errors.from && (
          <p className="text-xs text-destructive">{errors.from}</p>
        )}
        <p className="text-xs text-muted-foreground">
          Test e-postası gönderen adresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="to">
          Alıcı E-posta
        </Label>
        <Input
          id="to"
          name="to"
          value={settings.to || ''}
          onChange={handleInputChange}
          placeholder="Örn: <EMAIL>"
          className={errors.to ? "border-destructive" : ""}
        />
        {errors.to && (
          <p className="text-xs text-destructive">{errors.to}</p>
        )}
        <p className="text-xs text-muted-foreground">
          Test e-postası alıcı adresi
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="secure"
            checked={settings.secure || false}
            onCheckedChange={(checked) => handleSwitchChange('secure', checked)}
          />
          <Label htmlFor="secure">SSL/TLS Kullan</Label>
        </div>
        <p className="text-xs text-muted-foreground">
          Güvenli bağlantı kullanılsın mı?
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <Switch
            id="ignoreTLS"
            checked={settings.ignoreTLS || false}
            onCheckedChange={(checked) => handleSwitchChange('ignoreTLS', checked)}
          />
          <Label htmlFor="ignoreTLS">TLS Hatalarını Yoksay</Label>
        </div>
        <p className="text-xs text-muted-foreground">
          TLS sertifika hatalarını yoksay (test ortamları için)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="timeout">Timeout (saniye)</Label>
        <Select
          value={String(settings.timeout || '10')}
          onValueChange={(value) => handleSelectChange('timeout', parseInt(value))}
        >
          <SelectTrigger id="timeout">
            <SelectValue placeholder="Timeout seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 saniye</SelectItem>
            <SelectItem value="10">10 saniye</SelectItem>
            <SelectItem value="15">15 saniye</SelectItem>
            <SelectItem value="30">30 saniye</SelectItem>
            <SelectItem value="60">60 saniye</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          SMTP bağlantı timeout süresi
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '30'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="10">10 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
            <SelectItem value="120">2 saat</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          SMTP kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default SMTPMonitorSettings;
