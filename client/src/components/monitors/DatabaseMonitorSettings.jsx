import React from 'react';
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * Veritabanı izleme türü ayarları bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.settings - Mevcut ayarlar
 * @param {Function} props.onChange - Ayarlar değiştiğinde çağrılacak fonksiyon
 * @param {Object} props.errors - Form hataları
 */
const DatabaseMonitorSettings = ({ settings, onChange, errors = {} }) => {
  // Input değişikliklerini işle
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onChange({ [name]: value });
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    onChange({ [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="type">Veritabanı Türü</Label>
        <Select
          value={settings.type || 'mysql'}
          onValueChange={(value) => handleSelectChange('type', value)}
        >
          <SelectTrigger id="type">
            <SelectValue placeholder="Veritabanı türü seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="mysql">MySQL</SelectItem>
            <SelectItem value="postgresql">PostgreSQL</SelectItem>
            <SelectItem value="mongodb">MongoDB</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="host">Sunucu Adresi</Label>
        <Input
          id="host"
          name="host"
          value={settings.host || ''}
          onChange={handleInputChange}
          placeholder="Boş bırakılırsa cihaz adresi kullanılır"
        />
        <p className="text-xs text-muted-foreground">
          Veritabanı sunucusunun adresi (boş bırakılırsa cihazın host adresi kullanılır)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="port">Port</Label>
        <Input
          id="port"
          name="port"
          value={settings.port || ''}
          onChange={handleInputChange}
          placeholder="Boş bırakılırsa varsayılan port kullanılır"
        />
        <p className="text-xs text-muted-foreground">
          Veritabanı port numarası (boş bırakılırsa varsayılan port kullanılır)
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="database">
          Veritabanı Adı <span className="text-destructive">*</span>
        </Label>
        <Input
          id="database"
          name="database"
          value={settings.database || ''}
          onChange={handleInputChange}
          placeholder="Örn: mydb"
          className={errors.database ? "border-destructive" : ""}
        />
        {errors.database && (
          <p className="text-xs text-destructive">{errors.database}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="user">
          Kullanıcı Adı <span className="text-destructive">*</span>
        </Label>
        <Input
          id="user"
          name="user"
          value={settings.user || ''}
          onChange={handleInputChange}
          placeholder="Örn: dbuser"
          className={errors.user ? "border-destructive" : ""}
        />
        {errors.user && (
          <p className="text-xs text-destructive">{errors.user}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="password">Şifre</Label>
        <Input
          id="password"
          name="password"
          type="password"
          value={settings.password || ''}
          onChange={handleInputChange}
          placeholder="••••••••"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="interval">Kontrol Aralığı</Label>
        <Select
          value={settings.interval || '15'}
          onValueChange={(value) => handleSelectChange('interval', value)}
        >
          <SelectTrigger id="interval">
            <SelectValue placeholder="Kontrol aralığı seçin" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 dakika</SelectItem>
            <SelectItem value="15">15 dakika</SelectItem>
            <SelectItem value="30">30 dakika</SelectItem>
            <SelectItem value="60">60 dakika</SelectItem>
            <SelectItem value="120">120 dakika</SelectItem>
            <SelectItem value="240">240 dakika</SelectItem>
            <SelectItem value="360">360 dakika</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Veritabanı kontrolü ne sıklıkla yapılacak?
        </p>
      </div>
    </div>
  );
};

export default DatabaseMonitorSettings;
