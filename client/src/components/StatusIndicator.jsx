import React from 'react';
import { Badge } from './ui/badge';
import {
  STATUS_TYPES,
  STATUS_LABELS,
  STATUS_COLORS,
  STATUS_BG_COLORS,
  STATUS_VARIANTS,
  NOTIFICATION_BADGE_STYLES,
  DOT_SIZES,
  getStatusIcon
} from '../lib/theme';

/**
 * Merkezi durum gösterge bileşeni
 * @param {string} status - Durum değeri (up, down, warning, critical, degraded, flapping, partial, unknown)
 * @param {string} type - Gösterge tipi (icon, badge, dot, text)
 * @param {string} size - Boyut (sm, md, lg)
 * @param {boolean} withLabel - Etiket gösterilsin mi?
 * @param {string} className - Ek CSS sınıfları
 */
const StatusIndicator = ({ status, type = 'badge', size = 'md', withLabel = true, className = '' }) => {

  // Boyut sınıfları - tema dosyasından al
  const getDotSize = () => {
    return DOT_SIZES[size] || DOT_SIZES['md'];
  };

  // Gösterge tipine göre render
  switch (type) {
    case 'icon':
      return getStatusIcon(status, size);

    case 'badge':
      // Bildirim durumları için özel stiller - tema dosyasından al
      if ([STATUS_TYPES.NEW, STATUS_TYPES.READ, STATUS_TYPES.ACKNOWLEDGED, STATUS_TYPES.RESOLVED].includes(status)) {
        // Tema dosyasından stil al veya varsayılan stil kullan
        const badgeStyle = NOTIFICATION_BADGE_STYLES[status] || 'flex items-center gap-1.5';

        return (
          <Badge
            variant={STATUS_VARIANTS[status] || 'secondary'}
            className={`${badgeStyle} ${className} py-1 px-3 font-normal`}
          >
            {getStatusIcon(status, 'sm', true)}
            {STATUS_LABELS[status || STATUS_TYPES.UNKNOWN]}
          </Badge>
        );
      }

      // Diğer durumlar için normal badge
      return (
        <Badge
          variant={STATUS_VARIANTS[status] || 'secondary'}
          className={`flex items-center gap-1 ${className}`}
        >
          {withLabel && getStatusIcon(status, 'sm', true)}
          {withLabel && STATUS_LABELS[status || STATUS_TYPES.UNKNOWN]}
        </Badge>
      );

    case 'dot':
      return (
        <div className={`flex items-center gap-1 ${className}`}>
          <div
            className={`${getDotSize()} rounded-full ${STATUS_BG_COLORS[status || STATUS_TYPES.UNKNOWN]}`}
          />
          {withLabel && (
            <span className={STATUS_COLORS[status || STATUS_TYPES.UNKNOWN]}>
              {STATUS_LABELS[status || STATUS_TYPES.UNKNOWN]}
            </span>
          )}
        </div>
      );

    case 'text':
      return (
        <span className={`${STATUS_COLORS[status || STATUS_TYPES.UNKNOWN]} ${className}`}>
          {STATUS_LABELS[status || STATUS_TYPES.UNKNOWN]}
        </span>
      );

    default:
      return null;
  }
};

export default StatusIndicator;
