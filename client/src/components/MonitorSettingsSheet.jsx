import React, { useState, useEffect } from 'react';
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, Sheet<PERSON>it<PERSON>, SheetFooter } from "./ui/sheet";
import { But<PERSON> } from "./ui/button";
import { ScrollArea } from "./ui/scroll-area";
import { AlertCircle } from 'lucide-react';
import { deviceService, settingsService } from '../services/api';
import { useSocket } from '../contexts/SocketContext';

// İzleme türü ayarları bileşenleri
import ICMPMonitorSettings from './monitors/ICMPMonitorSettings';
import TCPMonitorSettings from './monitors/TCPMonitorSettings';
import HTTPMonitorSettings from './monitors/HTTPMonitorSettings';
import DNSMonitorSettings from './monitors/DNSMonitorSettings';
import SSLMonitorSettings from './monitors/SSLMonitorSettings';
import SNMPMonitorSettings from './monitors/SNMPMonitorSettings';
import DatabaseMonitorSettings from './monitors/DatabaseMonitorSettings';
import APIMonitorSettings from './monitors/APIMonitorSettings';
import SMTPMonitorSettings from './monitors/SMTPMonitorSettings';
import SystemMonitorSettings from './monitors/SystemMonitorSettings';
import IPMIMonitorSettings from './monitors/IPMIMonitorSettings';
import DockerMonitorSettings from './monitors/DockerMonitorSettings';

/**
 * İzleme türü ayarları sheet bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.open - Sheet açık mı?
 * @param {Function} props.onOpenChange - Sheet açık/kapalı durumu değiştiğinde çağrılacak fonksiyon
 * @param {string} props.monitorType - İzleme türü (tcp, http, dns, ssl, snmp, database, api)
 * @param {string} props.deviceId - Cihaz ID'si
 * @param {Object} props.currentSettings - Mevcut izleme türü ayarları
 * @param {Function} props.onSuccess - Ayarlar başarıyla kaydedildiğinde çağrılacak fonksiyon
 */
const MonitorSettingsSheet = ({ open, onOpenChange, monitorType, deviceId, currentSettings, onSuccess }) => {
  // Socket bağlantısını al
  const socket = useSocket();

  // Ayarlar state'i
  const [settings, setSettings] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Varsayılan ayarları saklamak için state
  const [defaultSettings, setDefaultSettings] = useState({
    defaultPingInterval: '5',
    defaultHttpInterval: '10',
    defaultDnsInterval: '10',
    defaultSslInterval: '60',
    defaultTcpInterval: '5',
    defaultSnmpInterval: '5',
    defaultDatabaseInterval: '10',
    defaultApiInterval: '10',
    defaultSmtpInterval: '15',
    defaultSystemInterval: '30',
    defaultWindowsInterval: '15',
    defaultLinuxInterval: '15',
    defaultIpmiInterval: '30',
    defaultDockerInterval: '15',
    defaultDnsServer: '*******'
  });

  // İzleme türü başlıkları
  const monitorTitles = {
    icmp: 'ICMP Ping',
    tcp: 'TCP Port',
    http: 'HTTP/HTTPS',
    dns: 'DNS Sorgusu',
    ssl: 'SSL Sertifikası',
    snmp: 'SNMP',
    database: 'Veritabanı',
    api: 'REST API',
    smtp: 'SMTP E-posta',
    system: 'Sistem Monitoring',
    ipmi: 'IPMI Donanım',
    docker: 'Docker Konteyner',
    hardware: 'Hardware Monitoring'
  };

  // Varsayılan ayarları yükle
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await settingsService.getAll();
        setDefaultSettings({
          defaultPingInterval: settings.defaultPingInterval || '5',
          defaultHttpInterval: settings.defaultHttpInterval || '10',
          defaultDnsInterval: settings.defaultDnsInterval || '10',
          defaultSslInterval: settings.defaultSslInterval || '60',
          defaultTcpInterval: settings.defaultTcpInterval || '5',
          defaultSnmpInterval: settings.defaultSnmpInterval || '5',
          defaultDatabaseInterval: settings.defaultDatabaseInterval || '10',
          defaultApiInterval: settings.defaultApiInterval || '10',
          defaultDnsServer: settings.defaultDnsServer || '*******'
        });
      } catch (err) {
        console.error('Ayarlar yüklenirken hata oluştu:', err);
      }
    };

    loadSettings();
  }, []);

  // İzleme türü değiştiğinde veya mevcut ayarlar değiştiğinde state'i güncelle
  useEffect(() => {
    if (monitorType) {
      // Varsayılan değerleri belirle
      const defaultIntervalKey = `default${monitorType.charAt(0).toUpperCase() + monitorType.slice(1)}Interval`;
      const defaultInterval = defaultSettings[defaultIntervalKey] || '5';

      // İzleme türüne göre varsayılan ayarları belirle
      let defaultValues = { interval: defaultInterval, method: monitorType };

      // HTTP için varsayılan değerler
      if (monitorType === 'http') {
        defaultValues = {
          ...defaultValues,
          method: 'GET',
          expectedStatus: 'any'
        };
      }

      // TCP için varsayılan değerler
      if (monitorType === 'tcp') {
        defaultValues = {
          ...defaultValues,
          port: '80'
        };
      }

      // SSL için varsayılan değerler
      if (monitorType === 'ssl') {
        defaultValues = {
          ...defaultValues,
          port: '443'
        };
      }

      // DNS izleme türü için varsayılan DNS sunucusu ayarını ekle
      if (monitorType === 'dns') {
        defaultValues = {
          ...defaultValues,
          server: defaultSettings.defaultDnsServer || '*******',
          recordType: 'A'
        };
      }

      // API için varsayılan değerler
      if (monitorType === 'api') {
        defaultValues = {
          ...defaultValues,
          method: 'GET',
          expectedStatus: 'any'
        };
      }

      // SMTP için varsayılan değerler
      if (monitorType === 'smtp') {
        defaultValues = {
          ...defaultValues,
          port: 587,
          secure: false,
          timeout: 10
        };
      }

      // System için varsayılan değerler
      if (monitorType === 'system') {
        defaultValues = {
          ...defaultValues,
          platform: 'local',
          timeout: 10
        };
      }

      // Windows için varsayılan değerler
      if (monitorType === 'windows') {
        defaultValues = {
          ...defaultValues,
          timeout: 30,
          method: 'windows'
        };
      }

      // Linux için varsayılan değerler
      if (monitorType === 'linux') {
        defaultValues = {
          ...defaultValues,
          port: 22,
          authMethod: 'password',
          timeout: 30
        };
      }

      // IPMI için varsayılan değerler
      if (monitorType === 'ipmi') {
        defaultValues = {
          ...defaultValues,
          port: 623,
          interface: 'lanplus',
          timeout: 15
        };
      }

      // Docker için varsayılan değerler
      if (monitorType === 'docker') {
        defaultValues = {
          ...defaultValues,
          port: 2376,
          secure: false,
          timeout: 15
        };
      }

      // Mevcut ayarlar varsa, varsayılan değerlerle birleştir (mevcut ayarlar öncelikli)
      if (currentSettings && Object.keys(currentSettings).length > 0) {
        // Mevcut ayarları temizle ve doğru formata çevir
        const cleanedCurrentSettings = { ...currentSettings };

        // HTTP için özel temizlik
        if (monitorType === 'http') {
          // Yanlış method değerini düzelt
          if (cleanedCurrentSettings.method === 'http') {
            cleanedCurrentSettings.method = 'GET';
          }
          // expectedStatus'u string'e çevir
          if (typeof cleanedCurrentSettings.expectedStatus === 'number') {
            cleanedCurrentSettings.expectedStatus = String(cleanedCurrentSettings.expectedStatus);
          }
          // statusCode varsa expectedStatus'a çevir
          if (cleanedCurrentSettings.statusCode && !cleanedCurrentSettings.expectedStatus) {
            cleanedCurrentSettings.expectedStatus = String(cleanedCurrentSettings.statusCode);
            delete cleanedCurrentSettings.statusCode;
          }
          // Gereksiz alanları temizle
          delete cleanedCurrentSettings.path;
          delete cleanedCurrentSettings.port;
        }

        const finalSettings = { ...defaultValues, ...cleanedCurrentSettings };
        setSettings(finalSettings);
      } else {
        // Mevcut ayarlar yoksa, sadece varsayılan değerleri kullan
        setSettings(defaultValues);
      }

      // HTTP ve API izleme türleri için cihaz bilgilerini getir ve URL ayarını güncelle
      if ((monitorType === 'http' || monitorType === 'api') && deviceId && (!currentSettings || !currentSettings.url)) {
        deviceService.getById(deviceId)
          .then(device => {
            if (device.host) {
              const protocol = 'http'; // Varsayılan olarak HTTP kullan
              const path = monitorType === 'api' ? '/api' : '';
              const url = `${protocol}://${device.host}${path}`;
              setSettings(prevSettings => ({
                ...prevSettings,
                url: url
              }));
            }
          })
          .catch(err => console.error('Error fetching device for HTTP/API settings:', err));
      }

      // DNS izleme türü için cihaz bilgilerini getir ve domain ayarını güncelle
      if (monitorType === 'dns' && deviceId && (!currentSettings || !currentSettings.domain)) {
        deviceService.getById(deviceId)
          .then(device => {
            // Eğer host bir IP adresi değilse ve domain ayarı yoksa, host'u domain olarak kullan
            if (device.host && !device.host.match(/^\d+\.\d+\.\d+\.\d+$/)) {
              setSettings(prevSettings => ({
                ...prevSettings,
                domain: device.host
              }));
            }
            // Eğer host bir IP adresi ise, domain alanını boş bırak ve kullanıcıdan girmesini bekle
            // Bu durumda backend tarafında bir hata mesajı gösterilecek
          })
          .catch(err => console.error('Error fetching device for DNS settings:', err));
      }
    }
  }, [monitorType, currentSettings, deviceId, defaultSettings]);

  // Ayarlar değiştiğinde çağrılacak fonksiyon
  const handleSettingsChange = (newSettings) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      ...newSettings
    }));

    // Hata mesajlarını temizle
    if (Object.keys(formErrors).length > 0) {
      setFormErrors({});
    }
  };

  // Ayarları doğrula
  const validateSettings = () => {
    const errors = {};

    // TCP ayarları doğrulama
    if (monitorType === 'tcp') {
      if (!settings.port) {
        errors.port = 'Port numarası gereklidir';
      } else if (isNaN(settings.port) || parseInt(settings.port) < 1 || parseInt(settings.port) > 65535) {
        errors.port = 'Geçerli bir port numarası girin (1-65535)';
      }
    }

    // HTTP ayarları doğrulama
    if (monitorType === 'http') {
      if (!settings.url) {
        errors.url = 'URL gereklidir';
      }
    }

    // DNS ayarları doğrulama
    if (monitorType === 'dns') {
      if (!settings.domain) {
        errors.domain = 'Domain gereklidir';
      } else if (settings.domain.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        errors.domain = 'IP adresi değil, bir domain adı girilmelidir (örn: example.com)';
      }
    }

    // Veritabanı ayarları doğrulama
    if (monitorType === 'database') {
      if (!settings.database) {
        errors.database = 'Veritabanı adı gereklidir';
      }
      if (!settings.user) {
        errors.user = 'Kullanıcı adı gereklidir';
      }
    }

    // API ayarları doğrulama
    if (monitorType === 'api') {
      if (!settings.url) {
        errors.url = 'API URL gereklidir';
      }
    }

    // SMTP ayarları doğrulama
    if (monitorType === 'smtp') {
      if (!settings.host) {
        errors.host = 'SMTP sunucu gereklidir';
      }
      if (!settings.from) {
        errors.from = 'Gönderen e-posta gereklidir';
      }
      if (!settings.to) {
        errors.to = 'Alıcı e-posta gereklidir';
      }
    }

    // System ayarları doğrulama
    if (monitorType === 'system') {
      if (!settings.platform) {
        errors.platform = 'Platform seçimi gereklidir';
      }
      if (settings.platform === 'windows') {
        if (!settings.username) {
          errors.username = 'Windows kullanıcı adı gereklidir';
        }
        if (!settings.password) {
          errors.password = 'Windows şifresi gereklidir';
        }
      }
      if (settings.platform === 'linux') {
        if (!settings.username) {
          errors.username = 'Linux kullanıcı adı gereklidir';
        }
        if (!settings.password) {
          errors.password = 'Linux şifresi gereklidir';
        }
      }
    }

    // Windows ayarları doğrulama
    if (monitorType === 'windows') {
      if (!settings.username) {
        errors.username = 'Kullanıcı adı gereklidir';
      }
      if (!settings.password) {
        errors.password = 'Şifre gereklidir';
      }
    }

    // Linux ayarları doğrulama
    if (monitorType === 'linux') {
      if (!settings.username) {
        errors.username = 'Kullanıcı adı gereklidir';
      }
      if (settings.authMethod === 'password' && !settings.password) {
        errors.password = 'Şifre gereklidir';
      }
      if (settings.authMethod === 'privateKey' && !settings.privateKey) {
        errors.privateKey = 'SSH private key gereklidir';
      }
    }

    // IPMI ayarları doğrulama
    if (monitorType === 'ipmi') {
      if (!settings.username) {
        errors.username = 'IPMI kullanıcı adı gereklidir';
      }
      if (!settings.password) {
        errors.password = 'IPMI şifresi gereklidir';
      }
    }

    // Docker ayarları doğrulama
    if (monitorType === 'docker') {
      if (!settings.host) {
        errors.host = 'Docker host gereklidir';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Ayarları kaydet
  const handleSave = async () => {
    if (!validateSettings()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Cihazı getir
      const device = await deviceService.getById(deviceId);

      // İzleme türü ayarlarını güncelle
      const updatedMonitors = {
        ...device.monitors,
        [monitorType]: {
          ...settings,
          enabled: true
        }
      };

      // Güncellenmiş cihaz verisi
      const updatedDevice = {
        ...device,
        monitors: updatedMonitors
      };

      // Cihazı güncelle
      await deviceService.update(deviceId, updatedDevice);

      // Socket bağlantısı varsa, diğer kullanıcılara bildirim gönder
      if (socket && socket.connected) {
        console.log('Emitting device:update event via socket');
        socket.emit('device:update', {
          id: deviceId,
          monitors: updatedMonitors
        });
      }

      // Başarılı olduğunda
      setIsSubmitting(false);
      onOpenChange(false);

      // Başarı callback'ini çağır
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error saving monitor settings:', err);
      setError('Ayarlar kaydedilirken bir hata oluştu.');
      setIsSubmitting(false);
    }
  };

  // İzleme türüne göre ayarlar bileşenini render et
  const renderSettingsComponent = () => {
    switch (monitorType) {
      case 'icmp':
        return <ICMPMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'tcp':
        return <TCPMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'http':
        return <HTTPMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'dns':
        return <DNSMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'ssl':
        return <SSLMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'snmp':
        return <SNMPMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'database':
        return <DatabaseMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'api':
        return <APIMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'smtp':
        return <SMTPMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'system':
        return <SystemMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'ipmi':
        return <IPMIMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'docker':
        return <DockerMonitorSettings settings={settings} onChange={handleSettingsChange} errors={formErrors} />;
      case 'hardware':
        return <div className="text-center py-8 text-muted-foreground">Hardware monitoring ayarları yakında eklenecek</div>;
      default:
        return <div>Bilinmeyen izleme türü</div>;
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[500px] flex flex-col h-full p-0" side="right">
        <SheetHeader className="flex-shrink-0 p-6 pb-0">
          <SheetTitle>{monitorTitles[monitorType] || 'İzleme'} Ayarları</SheetTitle>
        </SheetHeader>

        <ScrollArea className="flex-1 px-6 min-h-0">
          {error && (
            <div className="bg-destructive/15 text-destructive p-3 rounded-md flex items-start space-x-2 my-4">
              <AlertCircle className="h-5 w-5 mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          <div className="py-4">
            {renderSettingsComponent()}
          </div>
        </ScrollArea>

        <SheetFooter className="flex-shrink-0 p-6 pt-4 border-t bg-background">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            İptal
          </Button>
          <Button onClick={handleSave} disabled={isSubmitting}>
            {isSubmitting ? 'Kaydediliyor...' : 'Kaydet'}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default MonitorSettingsSheet;
