import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import {
  Activity,
  AlertCircle,
  Battery,
  Building2,
  Cloud,
  Cog,
  Cpu,
  Database,
  Globe,
  Globe2,
  HardDrive,
  Mail,
  Monitor,
  Network,
  Package,
  Radio,
  RefreshCw,
  Router,
  Search,
  Server,
  ServerCrash,
  Share2,
  Shield,
  Terminal,
  Webhook,
  Wifi
} from 'lucide-react';
import { deviceService, settingsService } from '../services/api';
import { getFormattedSubCategory } from '../utils/categoryUtils';
import { notificationService } from '../services/notification-service';

/**
 * <PERSON><PERSON> cihaz ekleme dialog bileşeni
 * @param {Object} props - Bileşen özellikleri
 * @param {boolean} props.open - Dialog açık mı?
 * @param {Function} props.onOpenChange - Dialog açık/kapalı durumu değiştiğinde çağrılacak fonksiyon
 * @param {Function} props.onSuccess - Cihaz başarıyla eklendiğinde çağrılacak fonksiyon
 */
const DeviceAddDialog = ({ open, onOpenChange, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    host: '',
    group: '',
    platform: 'linux', // Varsayılan platform
    description: '',
    location: ''
  });

  // Form validation
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Platform seçimi gerekli mi kontrol et
  const needsPlatformSelection = (category) => {
    if (!category) return false;

    // Sadece fiziksel/sanal sunucular ve web servisleri platform seçimi gerektirir
    const platformCategories = [
      'Sunucular/Fiziksel',
      'Sunucular/Sanal',
      'Web/WebServer',
      'Web/API',
      'Web/Mail'
    ];

    return platformCategories.includes(category);
  };

  // Platform seçeneklerini kategori bazlı getir
  const getPlatformOptions = (category) => {
    if (!category) return [];

    // Sadece Sunucular ve Web kategorileri için Windows/Linux
    if (category.startsWith('Sunucular/') || category.startsWith('Web/')) {
      return [
        { value: 'windows', label: 'Windows Server', icon: Monitor },
        { value: 'linux', label: 'Linux Server', icon: Terminal }
      ];
    }

    return [];
  };
  const [error, setError] = useState(null);

  // Varsayılan ayarları saklamak için state
  const [defaultSettings, setDefaultSettings] = useState({
    defaultPingInterval: '5',
    defaultHttpInterval: '10',
    defaultDnsInterval: '10',
    defaultSslInterval: '60',
    defaultTcpInterval: '5',
    defaultSnmpInterval: '5',
    defaultDatabaseInterval: '10',
    defaultApiInterval: '10',
    defaultSmtpInterval: '15',
    defaultSystemInterval: '30',
    defaultDockerInterval: '15',
    defaultDnsServer: '8.8.8.8'
  });

  // Ayarları yükle
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await settingsService.getAll();
        setDefaultSettings({
          defaultPingInterval: settings.defaultPingInterval || '5',
          defaultHttpInterval: settings.defaultHttpInterval || '10',
          defaultDnsInterval: settings.defaultDnsInterval || '10',
          defaultSslInterval: settings.defaultSslInterval || '60',
          defaultTcpInterval: settings.defaultTcpInterval || '5',
          defaultSnmpInterval: settings.defaultSnmpInterval || '5',
          defaultDatabaseInterval: settings.defaultDatabaseInterval || '10',
          defaultApiInterval: settings.defaultApiInterval || '10',
          defaultSmtpInterval: settings.defaultSmtpInterval || '15',
          defaultSystemInterval: settings.defaultSystemInterval || '30',
          defaultDockerInterval: settings.defaultDockerInterval || '15',
          defaultDnsServer: settings.defaultDnsServer || '8.8.8.8'
        });
      } catch (err) {
        console.error('Ayarlar yüklenirken hata oluştu:', err);
      }
    };

    loadSettings();
  }, []);

  // Form değişikliklerini işle
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Hata mesajını temizle
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Select değişikliklerini işle
  const handleSelectChange = (name, value) => {
    const newFormData = {
      ...formData,
      [name]: value
    };

    // Kategori değiştiğinde platform'u sıfırla
    if (name === 'group') {
      newFormData.platform = '';
    }

    setFormData(newFormData);

    // Hata mesajını temizle
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Formu doğrula
  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Cihaz adı gereklidir';
    }

    if (!formData.host.trim()) {
      errors.host = 'Host adresi gereklidir';
    } else if (!/^[a-zA-Z0-9]([a-zA-Z0-9\-\.]+)?[a-zA-Z0-9]$|^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(formData.host)) {
      errors.host = 'Geçerli bir host adı veya IP adresi girin';
    }

    if (!formData.group || formData.group.trim() === '') {
      errors.group = 'Kategori seçimi zorunludur';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Formu gönder
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Cihaz verisi oluştur
      const deviceData = {
        name: formData.name,
        host: formData.host,
        group: formData.group,
        platform: formData.platform, // Platform bilgisini kaydet
        description: formData.description,
        location: formData.location,
        monitors: {
          icmp: {
            enabled: true,
            method: 'icmp',
            interval: defaultSettings.defaultPingInterval
          }
        }
      };

      // Kategori bazlı varsayılan izleyicileri ekle
      const category = formData.group.split('/')[0];
      const subCategory = formData.group.split('/')[1];

      // Web servisleri için özel izleyici atamaları
      if (category === 'Web') {
        // Tüm web servisleri için HTTP temel
        deviceData.monitors.http = {
          enabled: true,
          method: 'GET',
          expectedStatus: 'any',
          interval: defaultSettings.defaultHttpInterval
        };

        // Alt kategori bazlı özel atamalar
        switch (subCategory) {
          case 'WebServer':
            // Web sunucuları için SSL monitoring
            deviceData.monitors.ssl = {
              enabled: false, // Kullanıcı HTTPS varsa etkinleştirecek
              interval: '60', // 60 dakika
              port: 443
            };
            break;

          case 'API':
            // API servisleri için API monitoring
            deviceData.monitors.api = {
              enabled: false, // Kullanıcı yapılandıracak
              interval: defaultSettings.defaultApiInterval,
              method: 'GET',
              expectedStatus: 200
            };
            break;

          case 'Mail':
            // Mail sunucuları için SMTP ve DNS
            deviceData.monitors.smtp = {
              enabled: false, // Kullanıcı yapılandıracak
              interval: defaultSettings.defaultSmtpInterval,
              port: 25,
              protocol: 'smtp'
            };
            deviceData.monitors.dns = {
              enabled: false, // Kullanıcı yapılandıracak
              interval: '30', // 30 dakika
              domain: '', // Kullanıcı girecek
              server: 'system'
            };
            break;

          case 'DNS':
            // DNS sunucuları için DNS monitoring
            deviceData.monitors.dns = {
              enabled: true, // Otomatik etkin
              interval: '15', // 15 dakika
              domain: 'google.com', // Test domain
              server: 'system'
            };
            break;
        }
      }

      // Bu eski atamalar kaldırıldı - yeni mantık Web kategorisi içinde ve platform bazlı atamalarda

      // Kategori bazlı otomatik monitor ataması (platform seçimi olmayan kategoriler için)
      if (!needsPlatformSelection(formData.group)) {
        switch (formData.group) {
          case 'Sunucular/Container':
            // Container monitoring
            deviceData.monitors.docker = {
              enabled: true,
              interval: defaultSettings.defaultDockerInterval || '15'
            };
            break;

          case 'IoT/UPS':
            // UPS SNMP monitoring
            deviceData.monitors.snmp = {
              enabled: true,
              interval: defaultSettings.defaultSnmpInterval,
              community: 'public',
              version: '2c',
              port: 161
            };
            break;

          case 'IoT/Kamera':
            // Kamera HTTP monitoring
            deviceData.monitors.http = {
              enabled: true,
              interval: defaultSettings.defaultHttpInterval,
              method: 'GET',
              expectedStatus: 200
            };
            break;

          case 'IoT/Sensör':
          case 'IoT/PLC':
          case 'IoT/BinaSistemi':
            // IoT cihazları SNMP monitoring
            deviceData.monitors.snmp = {
              enabled: true,
              interval: defaultSettings.defaultSnmpInterval,
              community: 'public',
              version: '2c',
              port: 161
            };
            break;

          // Ağ cihazları için otomatik SNMP ataması
          case 'Ağ Cihazları/Router':
          case 'Ağ Cihazları/Switch':
          case 'Ağ Cihazları/Firewall':
          case 'Ağ Cihazları/AccessPoint':
          case 'Ağ Cihazları/Modem':
            // Ağ cihazları SNMP monitoring
            deviceData.monitors.snmp = {
              enabled: true,
              interval: defaultSettings.defaultSnmpInterval,
              community: 'public',
              version: '2c',
              port: 161
            };
            break;

          // Web servisleri için otomatik HTTP ataması
          case 'Web/CDN':
            // CDN HTTP monitoring
            deviceData.monitors.http = {
              enabled: true,
              interval: defaultSettings.defaultHttpInterval,
              method: 'GET',
              expectedStatus: 200
            };
            break;

          // Web/DNS zaten Web kategorisinde işleniyor, burada tekrar etmeyelim

          // Sunucu kategorileri için TCP port monitoring
          case 'Sunucular/Veritabanı':
            // Veritabanı sunucuları için database monitoring
            deviceData.monitors.database = {
              enabled: false, // Kullanıcı manuel etkinleştirecek
              interval: defaultSettings.defaultDatabaseInterval,
              type: 'mysql' // Varsayılan
            };
            deviceData.monitors.tcp = {
              enabled: true,
              interval: '5', // 5 dakika
              port: 3306 // MySQL varsayılan port
            };
            break;

          case 'Sunucular/Depolama':
            // Depolama cihazları için SNMP monitoring
            deviceData.monitors.snmp = {
              enabled: true,
              interval: defaultSettings.defaultSnmpInterval,
              community: 'public',
              version: '2c',
              port: 161
            };
            break;
        }
      }

      // Platform bazlı ana monitoring ataması (sadece platform seçimi olan kategoriler için)
      if (needsPlatformSelection(formData.group)) {
        const category = formData.group.split('/')[0];
        const subCategory = formData.group.split('/')[1];

        switch (formData.platform) {
        case 'windows':
          // Windows sistem monitoring (temel)
          deviceData.monitors.system = {
            enabled: true, // Otomatik etkin
            platform: 'windows',
            interval: defaultSettings.defaultSystemInterval
          };

          // Kategori bazlı ek izleyiciler
          if (category === 'Sunucular' && subCategory === 'Fiziksel') {
            deviceData.monitors.ipmi = { enabled: false, interval: '30' }; // IPMI fiziksel sunucularda
          }
          if (subCategory === 'Veritabanı') {
            deviceData.monitors.database = { enabled: false, interval: defaultSettings.defaultDatabaseInterval };
          }

          // Opsiyonel izleyiciler (kullanıcı etkinleştirecek)
          deviceData.monitors.snmp = { enabled: false, interval: defaultSettings.defaultSnmpInterval };
          deviceData.monitors.tcp = { enabled: false, interval: '5', port: 80 };
          deviceData.monitors.smtp = { enabled: false, interval: defaultSettings.defaultSmtpInterval };
          break;

        case 'linux':
          // Linux sistem monitoring (temel)
          deviceData.monitors.system = {
            enabled: true, // Otomatik etkin
            platform: 'linux',
            interval: defaultSettings.defaultSystemInterval
          };

          // Kategori bazlı ek izleyiciler
          if (category === 'Sunucular' && subCategory === 'Fiziksel') {
            deviceData.monitors.ipmi = { enabled: false, interval: '30' }; // IPMI fiziksel sunucularda
          }
          if (subCategory === 'Veritabanı') {
            deviceData.monitors.database = { enabled: false, interval: defaultSettings.defaultDatabaseInterval };
          }

          // Opsiyonel izleyiciler (kullanıcı etkinleştirecek)
          deviceData.monitors.docker = { enabled: false, interval: defaultSettings.defaultDockerInterval };
          deviceData.monitors.tcp = { enabled: false, interval: '5', port: 22 };
          deviceData.monitors.api = { enabled: false, interval: defaultSettings.defaultApiInterval };
          break;



        default:
          // Platform seçimi olmayan kategoriler için varsayılan ayarlar zaten yukarıda yapıldı
          break;
        }
      }

      // Cihazı oluştur
      const response = await deviceService.create(deviceData);

      // Başarılı olduğunda
      setIsSubmitting(false);
      onOpenChange(false);

      // Başarı bildirimi göster
      notificationService.success(`${formData.name} cihazı eklendi`, {
        description: 'Cihaz başarıyla eklendi ve izlenmeye başlandı.',
        persistent: true, // Bildirim panelinde de göster
        category: 'device'
      });

      // Formu sıfırla
      setFormData({
        name: '',
        host: '',
        group: '',
        platform: 'linux',
        description: '',
        location: ''
      });

      // Başarı callback'ini çağır
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error creating device:', err);
      setError('Cihaz eklenirken bir hata oluştu.');

      // Hata bildirimi göster
      notificationService.error('Cihaz eklenemedi', {
        description: err.response?.data?.message || 'Cihaz eklenirken bir hata oluştu.'
      });

      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Yeni Cihaz Ekle</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-destructive/15 text-destructive p-3 rounded-md flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 mt-0.5" />
            <span>{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Cihaz Adı <span className="text-destructive">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Örn: Web Sunucusu"
                  className={formErrors.name ? "border-destructive" : ""}
                />
                {formErrors.name && (
                  <p className="text-xs text-destructive">{formErrors.name}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="host" className="text-right">
                Host Adresi <span className="text-destructive">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="host"
                  name="host"
                  value={formData.host}
                  onChange={handleChange}
                  placeholder="Örn: *********** veya server.example.com"
                  className={formErrors.host ? "border-destructive" : ""}
                />
                {formErrors.host && (
                  <p className="text-xs text-destructive">{formErrors.host}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="group" className="text-right">
                Kategori <span className="text-destructive">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Select
                  value={formData.group}
                  onValueChange={(value) => handleSelectChange('group', value)}
                >
                  <SelectTrigger id="group" className={formErrors.group ? "border-destructive" : ""}>
                    <SelectValue placeholder="Kategori seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Ağ Cihazları Kategorisi */}
                    <SelectItem value="Ağ Cihazları" disabled className="font-semibold">
                      <div className="flex items-center gap-2">
                        <Network className="h-4 w-4 text-blue-500" />
                        <span className="font-semibold">Ağ Cihazları</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Router">
                      <div className="flex items-center gap-2 ml-4">
                        <Router className="h-4 w-4 text-blue-400" />
                        Router'lar
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Switch">
                      <div className="flex items-center gap-2 ml-4">
                        <Share2 className="h-4 w-4 text-blue-400" />
                        Switch'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Firewall">
                      <div className="flex items-center gap-2 ml-4">
                        <Shield className="h-4 w-4 text-blue-400" />
                        Firewall'lar
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/AccessPoint">
                      <div className="flex items-center gap-2 ml-4">
                        <Wifi className="h-4 w-4 text-blue-400" />
                        Access Point'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Ağ Cihazları/Modem">
                      <div className="flex items-center gap-2 ml-4">
                        <Radio className="h-4 w-4 text-blue-400" />
                        Modem'ler
                      </div>
                    </SelectItem>

                    {/* Sunucular & Hizmetler Kategorisi */}
                    <SelectItem value="Sunucular" disabled className="font-semibold">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-green-500" />
                        <span className="font-semibold">Sunucular & Hizmetler</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Fiziksel">
                      <div className="flex items-center gap-2 ml-4">
                        <ServerCrash className="h-4 w-4 text-green-400" />
                        Fiziksel Sunucular
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Sanal">
                      <div className="flex items-center gap-2 ml-4">
                        <Cloud className="h-4 w-4 text-green-400" />
                        Sanal Sunucular
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Container">
                      <div className="flex items-center gap-2 ml-4">
                        <Package className="h-4 w-4 text-green-400" />
                        Container'lar
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Veritabanı">
                      <div className="flex items-center gap-2 ml-4">
                        <Database className="h-4 w-4 text-green-400" />
                        Veri Tabanı Sunucuları
                      </div>
                    </SelectItem>
                    <SelectItem value="Sunucular/Depolama">
                      <div className="flex items-center gap-2 ml-4">
                        <HardDrive className="h-4 w-4 text-green-400" />
                        Depolama Cihazları
                      </div>
                    </SelectItem>

                    {/* Web & Uygulama Servisleri */}
                    <SelectItem value="Web" disabled className="font-semibold">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-purple-500" />
                        <span className="font-semibold">Web & Uygulama Servisleri</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/WebServer">
                      <div className="flex items-center gap-2 ml-4">
                        <Globe2 className="h-4 w-4 text-purple-400" />
                        Web Sunucuları
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/API">
                      <div className="flex items-center gap-2 ml-4">
                        <Webhook className="h-4 w-4 text-purple-400" />
                        API'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/Mail">
                      <div className="flex items-center gap-2 ml-4">
                        <Mail className="h-4 w-4 text-purple-400" />
                        Mail Sunucuları
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/CDN">
                      <div className="flex items-center gap-2 ml-4">
                        <Network className="h-4 w-4 text-purple-400" />
                        CDN'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="Web/DNS">
                      <div className="flex items-center gap-2 ml-4">
                        <Search className="h-4 w-4 text-purple-400" />
                        DNS Sunucuları
                      </div>
                    </SelectItem>

                    {/* IoT & Endüstriyel Cihazlar */}
                    <SelectItem value="IoT" disabled className="font-semibold">
                      <div className="flex items-center gap-2">
                        <Cpu className="h-4 w-4 text-orange-500" />
                        <span className="font-semibold">IoT & Endüstriyel Cihazlar</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/Sensör">
                      <div className="flex items-center gap-2 ml-4">
                        <Activity className="h-4 w-4 text-orange-400" />
                        Akıllı Sensörler
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/PLC">
                      <div className="flex items-center gap-2 ml-4">
                        <Cog className="h-4 w-4 text-orange-400" />
                        Endüstriyel PLC'ler
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/BinaSistemi">
                      <div className="flex items-center gap-2 ml-4">
                        <Building2 className="h-4 w-4 text-orange-400" />
                        Akıllı Bina Sistemleri
                      </div>
                    </SelectItem>
                    <SelectItem value="IoT/UPS">
                      <div className="flex items-center gap-2 ml-4">
                        <Battery className="h-4 w-4 text-orange-400" />
                        UPS & Güç Yönetim Cihazları
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {formErrors.group && (
                  <p className="text-xs text-destructive">{formErrors.group}</p>
                )}
              </div>
            </div>

            {/* Platform seçimi - sadece gerekli kategorilerde göster */}
            {needsPlatformSelection(formData.group) && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="platform" className="text-right">
                  Platform
                </Label>
                <div className="col-span-3 space-y-1">
                  <Select
                    value={formData.platform}
                    onValueChange={(value) => handleSelectChange('platform', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Platform seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      {getPlatformOptions(formData.group).map((option) => {
                        const IconComponent = option.icon;
                        return (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <IconComponent className="h-4 w-4" />
                              {option.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Platform seçimi sistem monitoring türünü belirler (Windows WMI / Linux SSH)
                  </p>
                  {formErrors.platform && (
                    <p className="text-xs text-destructive">{formErrors.platform}</p>
                  )}
                </div>
              </div>
            )}

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Açıklama
              </Label>
              <div className="col-span-3">
                <Input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Cihaz hakkında kısa açıklama (isteğe bağlı)"
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                Konum/Bölge
              </Label>
              <div className="col-span-3">
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  placeholder="Cihazın fiziksel konumu (isteğe bağlı)"
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              İptal
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> Ekleniyor...
                </>
              ) : (
                'Ekle'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DeviceAddDialog;
