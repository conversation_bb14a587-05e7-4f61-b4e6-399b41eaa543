import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';

/**
 * <PERSON>zleme türü için son kontrol zamanı ve sonraki kontrol zamanını gösteren bileşen
 * @param {Object} props - Bileşen özellikleri
 * @param {Object} props.monitorData - İzleme türü verileri
 */
const MonitorTimesDisplay = ({ monitorData }) => {
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [isMonitoring, setIsMonitoring] = useState(false);
  const socket = useSocket();

  // Her saniye güncel zamanı güncelle
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Socket.io olaylarını dinle
  useEffect(() => {
    if (!monitorData || !monitorData.deviceId) return;

    const deviceId = monitorData.deviceId;
    const type = monitorData.type;

    // Kontrol başladı olayını dinle
    const handleMonitoringStart = (data) => {
      if (data.deviceId === deviceId && data.monitorTypes.includes(type)) {
        setIsMonitoring(true);
      }
    };

    // Kontrol tamamlandı olayını dinle
    const handleMonitoringComplete = (data) => {
      if (data.deviceId === deviceId && data.monitorTypes.includes(type)) {
        setIsMonitoring(false);
      }
    };

    socket.on('device:monitoring:start', handleMonitoringStart);
    socket.on('device:monitoring:complete', handleMonitoringComplete);

    return () => {
      socket.off('device:monitoring:start', handleMonitoringStart);
      socket.off('device:monitoring:complete', handleMonitoringComplete);
    };
  }, [monitorData]);

  if (!monitorData) return null;

  // Sadece lastCheck ve nextCheck alanlarını kullan
  const lastCheck = monitorData.lastCheck;
  const nextCheck = monitorData.nextCheck;

  // Zaman değerlerini sayıya çevir
  const lastCheckTime = lastCheck ? parseInt(lastCheck) : null;
  const nextCheckTime = nextCheck ? parseInt(nextCheck) : null;

  // Sonraki kontrol zamanı geçti mi?
  const nextCheckPassed = nextCheckTime && nextCheckTime <= currentTime;

  // Debug bilgisi için konsola yazdır
  console.log('MonitorTimesDisplay:', {
    monitorType: monitorData.type || 'unknown',
    deviceId: monitorData.deviceId,
    lastCheck,
    nextCheck,
    lastCheckTime,
    nextCheckTime,
    currentTime,
    nextCheckPassed,
    isMonitoring,
    timeDiff: nextCheckTime ? nextCheckTime - currentTime : null
  });

  return (
    <>
      {lastCheckTime && (
        <span className="block text-xs mt-2 text-muted-foreground">
          <Clock className="inline-block h-3 w-3 mr-1" />
          Son kontrol: {new Date(lastCheckTime).toLocaleString()}
        </span>
      )}

      {nextCheckTime && !nextCheckPassed && !isMonitoring && (
        <span className="block text-xs mt-1 text-muted-foreground">
          <Clock className="inline-block h-3 w-3 mr-1" />
          Sonraki kontrol: {new Date(nextCheckTime).toLocaleString()}
        </span>
      )}

      {isMonitoring && (
        <span className="block text-xs mt-1 text-muted-foreground">
          <Clock className="inline-block h-3 w-3 mr-1" />
          Sonraki kontrol: Kontrol ediliyor...
        </span>
      )}

      {nextCheckTime && nextCheckPassed && !isMonitoring && (
        <span className="block text-xs mt-1 text-muted-foreground">
          <Clock className="inline-block h-3 w-3 mr-1" />
          Sonraki kontrol: Zamanı geldi, sırada...
        </span>
      )}

      {!nextCheckTime && lastCheckTime && !isMonitoring && (
        <span className="block text-xs mt-1 text-muted-foreground">
          <Clock className="inline-block h-3 w-3 mr-1" />
          Sonraki kontrol: Yeniden yükleniyor...
        </span>
      )}

      {!lastCheckTime && !isMonitoring && (
        <span className="block text-xs mt-1 text-muted-foreground">
          <Clock className="inline-block h-3 w-3 mr-1" />
          Henüz kontrol edilmedi
        </span>
      )}
    </>
  );
};

export default MonitorTimesDisplay;
