import React from 'react';
import { getSubCategoryIcon } from '../utils/categoryUtils';
import {
  Router,
  Share2,
  Shield,
  Wifi,
  Radio,
  ServerCrash,
  Cloud,
  Package,
  Database,
  HardDrive,
  Globe2,
  Webhook,
  Mail,
  Network,
  Search,
  Activity,
  Cog,
  Building2,
  Battery,
  Server,
  Globe,
  Cpu
} from 'lucide-react';

/**
 * Cihaz kategorisi için ikon bileşeni
 * @param {string} category - Cihaz kategorisi
 * @param {string} size - <PERSON>kon boyutu (sm, md, lg)
 * @param {boolean} useInheritedColor - Badge içinde kullanıldığında true olmalı
 * @param {string} className - Ek CSS sınıfları
 */
const DeviceCategoryIcon = ({ category, size = 'sm', useInheritedColor = false, className = '' }) => {
  // Boyut sınıfını belirle
  const sizeClass = size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : 'h-5 w-5';

  // Renk sınıfı (badge içinde kullanıldığında boş olmalı)
  const colorClass = useInheritedColor ? '' : 'text-muted-foreground';

  // İkon sınıfı
  const iconClass = `${sizeClass} ${colorClass} ${className}`;

  // Kategori türüne göre ikon döndür
  if (category?.startsWith('Ağ Cihazları/Router')) return <Router className={iconClass} />;
  if (category?.startsWith('Ağ Cihazları/Switch')) return <Share2 className={iconClass} />;
  if (category?.startsWith('Ağ Cihazları/Firewall')) return <Shield className={iconClass} />;
  if (category?.startsWith('Ağ Cihazları/AccessPoint')) return <Wifi className={iconClass} />;
  if (category?.startsWith('Ağ Cihazları/Modem')) return <Radio className={iconClass} />;

  if (category?.startsWith('Sunucular/Fiziksel')) return <ServerCrash className={iconClass} />;
  if (category?.startsWith('Sunucular/Sanal')) return <Cloud className={iconClass} />;
  if (category?.startsWith('Sunucular/Container')) return <Package className={iconClass} />;
  if (category?.startsWith('Sunucular/Veritabanı')) return <Database className={iconClass} />;
  if (category?.startsWith('Sunucular/Depolama')) return <HardDrive className={iconClass} />;

  if (category?.startsWith('Web/WebServer')) return <Globe2 className={iconClass} />;
  if (category?.startsWith('Web/API')) return <Webhook className={iconClass} />;
  if (category?.startsWith('Web/Mail')) return <Mail className={iconClass} />;
  if (category?.startsWith('Web/CDN')) return <Network className={iconClass} />;
  if (category?.startsWith('Web/DNS')) return <Search className={iconClass} />;

  if (category?.startsWith('IoT/Sensör')) return <Activity className={iconClass} />;
  if (category?.startsWith('IoT/PLC')) return <Cog className={iconClass} />;
  if (category?.startsWith('IoT/BinaSistemi')) return <Building2 className={iconClass} />;
  if (category?.startsWith('IoT/UPS')) return <Battery className={iconClass} />;

  // Ana kategoriler
  if (category === 'Ağ Cihazları') return <Network className={iconClass} />;
  if (category === 'Sunucular') return <Server className={iconClass} />;
  if (category === 'Web') return <Globe className={iconClass} />;
  if (category === 'IoT') return <Cpu className={iconClass} />;

  // Varsayılan ikon
  return <Server className={iconClass} />;
};

export default DeviceCategoryIcon;
