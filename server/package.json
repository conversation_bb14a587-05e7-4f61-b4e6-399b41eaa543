{"name": "server", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.4", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dns-packet": "^5.6.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "ioredis": "^4.28.5", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mysql2": "^3.14.0", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "pg": "^8.14.1", "snmp-native": "^1.2.0", "socket.io": "^4.7.2", "uuid": "^9.0.1", "node-winrm": "^0.3.3", "ssh2": "^1.15.0", "dockerode": "^4.0.2"}}