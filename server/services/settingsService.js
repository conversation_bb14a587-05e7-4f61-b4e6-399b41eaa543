/**
 * <PERSON>yar<PERSON> servisi
 */
const fs = require('fs').promises;
const path = require('path');

// <PERSON>yarlar dosyası yolu
const settingsFilePath = path.join(__dirname, '../data/settings.json');

// Varsayılan ayarlar
const defaultSettings = {
  // Bildirim ayarları
  emailNotifications: false,
  emailServer: '',
  emailPort: '',
  emailUser: '',
  emailPassword: '',
  emailFrom: '',
  emailTo: '',
  emailSecure: true,

  notificationRetentionDays: '30', // Bildirim saklama süresi

  // Varsayılan izleme aralıkları (dakika cinsinden)
  defaultPingInterval: '5',
  defaultHttpInterval: '10',
  defaultDnsInterval: '10',
  defaultSslInterval: '60',
  defaultTcpInterval: '5',
  defaultSnmpInterval: '5',
  defaultDatabaseInterval: '10',
  defaultApiInterval: '10',
  defaultSmtpInterval: '15',
  defaultWindowsInterval: '15',
  defaultLinuxInterval: '15',
  defaultIpmiInterval: '30',
  defaultDockerInterval: '15',
  defaultDnsServer: '*******',

  // İzleme türleri için saklama süreleri (gün cinsinden)
  icmpRetentionDays: '30',
  httpRetentionDays: '30',
  tcpRetentionDays: '30',
  snmpRetentionDays: '30',
  dnsRetentionDays: '30',
  sslRetentionDays: '30',
  databaseRetentionDays: '90',
  apiRetentionDays: '30',
  smtpRetentionDays: '30',
  windowsRetentionDays: '30',
  linuxRetentionDays: '30',
  ipmiRetentionDays: '30',
  dockerRetentionDays: '30',

  // Güvenlik ayarları
  sessionTimeout: '60', // Dakika cinsinden oturum zaman aşımı
  passwordPolicy: 'medium', // low, medium, high
  passwordExpiryDays: '90', // Şifre geçerlilik süresi (gün)
  maxLoginAttempts: '5', // Maksimum başarısız giriş denemesi
  accountLockDuration: '30', // Hesap kilitleme süresi (dakika)

  bruteForceProtection: true, // Brute force koruması
  securityLogRetention: '30', // Güvenlik logları saklama süresi (gün)
  autoLogout: true // Otomatik çıkış
};

/**
 * Ayarları getirir
 * @returns {Promise<Object>} - Ayarlar
 */
const getSettings = async () => {
  try {
    // data klasörünün varlığını kontrol et
    try {
      await fs.access(path.join(__dirname, '../data'));
    } catch (err) {
      // data klasörü yoksa oluştur
      await fs.mkdir(path.join(__dirname, '../data'), { recursive: true });
    }

    // Ayarlar dosyasını oku
    const data = await fs.readFile(settingsFilePath, 'utf8');
    return JSON.parse(data);
  } catch (err) {
    // Dosya yoksa veya okunamazsa varsayılan ayarları kullan
    await fs.writeFile(settingsFilePath, JSON.stringify(defaultSettings, null, 2));
    return defaultSettings;
  }
};

/**
 * Ayarları kaydeder
 * @param {Object} settings - Ayarlar
 * @returns {Promise<Object>} - Kaydedilen ayarlar
 */
const saveSettings = async (settings) => {
  await fs.writeFile(settingsFilePath, JSON.stringify(settings, null, 2));
  return settings;
};

module.exports = {
  getSettings,
  saveSettings,
  defaultSettings
};
