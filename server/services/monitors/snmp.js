/**
 * SNMP izleme servisi
 * Command-line SNMP araçları kullanılarak modernize edildi (snmpget, snmpwalk)
 */
const { exec } = require('child_process');
const BaseMonitor = require('./base-monitor');

// Standart SNMP OID'leri
const OIDs = {
  // Sistem bilgileri
  sysDescr: '*******.*******.0',
  sysUpTime: '*******.*******.0',
  sysName: '*******.*******.0',
  sysLocation: '*******.*******.0',
  sysContact: '*******.*******.0',

  // Arayüz bilgileri
  ifNumber: '*******.*******.0',
  ifTable: '*******.*******',
  ifEntry: '*******.*******.1',
  ifIndex: '*******.*******.1.1',
  ifDescr: '*******.*******.1.2',
  ifType: '*******.*******.1.3',
  ifMtu: '*******.*******.1.4',
  ifSpeed: '*******.*******.1.5',
  ifPhysAddress: '*******.*******.1.6',
  ifAdminStatus: '*******.*******.1.7',
  ifOperStatus: '*******.*******.1.8',
  ifInOctets: '*******.*******.1.10',
  ifInUcastPkts: '*******.*******.1.11',
  ifInErrors: '*******.*******.1.14',
  ifOutOctets: '*******.*******.1.16',
  ifOutUcastPkts: '*******.*******.1.17',
  ifOutErrors: '*******.*******.1.20',

  // CPU ve bellek kullanımı (bazı cihazlarda)
  hrProcessorLoad: '*******.********.3.1.2',
  memoryUsage: '*******.4.1.2021.4'
};

class SnmpMonitor extends BaseMonitor {
  constructor() {
    super('snmp');
    this.OIDs = OIDs;
    console.log('SNMP Monitor initialized using command-line tools (snmpget, snmpwalk)');
  }

  /**
   * SNMP araçlarının mevcut olup olmadığını kontrol eder
   * @returns {Promise<boolean>} - SNMP araçları mevcut mu?
   */
  async checkSnmpToolsAvailable() {
    return new Promise((resolve) => {
      exec('which snmpget snmpwalk', (error) => {
        resolve(!error);
      });
    });
  }

  /**
   * SNMP GET isteği gönderir (command-line snmpget kullanarak)
   * @param {Object} options - SNMP isteği seçenekleri
   * @returns {Promise<Object>} - SNMP isteği sonuçları
   */
  async snmpGet(options) {
    try {
      // SNMP araçlarının mevcut olup olmadığını kontrol et
      const toolsAvailable = await this.checkSnmpToolsAvailable();
      if (!toolsAvailable) {
        throw new Error('SNMP tools (snmpget, snmpwalk) not found. Please install net-snmp-utils package.');
      }

      const { host, port = 161, community = 'public', timeout = 5000, oids } = options;

      if (!oids || oids.length === 0) {
        throw new Error('No OIDs specified for SNMP GET request');
      }

      // OID'leri string formatına çevir
      const oidStrings = oids.map(oid => Array.isArray(oid) ? oid.join('.') : oid);

      // snmpget komutunu oluştur
      const command = `snmpget -v2c -c ${community} -t ${Math.ceil(timeout/1000)} ${host}:${port} ${oidStrings.join(' ')}`;

      console.log(`Executing SNMP GET: ${command}`);

      const output = await this.executeSnmpCommand(command, timeout);

      // Çıktıyı parse et
      return this.parseSnmpGetOutput(output, oidStrings);
    } catch (error) {
      console.error('SNMP GET error:', error);
      throw error;
    }
  }

  /**
   * SNMP komutunu çalıştırır
   * @param {string} command - Çalıştırılacak SNMP komutu
   * @param {number} timeout - Zaman aşımı (ms)
   * @returns {Promise<string>} - Komut çıktısı
   */
  executeSnmpCommand(command, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const child = exec(command, { timeout }, (error, stdout, stderr) => {
        if (error) {
          // Timeout hatası
          if (error.code === 'ETIMEDOUT') {
            reject(new Error('SNMP command timed out'));
            return;
          }

          // SNMP hatası (No Response gibi)
          if (stderr && stderr.includes('Timeout')) {
            reject(new Error('SNMP timeout - device not responding'));
            return;
          }

          // Diğer hatalar
          reject(new Error(`SNMP command failed: ${error.message}`));
          return;
        }

        if (stderr && !stderr.includes('Warning')) {
          console.warn(`SNMP command stderr: ${stderr}`);
        }

        resolve(stdout.trim());
      });

      // Manuel timeout kontrolü
      setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error('SNMP command timed out'));
      }, timeout);
    });
  }

  /**
   * snmpget çıktısını parse eder
   * @param {string} output - snmpget çıktısı
   * @param {Array} oids - İstenen OID'ler
   * @returns {Array} - Parse edilmiş varbinds
   */
  parseSnmpGetOutput(output, oids) {
    const varbinds = [];
    const lines = output.split('\n').filter(line => line.trim());

    lines.forEach((line, index) => {
      try {
        // Format: OID = TYPE: VALUE
        const match = line.match(/^(.+?)\s*=\s*(.+?):\s*(.+)$/);
        if (match) {
          const [, oidStr, typeStr, valueStr] = match;

          varbinds.push({
            oid: oidStr.trim(),
            type: this.parseSnmpType(typeStr.trim()),
            value: this.parseSnmpValue(valueStr.trim(), typeStr.trim())
          });
        } else {
          console.warn(`Could not parse SNMP line: ${line}`);
        }
      } catch (error) {
        console.error(`Error parsing SNMP line "${line}":`, error);
      }
    });

    return varbinds;
  }

  /**
   * SNMP WALK isteği gönderir (command-line snmpwalk kullanarak)
   * @param {Object} options - SNMP isteği seçenekleri
   * @returns {Promise<Object>} - SNMP isteği sonuçları
   */
  async snmpWalk(options) {
    try {
      const { host, port = 161, community = 'public', timeout = 10000, oid } = options;

      if (!oid) {
        throw new Error('No OID specified for SNMP WALK request');
      }

      // OID'yi string formatına çevir
      const oidString = Array.isArray(oid) ? oid.join('.') : oid;

      // snmpwalk komutunu oluştur
      const command = `snmpwalk -v2c -c ${community} -t ${Math.ceil(timeout/1000)} ${host}:${port} ${oidString}`;

      console.log(`Executing SNMP WALK: ${command}`);

      const output = await this.executeSnmpCommand(command, timeout);

      // Çıktıyı parse et
      return this.parseSnmpWalkOutput(output);
    } catch (error) {
      console.error('SNMP WALK error:', error);
      throw error;
    }
  }

  /**
   * snmpwalk çıktısını parse eder
   * @param {string} output - snmpwalk çıktısı
   * @returns {Array} - Parse edilmiş varbinds
   */
  parseSnmpWalkOutput(output) {
    const varbinds = [];
    const lines = output.split('\n').filter(line => line.trim());

    lines.forEach(line => {
      try {
        // Format: OID = TYPE: VALUE
        const match = line.match(/^(.+?)\s*=\s*(.+?):\s*(.+)$/);
        if (match) {
          const [, oidStr, typeStr, valueStr] = match;

          varbinds.push({
            oid: oidStr.trim(),
            type: this.parseSnmpType(typeStr.trim()),
            value: this.parseSnmpValue(valueStr.trim(), typeStr.trim())
          });
        }
      } catch (error) {
        console.error(`Error parsing SNMP walk line "${line}":`, error);
      }
    });

    return varbinds;
  }

  /**
   * SNMP tip string'ini sayısal tipe çevirir
   * @param {string} typeStr - SNMP tip string'i
   * @returns {number} - Sayısal tip
   */
  parseSnmpType(typeStr) {
    const typeMap = {
      'INTEGER': 2,
      'STRING': 4,
      'OID': 6,
      'IpAddress': 64,
      'Counter32': 65,
      'Gauge32': 66,
      'TimeTicks': 67,
      'Counter64': 70,
      'Opaque': 68
    };

    return typeMap[typeStr] || 4; // Default to STRING
  }

  /**
   * SNMP değerini uygun formata çevirir
   * @param {string} valueStr - SNMP değer string'i
   * @param {string} typeStr - SNMP tip string'i
   * @returns {any} - Parse edilmiş değer
   */
  parseSnmpValue(valueStr, typeStr) {
    try {
      switch (typeStr) {
        case 'INTEGER':
        case 'Counter32':
        case 'Gauge32':
        case 'TimeTicks':
          return parseInt(valueStr, 10);
        case 'Counter64':
          return BigInt(valueStr);
        case 'STRING':
          // Tırnak işaretlerini kaldır
          return valueStr.replace(/^"(.*)"$/, '$1');
        case 'IpAddress':
          return valueStr;
        default:
          return valueStr;
      }
    } catch (error) {
      console.error(`Error parsing SNMP value "${valueStr}" of type "${typeStr}":`, error);
      return valueStr;
    }
  }

  /**
   * Sistem bilgilerini alır
   * @param {Object} options - SNMP isteği seçenekleri
   * @returns {Promise<Object>} - Sistem bilgileri
   */
  async getSystemInfo(options) {
    try {
      // Web sitelerinde SNMP genellikle çalışmaz, bu durumda varsayılan değerler döndür
      if (options.host.match(/\.(com|org|net|io|dev|co|edu|gov)$/i)) {
        return {
          description: `Web service: ${options.host}`,
          uptime: 0,
          name: options.host,
          location: 'Unknown',
          contact: 'Unknown'
        };
      }

      const oids = [
        this.OIDs.sysDescr,
        this.OIDs.sysUpTime,
        this.OIDs.sysName,
        this.OIDs.sysLocation,
        this.OIDs.sysContact
      ];

      const varbinds = await this.snmpGet({ ...options, oids });

      const result = {};
      varbinds.forEach(varbind => {
        switch (varbind.oid) {
          case this.OIDs.sysDescr:
            result.description = varbind.value.toString();
            break;
          case this.OIDs.sysUpTime:
            result.uptime = varbind.value;
            break;
          case this.OIDs.sysName:
            result.name = varbind.value.toString();
            break;
          case this.OIDs.sysLocation:
            result.location = varbind.value.toString();
            break;
          case this.OIDs.sysContact:
            result.contact = varbind.value.toString();
            break;
        }
      });

      return result;
    } catch (error) {
      // Hata durumunda varsayılan değerler döndür
      return {
        description: `Device: ${options.host}`,
        uptime: 0,
        name: options.host,
        location: 'Unknown',
        contact: 'Unknown'
      };
    }
  }

  /**
   * Arayüz bilgilerini alır
   * @param {Object} options - SNMP isteği seçenekleri
   * @returns {Promise<Array>} - Arayüz bilgileri
   */
  async getInterfaces(options) {
    try {
      // Web sitelerinde SNMP genellikle çalışmaz, bu durumda varsayılan değerler döndür
      if (options.host.match(/\.(com|org|net|io|dev|co|edu|gov)$/i)) {
        return [{
          index: 1,
          description: 'Virtual Interface',
          type: 1,
          mtu: 1500,
          speed: 1000000000,
          physAddress: '000000000000',
          adminStatus: 1,
          operStatus: 1,
          inOctets: 0,
          outOctets: 0,
          inErrors: 0,
          outErrors: 0
        }];
      }

      // Önce arayüz sayısını al
      const ifNumberVarbinds = await this.snmpGet({ ...options, oids: [this.OIDs.ifNumber] });
      const ifNumber = ifNumberVarbinds[0].value;

      // Arayüz tablosunu al
      const ifTableVarbinds = await this.snmpWalk({ ...options, oid: this.OIDs.ifEntry });

      // Arayüz bilgilerini düzenle
      const interfaces = {};

      ifTableVarbinds.forEach(varbind => {
        const oidParts = varbind.oid.split('.');
        const ifIndex = oidParts[oidParts.length - 1];
        const oidPrefix = oidParts.slice(0, -1).join('.');

        if (!interfaces[ifIndex]) {
          interfaces[ifIndex] = { index: ifIndex };
        }

        switch (oidPrefix) {
          case this.OIDs.ifDescr:
            interfaces[ifIndex].description = varbind.value.toString();
            break;
          case this.OIDs.ifType:
            interfaces[ifIndex].type = varbind.value;
            break;
          case this.OIDs.ifMtu:
            interfaces[ifIndex].mtu = varbind.value;
            break;
          case this.OIDs.ifSpeed:
            interfaces[ifIndex].speed = varbind.value;
            break;
          case this.OIDs.ifPhysAddress:
            interfaces[ifIndex].physAddress = varbind.value.toString('hex');
            break;
          case this.OIDs.ifAdminStatus:
            interfaces[ifIndex].adminStatus = varbind.value;
            break;
          case this.OIDs.ifOperStatus:
            interfaces[ifIndex].operStatus = varbind.value;
            break;
          case this.OIDs.ifInOctets:
            interfaces[ifIndex].inOctets = varbind.value;
            break;
          case this.OIDs.ifOutOctets:
            interfaces[ifIndex].outOctets = varbind.value;
            break;
          case this.OIDs.ifInErrors:
            interfaces[ifIndex].inErrors = varbind.value;
            break;
          case this.OIDs.ifOutErrors:
            interfaces[ifIndex].outErrors = varbind.value;
            break;
        }
      });

      return Object.values(interfaces);
    } catch (error) {
      // Hata durumunda varsayılan değerler döndür
      return [{
        index: 1,
        description: 'Default Interface',
        type: 1,
        mtu: 1500,
        speed: 1000000000,
        physAddress: '000000000000',
        adminStatus: 1,
        operStatus: 1,
        inOctets: 0,
        outOctets: 0,
        inErrors: 0,
        outErrors: 0
      }];
    }
  }

  /**
   * Bir cihazın SNMP durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // SNMP izleme yapılandırmasını al
      const config = device.monitors?.snmp || {};

      const options = {
        host: device.host,
        port: config.port || 161,
        community: config.community || 'public',
        timeout: config.timeout || 5000
      };

      const startTime = Date.now();

      // Sistem bilgilerini al
      const systemInfo = await this.getSystemInfo(options);

      // Arayüz bilgilerini al
      const interfaces = await this.getInterfaces(options);

      const responseTime = Date.now() - startTime;

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: 'up',
        responseTime,
        error: '',
        details: {
          systemInfo,
          interfaces: interfaces.map(iface => ({
            index: iface.index,
            description: iface.description,
            operStatus: iface.operStatus,
            inOctets: iface.inOctets,
            outOctets: iface.outOctets
          }))
        }
      };

      // İzleme aralığını belirle
      const defaultInterval = 60000; // 60 saniye
      const interval = device.monitors?.snmp?.interval ? parseInt(device.monitors.snmp.interval) * 1000 : defaultInterval;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`SNMP check for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Sonuçları interval bilgisiyle kaydet
      await this.saveResults(device.id, result, io, parsedInterval);

      return {
        host: device.host,
        success: true,
        responseTime,
        systemInfo,
        interfaces,
        error: null
      };
    } catch (error) {
      console.error(`SNMP monitoring error for ${device.host}:`, error);

      // İzleme aralığını belirle
      const defaultInterval = 60000; // 60 saniye
      const interval = device.monitors?.snmp?.interval ? parseInt(device.monitors.snmp.interval) * 1000 : defaultInterval;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`SNMP check error for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan (daha kısa interval ile)
      await this.saveError(device.id, error, io, parsedInterval, true);

      return {
        host: device.host,
        success: false,
        responseTime: 0,
        systemInfo: null,
        interfaces: [],
        error: error.message
      };
    }
  }
}

// Singleton instance oluştur
const snmpMonitor = new SnmpMonitor();

module.exports = {
  snmpGet: (options) => snmpMonitor.snmpGet(options),
  snmpWalk: (options) => snmpMonitor.snmpWalk(options),
  getSystemInfo: (options) => snmpMonitor.getSystemInfo(options),
  getInterfaces: (options) => snmpMonitor.getInterfaces(options),
  monitorDevice: (device, io) => snmpMonitor.monitorDevice(device, io),
  getResults: (deviceId) => snmpMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => snmpMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => snmpMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => snmpMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => snmpMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => snmpMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate),
  OIDs
};
