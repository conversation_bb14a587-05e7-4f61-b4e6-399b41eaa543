/**
 * TCP izleme servisi
 */
const net = require('net');
const BaseMonitor = require('./base-monitor');
const { MONITOR_KEY, MONITOR_HISTORY_KEY } = require('../../constants/redis-keys');

class TcpMonitor extends BaseMonitor {
  constructor() {
    super('tcp');
  }

  /**
   * TCP port bağlantısını kontrol eder
   * @param {string} host - Kontrol edilecek host
   * @param {number} port - Kontrol edilecek port
   * @param {number} timeout - Zaman aşımı süresi (ms)
   * @returns {Promise<Object>} - TCP bağlantı sonuçları
   */
  checkPort(host, port, timeout = 2000) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const socket = new net.Socket();

      // Zaman aşımı ayarla
      socket.setTimeout(timeout);

      // Bağlantı başarılı olduğunda
      socket.on('connect', () => {
        const responseTime = Date.now() - startTime;
        socket.destroy();
        resolve({
          host,
          port,
          open: true,
          responseTime,
          error: null
        });
      });

      // Hata durumunda
      socket.on('error', (error) => {
        const responseTime = Date.now() - startTime;
        socket.destroy();
        resolve({
          host,
          port,
          open: false,
          responseTime,
          error: error.message
        });
      });

      // Zaman aşımı durumunda
      socket.on('timeout', () => {
        const responseTime = Date.now() - startTime;
        socket.destroy();
        resolve({
          host,
          port,
          open: false,
          responseTime,
          error: 'Connection timeout'
        });
      });

      // Bağlantıyı başlat
      socket.connect(port, host);
    });
  }

  /**
   * Bir cihazın TCP port durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // TCP izleme yapılandırmasını al
      const config = device.monitors?.tcp || {};
      const port = config.port || 80;
      const timeout = config.timeout || 2000;

      const tcpResult = await this.checkPort(device.host, port, timeout);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: tcpResult.open ? 'up' : 'down',
        responseTime: tcpResult.responseTime,
        error: tcpResult.error || '',
        details: {
          host: tcpResult.host,
          port: tcpResult.port
        }
      };

      // TCP için özel durum: port bilgisini de içeren anahtarlar kullan
      const key = `${MONITOR_KEY(this.type, device.id)}:${port}`;
      const historyKey = `${MONITOR_HISTORY_KEY(this.type, device.id)}:${port}`;

      // Özel kaydetme işlemi (BaseMonitor'ün saveResults metodunu override et)
      await this.saveResultsWithPort(device.id, port, result, io);

      return tcpResult;
    } catch (error) {
      console.error(`TCP monitoring error for ${device.host}:`, error);

      // TCP izleme yapılandırmasını al
      const config = device.monitors?.tcp || {};
      const port = config.port || 80;

      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan
      await this.saveErrorWithPort(device.id, port, error, io);

      return {
        host: device.host,
        port: port,
        open: false,
        responseTime: 0,
        error: error.message
      };
    }
  }

  /**
   * TCP için özel sonuç kaydetme metodu (port bilgisini içerir)
   * @param {string} deviceId - Cihaz ID
   * @param {number} port - Port numarası
   * @param {Object} result - İzleme sonuçları
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<void>}
   */
  async saveResultsWithPort(deviceId, port, result, io = null) {
    try {
      const redisClient = require('../../config/redis');

      // Ana sonuç anahtarı (port bilgisini içerir)
      const key = `monitor:${this.type}:${deviceId}:${port}`;

      // İzleme aralığını belirle
      const defaultInterval = 60000; // 60 saniye (1 dakika)
      const device = await redisClient.hgetall(`device:${deviceId}`);
      let interval = defaultInterval;

      if (device && device.monitors) {
        try {
          const monitors = JSON.parse(device.monitors);
          if (monitors.tcp && monitors.tcp.interval) {
            // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
            interval = parseInt(monitors.tcp.interval) * 60 * 1000;
          }
        } catch (e) {
          console.error(`Error parsing monitors for device ${deviceId}:`, e);
        }
      }

      const now = Date.now();

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`TCP check for port ${port}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      const nextCheck = now + parsedInterval;

      // Sonuçları Redis'e kaydet
      await redisClient.hmset(key, {
        'status': result.status || 'unknown',
        'port': port,
        'responseTime': result.responseTime || 0,
        'lastCheck': now.toString(),
        'nextCheck': nextCheck.toString(),
        'error': result.error || '',
        'details': result.details ? JSON.stringify(result.details) : '{}'
      });

      // TTL ayarla (24 saat)
      await redisClient.expire(key, 86400);

      // Geçmiş verileri kaydet (son 100 ölçüm)
      const historyKey = `history:${this.type}:${deviceId}:${port}`;
      await redisClient.lpush(historyKey, JSON.stringify({
        timestamp: Date.now(),
        status: result.status || 'unknown',
        responseTime: result.responseTime || 0,
        error: result.error || '',
        details: result.details || {}
      }));
      await redisClient.ltrim(historyKey, 0, 99);

      // Socket.io ile gerçek zamanlı güncelleme gönder
      if (io) {
        io.emit('monitor:update', {
          type: this.type,
          deviceId: deviceId,
          port: port,
          data: {
            status: result.status || 'unknown',
            responseTime: result.responseTime || 0,
            lastCheck: now,
            nextCheck: nextCheck,
            error: result.error || '',
            details: result.details || {}
          }
        });
      }
    } catch (error) {
      console.error(`Error saving ${this.type} results for device ${deviceId}:`, error);
    }
  }

  /**
   * TCP için özel hata kaydetme metodu (port bilgisini içerir)
   * @param {string} deviceId - Cihaz ID
   * @param {number} port - Port numarası
   * @param {Error} error - Hata nesnesi
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<void>}
   */
  async saveErrorWithPort(deviceId, port, error, io = null) {
    try {
      const errorResult = {
        status: 'error',
        responseTime: 0,
        error: error.message || 'Unknown error',
        details: {
          host: deviceId,
          port: port,
          stack: error.stack
        }
      };

      await this.saveResultsWithPort(deviceId, port, errorResult, io);
    } catch (err) {
      console.error(`Error saving error for ${this.type} monitor of device ${deviceId}:`, err);
    }
  }

  /**
   * TCP için özel sonuç alma metodu (port bilgisini içerir)
   * @param {string} deviceId - Cihaz ID
   * @param {number} port - Port numarası
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async getResultsWithPort(deviceId, port) {
    try {
      const redisClient = require('../../config/redis');
      const key = `monitor:${this.type}:${deviceId}:${port}`;
      return await redisClient.hgetall(key);
    } catch (error) {
      console.error(`Error getting ${this.type} results for device ${deviceId}:`, error);
      return null;
    }
  }

  /**
   * TCP için özel geçmiş alma metodu (port bilgisini içerir)
   * @param {string} deviceId - Cihaz ID
   * @param {number} port - Port numarası
   * @param {number} limit - Kaç kayıt alınacağı
   * @returns {Promise<Array>} - İzleme geçmişi
   */
  async getHistoryWithPort(deviceId, port, limit = 100) {
    try {
      const redisClient = require('../../config/redis');
      const historyKey = `history:${this.type}:${deviceId}:${port}`;
      const history = await redisClient.lrange(historyKey, 0, limit - 1);

      if (!history || history.length === 0) {
        return [];
      }

      return history.map(item => {
        try {
          return JSON.parse(item);
        } catch (e) {
          console.error(`Error parsing history item for ${this.type} of device ${deviceId}:`, e);
          return null;
        }
      }).filter(item => item !== null);
    } catch (error) {
      console.error(`Error getting ${this.type} history for device ${deviceId}:`, error);
      return [];
    }
  }
}

// Singleton instance oluştur
const tcpMonitor = new TcpMonitor();

module.exports = {
  checkPort: (host, port, timeout) => tcpMonitor.checkPort(host, port, timeout),
  monitorDevice: (device, io) => tcpMonitor.monitorDevice(device, io),
  getResults: (deviceId) => tcpMonitor.getResults(deviceId),
  getResultsWithPort: (deviceId, port) => tcpMonitor.getResultsWithPort(deviceId, port),
  getHistory: (deviceId) => tcpMonitor.getHistory(deviceId),
  getHistoryWithPort: (deviceId, port, limit) => tcpMonitor.getHistoryWithPort(deviceId, port, limit),
  deleteData: (deviceId) => tcpMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => tcpMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => tcpMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => tcpMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
