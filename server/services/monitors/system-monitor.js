/**
 * Unified System Monitor - Systeminformation ile Cross-Platform
 * Windows, Linux ve IPMI monitoring için tek çözüm
 */

const BaseMonitor = require('./base-monitor');
const si = require('systeminformation');
const { spawn } = require('child_process');
const { Client: SSHClient } = require('ssh2');
const path = require('path');

class SystemMonitor extends BaseMonitor {
  constructor() {
    super('system');
  }

  /**
   * Ana sistem monitoring fonksiyonu
   * @param {string} host - Hedef host
   * @param {Object} config - Monitoring konfigürasyonu
   * @returns {Promise<Object>} - Sistem bilgileri
   */
  async checkSystemHealth(host, config) {
    const startTime = process.hrtime();

    try {
      console.log(`🖥️ Sistem sağlığı kontrol ediliyor: ${host} (${config.platform})`);

      let systemData;

      switch (config.platform) {
        case 'windows':
          systemData = await this.getWindowsSystemInfo(host, config);
          break;
        case 'linux':
          // Eğer host localhost veya monitoring sunucusuysa, local systeminformation kullan
          if (host === 'localhost' || host === '127.0.0.1' || host === '*************') {
            console.log('Local sistem bilgileri alınıyor (monitoring sunucusu)');
            systemData = await this.getLocalSystemInfo();
          } else {
            systemData = await this.getLinuxSystemInfo(host, config);
          }
          break;
        default:
          throw new Error(`Desteklenmeyen platform: ${config.platform}`);
      }

      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));

      // Sistem sağlığını değerlendir
      const health = this.evaluateSystemHealth(systemData);

      return {
        success: true,
        responseTime,
        details: {
          host,
          platform: config.platform,
          protocol: this.getProtocolName(config.platform),
          system: systemData,
          health,
          timestamp: Date.now()
        }
      };

    } catch (error) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));
      
      console.error(`❌ Sistem sağlığı kontrol edilemedi ${host}:`, error.message);

      return {
        success: false,
        responseTime,
        error: error.message,
        details: { host, platform: config.platform }
      };
    }
  }

  /**
   * Local sistem bilgilerini alır (monitoring server'ın kendisi)
   * @returns {Promise<Object>} - Local sistem bilgileri
   */
  async getLocalSystemInfo() {
    try {
      // Paralel olarak tüm sistem bilgilerini al
      const [
        cpu,
        mem,
        osInfo,
        system,
        diskLayout,
        networkInterfaces,
        processes,
        services,
        temperature,
        graphics
      ] = await Promise.all([
        si.cpu(),
        si.mem(),
        si.osInfo(),
        si.system(),
        si.diskLayout(),
        si.networkInterfaces(),
        si.processes(),
        si.services('*'),
        si.cpuTemperature().catch(() => null),
        si.graphics().catch(() => null)
      ]);

      // CPU kullanımını al (1 saniye bekle)
      const cpuUsage = await si.currentLoad();

      return {
        cpu: {
          manufacturer: cpu.manufacturer,
          brand: cpu.brand,
          speed: cpu.speed,
          cores: cpu.cores,
          physicalCores: cpu.physicalCores,
          usage: Math.round(cpuUsage.currentLoad)
        },
        memory: {
          total: mem.total,
          free: mem.free,
          used: mem.used,
          active: mem.active,
          usage: Math.round((mem.used / mem.total) * 100)
        },
        os: {
          platform: osInfo.platform,
          distro: osInfo.distro,
          release: osInfo.release,
          arch: osInfo.arch,
          hostname: osInfo.hostname,
          uptime: osInfo.uptime
        },
        system: {
          manufacturer: system.manufacturer,
          model: system.model,
          version: system.version,
          serial: system.serial,
          uuid: system.uuid
        },
        disk: diskLayout.map(disk => ({
          device: disk.device,
          type: disk.type,
          name: disk.name,
          size: disk.size,
          interfaceType: disk.interfaceType
        })),
        network: networkInterfaces.filter(iface => !iface.internal).map(iface => ({
          iface: iface.iface,
          ip4: iface.ip4,
          ip6: iface.ip6,
          mac: iface.mac,
          speed: iface.speed,
          operstate: iface.operstate
        })),
        processes: {
          all: processes.all,
          running: processes.running,
          blocked: processes.blocked,
          sleeping: processes.sleeping
        },
        services: services.filter(service => service.running).length,
        temperature: temperature ? {
          main: temperature.main,
          cores: temperature.cores,
          max: temperature.max
        } : null,
        graphics: graphics ? graphics.controllers.map(gpu => ({
          vendor: gpu.vendor,
          model: gpu.model,
          vram: gpu.vram,
          vramDynamic: gpu.vramDynamic
        })) : null
      };

    } catch (error) {
      console.error('Local sistem bilgisi alınamadı:', error);
      throw error;
    }
  }

  /**
   * Windows sistem bilgilerini alır (WMI üzerinden)
   * @param {string} host - Hedef host
   * @param {Object} config - Windows konfigürasyonu
   * @returns {Promise<Object>} - Windows sistem bilgileri
   */
  async getWindowsSystemInfo(host, config) {
    // PowerShell script ile uzak Windows sistemde systeminformation çalıştır
    const psScript = this.generateWindowsPowerShellScript();
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Windows sistem sorgusu timeout'));
      }, config.timeout || 60000);

      // PowerShell ile uzak çalıştırma
      const credential = `$cred = New-Object System.Management.Automation.PSCredential('${config.username}', (ConvertTo-SecureString '${config.password}' -AsPlainText -Force))`;
      const invokeCommand = `Invoke-Command -ComputerName ${host} -Credential $cred -ScriptBlock { ${psScript} }`;
      
      const ps = spawn('pwsh', ['-Command', `${credential}; ${invokeCommand}`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      ps.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      ps.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ps.on('close', (code) => {
        clearTimeout(timeout);
        
        if (code === 0) {
          try {
            const systemData = JSON.parse(stdout);
            resolve(systemData);
          } catch (parseError) {
            reject(new Error(`JSON parse error: ${parseError.message}`));
          }
        } else {
          reject(new Error(`PowerShell error (${code}): ${stderr}`));
        }
      });

      ps.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Linux sistem bilgilerini alır (SSH üzerinden)
   * @param {string} host - Hedef host
   * @param {Object} config - Linux konfigürasyonu
   * @returns {Promise<Object>} - Linux sistem bilgileri
   */
  async getLinuxSystemInfo(host, config) {
    const { spawn } = require('child_process');

    return new Promise((resolve, reject) => {
      // Node.js script'ini hazırla
      const nodeScript = this.generateLinuxNodeScript();
      const scriptContent = nodeScript.replace(/"/g, '\\"').replace(/\n/g, ' ');

      // SSH komutunu hazırla
      const sshCommand = [
        '-o', 'ConnectTimeout=30',
        '-o', 'StrictHostKeyChecking=no',
        '-o', 'UserKnownHostsFile=/dev/null',
        '-o', 'LogLevel=ERROR',
        `${config.username}@${host}`,
        `node -e "${scriptContent}"`
      ];

      console.log(`SSH bağlantısı kuruluyor: ${config.username}@${host}`);

      // sshpass ile SSH bağlantısı kur
      const ssh = spawn('sshpass', ['-p', config.password, 'ssh', ...sshCommand], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      // Timeout ayarla
      const timeout = setTimeout(() => {
        ssh.kill('SIGTERM');
        reject(new Error('SSH bağlantısı zaman aşımına uğradı'));
      }, config.timeout || 30000);

      ssh.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      ssh.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ssh.on('close', (code) => {
        clearTimeout(timeout);

        if (code === 0) {
          try {
            // JSON çıktısını parse et
            const lines = stdout.trim().split('\n');
            const jsonLine = lines.find(line => line.startsWith('{'));

            if (jsonLine) {
              const systemData = JSON.parse(jsonLine);
              console.log('Linux sistem bilgileri başarıyla alındı');
              resolve(systemData);
            } else {
              reject(new Error('JSON çıktısı bulunamadı: ' + stdout));
            }
          } catch (parseError) {
            reject(new Error(`JSON parse error: ${parseError.message}, Output: ${stdout}`));
          }
        } else {
          reject(new Error(`SSH error (${code}): ${stderr || stdout}`));
        }
      });

      ssh.on('error', (err) => {
        clearTimeout(timeout);
        reject(new Error(`SSH spawn error: ${err.message}`));
      });
    });
  }

  /**
   * Protocol adını döndürür
   * @param {string} platform - Platform
   * @returns {string} - Protocol adı
   */
  getProtocolName(platform) {
    switch (platform) {
      case 'windows': return 'WMI+SI';
      case 'linux': return 'SSH+SI';
      default: return 'SI';
    }
  }

  /**
   * Windows PowerShell script'i oluşturur
   * @returns {string} - PowerShell script
   */
  generateWindowsPowerShellScript() {
    return `
      # Systeminformation benzeri bilgileri topla
      $cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
      $memory = Get-WmiObject -Class Win32_OperatingSystem
      $system = Get-WmiObject -Class Win32_ComputerSystem
      $os = Get-WmiObject -Class Win32_OperatingSystem
      $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }

      # CPU kullanımını al
      $cpuUsage = (Get-Counter "\\Processor(_Total)\\% Processor Time").CounterSamples.CookedValue

      # Sistem bilgilerini JSON formatında hazırla
      $systemInfo = @{
        cpu = @{
          manufacturer = $cpu.Manufacturer
          brand = $cpu.Name
          speed = [math]::Round($cpu.MaxClockSpeed / 1000, 2)
          cores = $cpu.NumberOfCores
          usage = [math]::Round(100 - $cpuUsage, 2)
        }
        memory = @{
          total = [long]$memory.TotalVisibleMemorySize * 1024
          free = [long]$memory.FreePhysicalMemory * 1024
          used = ([long]$memory.TotalVisibleMemorySize - [long]$memory.FreePhysicalMemory) * 1024
          usage = [math]::Round((([long]$memory.TotalVisibleMemorySize - [long]$memory.FreePhysicalMemory) / [long]$memory.TotalVisibleMemorySize) * 100, 2)
        }
        os = @{
          platform = "win32"
          distro = $os.Caption
          release = $os.Version
          arch = $os.OSArchitecture
          hostname = $system.Name
          uptime = [math]::Round((Get-Date) - (Get-Date $os.LastBootUpTime)).TotalSeconds
        }
        system = @{
          manufacturer = $system.Manufacturer
          model = $system.Model
          version = $system.SystemFamily
        }
        disk = @($disks | ForEach-Object {
          @{
            device = $_.DeviceID
            size = [long]$_.Size
            used = [long]($_.Size - $_.FreeSpace)
            free = [long]$_.FreeSpace
            usage = if ($_.Size -gt 0) { [math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 2) } else { 0 }
          }
        })
      }

      # JSON olarak çıktı ver
      $systemInfo | ConvertTo-Json -Depth 10 -Compress
    `;
  }

  /**
   * Linux Node.js script'i oluşturur
   * @returns {string} - Node.js script
   */
  generateLinuxNodeScript() {
    return `
      const si = require('systeminformation');

      (async () => {
        try {
          const [cpu, mem, osInfo, system, currentLoad, diskLayout] = await Promise.all([
            si.cpu(),
            si.mem(),
            si.osInfo(),
            si.system(),
            si.currentLoad(),
            si.diskLayout()
          ]);

          const systemInfo = {
            cpu: {
              manufacturer: cpu.manufacturer,
              brand: cpu.brand,
              speed: cpu.speed,
              cores: cpu.cores,
              usage: Math.round(currentLoad.currentLoad)
            },
            memory: {
              total: mem.total,
              free: mem.free,
              used: mem.used,
              usage: Math.round((mem.used / mem.total) * 100)
            },
            os: {
              platform: osInfo.platform,
              distro: osInfo.distro,
              release: osInfo.release,
              arch: osInfo.arch,
              hostname: osInfo.hostname,
              uptime: osInfo.uptime
            },
            system: {
              manufacturer: system.manufacturer,
              model: system.model,
              version: system.version
            },
            disk: diskLayout.map(disk => ({
              device: disk.device,
              size: disk.size,
              type: disk.type
            }))
          };

          console.log(JSON.stringify(systemInfo));
        } catch (error) {
          console.error(JSON.stringify({ error: error.message }));
          process.exit(1);
        }
      })();
    `;
  }

  /**
   * Sistem sağlığını değerlendirir
   * @param {Object} systemData - Sistem verileri
   * @returns {Object} - Sağlık durumu
   */
  evaluateSystemHealth(systemData) {
    const issues = [];
    let score = 100;

    // CPU kullanımı kontrolü
    if (systemData.cpu && systemData.cpu.usage > 90) {
      issues.push('Yüksek CPU kullanımı');
      score -= 20;
    } else if (systemData.cpu && systemData.cpu.usage > 70) {
      issues.push('Orta düzey CPU kullanımı');
      score -= 10;
    }

    // Memory kullanımı kontrolü
    if (systemData.memory && systemData.memory.usage > 90) {
      issues.push('Yüksek bellek kullanımı');
      score -= 20;
    } else if (systemData.memory && systemData.memory.usage > 80) {
      issues.push('Orta düzey bellek kullanımı');
      score -= 10;
    }

    // Disk kullanımı kontrolü
    if (systemData.disk && Array.isArray(systemData.disk)) {
      systemData.disk.forEach(disk => {
        if (disk.usage > 95) {
          issues.push(`Disk ${disk.device} dolu`);
          score -= 15;
        } else if (disk.usage > 85) {
          issues.push(`Disk ${disk.device} dolmak üzere`);
          score -= 5;
        }
      });
    }

    // Sıcaklık kontrolü (varsa)
    if (systemData.temperature && systemData.temperature.main > 80) {
      issues.push('Yüksek CPU sıcaklığı');
      score -= 15;
    }

    // Genel durum belirleme
    let status = 'up';
    if (score < 50) {
      status = 'critical';
    } else if (score < 70) {
      status = 'warning';
    }

    return {
      status,
      score: Math.max(0, score),
      issues,
      summary: issues.length > 0 ? issues.join(', ') : 'Sistem sağlıklı'
    };
  }

  /**
   * Bir cihazın sistem durumunu kontrol eder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @param {boolean} isManualCheck - Manuel kontrol mu?
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io, isManualCheck = false) {
    try {
      // Sistem izleme yapılandırmasını al
      const config = device.monitors?.system || {};

      // Platform belirleme
      if (!config.platform) {
        throw new Error('Platform belirtilmemiş (windows/linux/local)');
      }

      // Platform özel kontroller
      if (config.platform === 'windows' && (!config.username || !config.password)) {
        throw new Error('Windows kimlik bilgileri eksik');
      }

      if (config.platform === 'linux' && !config.username) {
        throw new Error('Linux kullanıcı adı eksik');
      }

      if (config.platform === 'linux' && !config.password && !config.privateKey) {
        throw new Error('Linux şifre veya private key eksik');
      }

      const host = config.host || device.host;
      console.log(`System check for ${device.name}: ${host} (${config.platform})`);

      // Sistem kontrolü
      const systemResult = await this.checkSystemHealth(host, config);

      // Sonuç
      const result = {
        status: systemResult.success ? systemResult.details.health.status : 'down',
        responseTime: systemResult.responseTime,
        error: systemResult.error || '',
        details: {
          protocol: this.getProtocolName(config.platform),
          ...systemResult.details
        }
      };

      // Sonuçları kaydet
      await this.saveResults(device.id, result, io, isManualCheck);

      return result;

    } catch (error) {
      console.error(`System monitoring error for ${device.name}:`, error);

      const result = {
        status: 'down',
        responseTime: 0,
        error: error.message,
        details: {
          protocol: 'SYSTEM',
          host: device.host
        }
      };

      // Hata sonucunu kaydet
      await this.saveResults(device.id, result, io, isManualCheck);

      return result;
    }
  }
}

const systemMonitor = new SystemMonitor();

module.exports = {
  checkSystemHealth: (host, config) => systemMonitor.checkSystemHealth(host, config),
  monitorDevice: (device, io, isManualCheck) => systemMonitor.monitorDevice(device, io, isManualCheck),
  evaluateSystemHealth: (systemData) => systemMonitor.evaluateSystemHealth(systemData),
  getResults: (deviceId) => systemMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => systemMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => systemMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => systemMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => systemMonitor.getNextCheckTime(deviceId)
};
