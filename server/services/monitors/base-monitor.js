/**
 * Tüm izleme servisleri için temel sınıf
 */
const redisClient = require('../../config/redis');
const { safeJsonStringify } = require('../../utils/helpers');

class BaseMonitor {
  /**
   * Temel izleme sınıfını oluşturur
   * @param {string} type - İzleme türü (icmp, http, tcp, vb.)
   */
  constructor(type) {
    this.type = type;
  }

  /**
   * İzleme sonuçlarını Redis'e kaydeder
   * @param {string} deviceId - Cihaz ID
   * @param {Object} result - İzleme sonuçları
   * @param {Object} io - Socket.io nesnesi (opsiyonel)
   * @param {number} interval - İzleme aralığı (ms) (opsiyonel)
   * @param {boolean} isManualCheck - Manuel kontrol mu? (varsayılan: false)
   * @returns {Promise<void>}
   */
  async saveResults(deviceId, result, io = null, interval = null, isManualCheck = false) {
    try {
      const now = Date.now();

      // Ana sonuç anahtarı
      const key = `monitor:${this.type}:${deviceId}`;

      // Temel sonuç verileri
      const resultData = {
        'status': result.status || 'unknown',
        'responseTime': result.responseTime || 0,
        'lastCheck': now.toString(),
        'error': result.error || '',
        'details': result.details ? safeJsonStringify(result.details) : '{}'
      };

      // Interval değerini kullan (null olmamalı)
      // Eğer interval null ise, varsayılan olarak 5 dakika kullan
      const effectiveInterval = interval || 5 * 60 * 1000; // 5 dakika = 300000 ms

      // nextCheck değişkenini tanımla
      let nextCheck;

      // Manuel kontrol ise, mevcut nextCheck değerini al
      if (isManualCheck) {
        const currentNextCheck = await this.getNextCheckTime(deviceId);

        // Eğer mevcut bir nextCheck değeri varsa ve gelecekte ise, onu koru
        if (currentNextCheck > now) {
          nextCheck = currentNextCheck;
          resultData.nextCheck = nextCheck.toString();
          console.log(`Manual check for ${this.type} monitor of device ${deviceId}, preserving next scheduled check at: ${new Date(nextCheck).toISOString()}`);
        } else {
          // Mevcut nextCheck değeri yoksa veya geçmişte kaldıysa, yeni bir nextCheck hesapla
          nextCheck = now + effectiveInterval;
          resultData.nextCheck = nextCheck.toString();
          console.log(`Manual check for ${this.type} monitor of device ${deviceId}, setting next check at: ${new Date(nextCheck).toISOString()}`);
        }
      } else {
        // Normal kontrol ise, her zaman yeni bir nextCheck hesapla
        nextCheck = now + effectiveInterval;
        resultData.nextCheck = nextCheck.toString();
        console.log(`Next check for ${this.type} monitor of device ${deviceId} scheduled at: ${new Date(nextCheck).toISOString()}`);
      }

      // Sonuçları Redis'e kaydet
      await redisClient.hmset(key, resultData);

      // TTL ayarla (24 saat)
      await redisClient.expire(key, 86400);

      // Geçmiş verileri kaydet (son 100 ölçüm)
      const historyKey = `history:${this.type}:${deviceId}`;
      await redisClient.lpush(historyKey, safeJsonStringify({
        timestamp: now,
        status: result.status || 'unknown',
        responseTime: result.responseTime || 0,
        error: result.error || '',
        details: result.details || {}
      }));
      await redisClient.ltrim(historyKey, 0, 99);

      // Socket.io ile gerçek zamanlı güncelleme gönder
      if (io) {
        io.emit('monitor:update', {
          type: this.type,
          deviceId: deviceId,
          data: {
            status: result.status || 'unknown',
            responseTime: result.responseTime || 0,
            lastCheck: now,
            nextCheck: nextCheck, // Her zaman nextCheck değerini gönder
            error: result.error || '',
            details: result.details || {}
          }
        });
      }
    } catch (error) {
      console.error(`Error saving ${this.type} results for device ${deviceId}:`, error);
    }
  }

  /**
   * Hata durumunda sonuçları kaydeder
   * @param {string} deviceId - Cihaz ID
   * @param {Error} error - Hata nesnesi
   * @param {Object} io - Socket.io nesnesi (opsiyonel)
   * @param {number} interval - İzleme aralığı (ms) (opsiyonel)
   * @param {boolean} isManualCheck - Manuel kontrol mu? (varsayılan: false)
   * @returns {Promise<void>}
   */
  async saveError(deviceId, error, io = null, interval = null, isManualCheck = false) {
    try {
      const errorResult = {
        status: 'error',
        responseTime: 0,
        error: error.message || 'Unknown error',
        details: { stack: error.stack }
      };

      // Hata durumunda da normal interval kullan
      await this.saveResults(deviceId, errorResult, io, interval, isManualCheck);
    } catch (err) {
      console.error(`Error saving error for ${this.type} monitor of device ${deviceId}:`, err);
    }
  }

  /**
   * İzleme sonuçlarını Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async getResults(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const results = await redisClient.hgetall(key);

      // Sayısal değerleri dönüştür
      if (results) {
        if (results.lastCheck) results.lastCheck = parseInt(results.lastCheck);
        if (results.nextCheck) results.nextCheck = parseInt(results.nextCheck);
        if (results.responseTime) results.responseTime = parseInt(results.responseTime);

        // details alanını JSON'dan parse et
        if (results.details && typeof results.details === 'string') {
          try {
            results.details = JSON.parse(results.details);
          } catch (e) {
            console.error(`Error parsing details for ${this.type} monitor of device ${deviceId}:`, e);
          }
        }
      }

      return results;
    } catch (error) {
      console.error(`Error getting ${this.type} results for device ${deviceId}:`, error);
      return null;
    }
  }

  /**
   * İzleme geçmişini Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @param {number} limit - Kaç kayıt alınacağı
   * @returns {Promise<Array>} - İzleme geçmişi
   */
  async getHistory(deviceId, limit = 100) {
    try {
      const historyKey = `history:${this.type}:${deviceId}`;
      const history = await redisClient.lrange(historyKey, 0, limit - 1);

      if (!history || history.length === 0) {
        return [];
      }

      return history.map(item => {
        try {
          return JSON.parse(item);
        } catch (e) {
          console.error(`Error parsing history item for ${this.type} of device ${deviceId}:`, e);
          return null;
        }
      }).filter(item => item !== null);
    } catch (error) {
      console.error(`Error getting ${this.type} history for device ${deviceId}:`, error);
      return [];
    }
  }

  /**
   * İzleme verilerini siler
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<boolean>} - Başarılı ise true
   */
  async deleteData(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const historyKey = `history:${this.type}:${deviceId}`;

      await Promise.all([
        redisClient.del(key),
        redisClient.del(historyKey)
      ]);

      return true;
    } catch (error) {
      console.error(`Error deleting ${this.type} data for device ${deviceId}:`, error);
      return false;
    }
  }

  /**
   * Son kontrol zamanını Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<number>} - Son kontrol zamanı
   */
  async getLastCheckTime(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const lastCheck = await redisClient.hget(key, 'lastCheck');
      return lastCheck ? parseInt(lastCheck) : 0;
    } catch (error) {
      console.error(`Error getting last check time for ${this.type} monitor of device ${deviceId}:`, error);
      return 0;
    }
  }

  /**
   * Sonraki kontrol zamanını Redis'ten alır
   * @param {string} deviceId - Cihaz ID
   * @returns {Promise<number>} - Sonraki kontrol zamanı
   */
  async getNextCheckTime(deviceId) {
    try {
      const key = `monitor:${this.type}:${deviceId}`;
      const nextCheck = await redisClient.hget(key, 'nextCheck');
      return nextCheck ? parseInt(nextCheck) : 0;
    } catch (error) {
      console.error(`Error getting next check time for ${this.type} monitor of device ${deviceId}:`, error);
      return 0;
    }
  }

  /**
   * Sonraki kontrol zamanını günceller
   * @param {string} deviceId - Cihaz ID
   * @param {number} interval - İzleme aralığı (ms)
   * @param {boolean} isInitialUpdate - İlk güncelleme mi? (varsayılan: false)
   * @returns {Promise<void>}
   */
  async updateNextCheckTime(deviceId, interval, isInitialUpdate = false) {
    try {
      const now = Date.now();
      const key = `monitor:${this.type}:${deviceId}`;

      if (isInitialUpdate) {
        // İlk güncelleme: sadece nextCheck'i güncelle
        const nextCheck = now + interval;
        await redisClient.hset(key, 'nextCheck', nextCheck.toString());
        console.log(`Initial next check time set for ${this.type} monitor of device ${deviceId}: ${new Date(nextCheck).toISOString()}`);
      } else {
        // Normal güncelleme: lastCheck'i al ve nextCheck'i hesapla
        const lastCheck = await this.getLastCheckTime(deviceId);
        const nextCheck = lastCheck > 0 ? lastCheck + interval : now + interval;

        // Eğer hesaplanan nextCheck geçmişte kaldıysa, şimdiki zamandan başla
        const finalNextCheck = nextCheck < now ? now + interval : nextCheck;

        await redisClient.hset(key, 'nextCheck', finalNextCheck.toString());
        console.log(`Next check time updated for ${this.type} monitor of device ${deviceId}: ${new Date(finalNextCheck).toISOString()}`);
      }
    } catch (error) {
      console.error(`Error updating next check time for ${this.type} monitor of device ${deviceId}:`, error);
    }
  }
}

module.exports = BaseMonitor;
