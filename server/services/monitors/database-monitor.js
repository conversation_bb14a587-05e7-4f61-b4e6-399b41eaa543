/**
 * Veritabanı izleme servisi
 */
const mysql = require('mysql2/promise');
const { Pool } = require('pg');
const { MongoClient } = require('mongodb');
const BaseMonitor = require('./base-monitor');

class DatabaseMonitor extends BaseMonitor {
  constructor() {
    super('database');
  }

  /**
   * MySQL veritabanını kontrol eder
   * @param {Object} config - MySQL bağlantı yapılandırması
   * @returns {Promise<Object>} - MySQL kontrol sonuçları
   */
  async checkMySql(config) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    let connection;
    try {
      // MySQL bağlantısı oluştur
      connection = await mysql.createConnection({
        host: config.host,
        port: config.port || 3306,
        user: config.user,
        password: config.password,
        database: config.database,
        connectTimeout: config.timeout || 5000
      });

      // Basit bir sorgu çalıştır
      const [rows] = await connection.execute('SELECT 1 as result');

      // Veritabanı durumunu kontrol et
      if (config.checkStatus) {
        const [statusRows] = await connection.execute('SHOW STATUS');
        const statusMap = {};
        statusRows.forEach(row => {
          statusMap[row.Variable_name] = row.Value;
        });

        result.details.status = statusMap;
      }

      // Veritabanı versiyonunu kontrol et
      if (config.checkVersion) {
        const [versionRows] = await connection.execute('SELECT VERSION() as version');
        result.details.version = versionRows[0].version;
      }

      result.responseTime = Date.now() - startTime;
      result.status = 'up';
      result.details.connected = true;
    } catch (err) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = err.message;
      result.details.connected = false;
    } finally {
      if (connection) {
        await connection.end();
      }
    }

    return result;
  }

  /**
   * PostgreSQL veritabanını kontrol eder
   * @param {Object} config - PostgreSQL bağlantı yapılandırması
   * @returns {Promise<Object>} - PostgreSQL kontrol sonuçları
   */
  async checkPostgreSql(config) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    const pool = new Pool({
      host: config.host,
      port: config.port || 5432,
      user: config.user,
      password: config.password,
      database: config.database,
      connectionTimeoutMillis: config.timeout || 5000
    });

    let client;
    try {
      // PostgreSQL bağlantısı oluştur
      client = await pool.connect();

      // Basit bir sorgu çalıştır
      const res = await client.query('SELECT 1 as result');

      // Veritabanı versiyonunu kontrol et
      if (config.checkVersion) {
        const versionRes = await client.query('SELECT version()');
        result.details.version = versionRes.rows[0].version;
      }

      // Veritabanı istatistiklerini kontrol et
      if (config.checkStats) {
        const statsRes = await client.query(`
          SELECT
            datname,
            numbackends,
            xact_commit,
            xact_rollback,
            blks_read,
            blks_hit
          FROM
            pg_stat_database
          WHERE
            datname = $1
        `, [config.database]);

        if (statsRes.rows.length > 0) {
          result.details.stats = statsRes.rows[0];
        }
      }

      result.responseTime = Date.now() - startTime;
      result.status = 'up';
      result.details.connected = true;
    } catch (err) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = err.message;
      result.details.connected = false;
    } finally {
      if (client) {
        client.release();
      }
      await pool.end();
    }

    return result;
  }

  /**
   * MongoDB veritabanını kontrol eder
   * @param {Object} config - MongoDB bağlantı yapılandırması
   * @returns {Promise<Object>} - MongoDB kontrol sonuçları
   */
  async checkMongoDB(config) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    let client;
    try {
      // MongoDB bağlantı URL'si oluştur
      const auth = config.user && config.password
        ? `${encodeURIComponent(config.user)}:${encodeURIComponent(config.password)}@`
        : '';
      const url = `mongodb://${auth}${config.host}:${config.port || 27017}/${config.database || 'admin'}`;

      // MongoDB bağlantısı oluştur
      client = new MongoClient(url, {
        serverSelectionTimeoutMS: config.timeout || 5000
      });

      await client.connect();

      // Veritabanı durumunu kontrol et
      const adminDb = client.db('admin');

      // Sunucu durumunu kontrol et
      if (config.checkStatus) {
        const serverStatus = await adminDb.command({ serverStatus: 1 });
        result.details.serverStatus = {
          version: serverStatus.version,
          uptime: serverStatus.uptime,
          connections: serverStatus.connections,
          ok: serverStatus.ok
        };
      }

      // Veritabanı istatistiklerini kontrol et
      if (config.checkStats && config.database) {
        const db = client.db(config.database);
        const dbStats = await db.command({ dbStats: 1 });
        result.details.dbStats = {
          db: dbStats.db,
          collections: dbStats.collections,
          views: dbStats.views,
          objects: dbStats.objects,
          dataSize: dbStats.dataSize,
          storageSize: dbStats.storageSize,
          indexes: dbStats.indexes,
          indexSize: dbStats.indexSize
        };
      }

      result.responseTime = Date.now() - startTime;
      result.status = 'up';
      result.details.connected = true;
    } catch (err) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = err.message;
      result.details.connected = false;
    } finally {
      if (client) {
        await client.close();
      }
    }

    return result;
  }

  /**
   * Bir cihazın veritabanı durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // Veritabanı izleme yapılandırmasını al
      const config = device.monitors?.database || {};

      // Veritabanı türünü belirle
      const dbType = config.type || 'mysql';

      // Veritabanı bağlantı bilgilerini belirle
      const dbConfig = {
        host: config.host || device.host,
        port: config.port || this.getDefaultPort(dbType),
        user: config.user || 'root',
        password: config.password || '',
        database: config.database || 'mysql',
        timeout: config.timeout || 5000,
        checkStatus: config.checkStatus || false,
        checkVersion: config.checkVersion || true,
        checkStats: config.checkStats || false
      };

      console.log(`Database check for ${device.name}: Using ${dbType} on ${dbConfig.host}:${dbConfig.port}`);

      // Veritabanı türüne göre kontrol fonksiyonunu çağır
      let dbResult;
      switch (dbType.toLowerCase()) {
        case 'mysql':
          dbResult = await this.checkMySql(dbConfig);
          break;
        case 'postgresql':
        case 'postgres':
          dbResult = await this.checkPostgreSql(dbConfig);
          break;
        case 'mongodb':
        case 'mongo':
          dbResult = await this.checkMongoDB(dbConfig);
          break;
        default:
          throw new Error(`Unsupported database type: ${dbType}`);
      }

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: dbResult.status,
        responseTime: dbResult.responseTime,
        error: dbResult.error || '',
        details: {
          type: dbType,
          ...dbResult.details
        }
      };

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      const interval = parseInt(device.monitors.database.interval) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`Database check for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Sonuçları interval bilgisiyle kaydet
      await this.saveResults(device.id, result, io, parsedInterval);

      return dbResult;
    } catch (error) {
      console.error(`Database monitoring error for ${device.host}:`, error);

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      const interval = parseInt(device.monitors.database.interval) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`Database check error for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan (daha kısa interval ile)
      await this.saveError(device.id, error, io, parsedInterval, true);

      return {
        status: 'error',
        responseTime: 0,
        error: error.message,
        details: {}
      };
    }
  }

  /**
   * Veritabanı türüne göre varsayılan port numarasını döndürür
   * @param {string} dbType - Veritabanı türü
   * @returns {number} - Varsayılan port numarası
   */
  getDefaultPort(dbType) {
    switch (dbType.toLowerCase()) {
      case 'mysql':
        return 3306;
      case 'postgresql':
      case 'postgres':
        return 5432;
      case 'mongodb':
      case 'mongo':
        return 27017;
      default:
        return 3306;
    }
  }
}

// Singleton instance oluştur
const databaseMonitor = new DatabaseMonitor();

module.exports = {
  checkMySql: (config) => databaseMonitor.checkMySql(config),
  checkPostgreSql: (config) => databaseMonitor.checkPostgreSql(config),
  checkMongoDB: (config) => databaseMonitor.checkMongoDB(config),
  monitorDevice: (device, io) => databaseMonitor.monitorDevice(device, io),
  getResults: (deviceId) => databaseMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => databaseMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => databaseMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => databaseMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => databaseMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => databaseMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
