const BaseMonitor = require('./base-monitor');
const Docker = require('dockerode');
const dns = require('dns').promises;

/**
 * Container Docker API İzleyici Sınıfı
 * Docker API ile container'ların durumunu izler
 */
class DockerMonitor extends BaseMonitor {
  constructor() {
    super('docker');
    console.log('Docker Monitor initialized');
  }

  /**
   * Docker sunucusunun container bilgilerini Docker API ile alır
   * @param {string} host - Docker sunucu adresi
   * @param {number} port - Docker API port (varsayılan: 2376)
   * @param {boolean} secure - HTTPS kullanılsın mı (varsayılan: false)
   * @param {string} certPath - <PERSON><PERSON><PERSON><PERSON> yolu (opsiyonel)
   * @param {number} timeout - Bağlantı timeout (ms)
   * @returns {Promise<Object>} - Container bilgileri
   */
  async checkDockerContainers(host, port = 2376, secure = false, certPath = null, timeout = 10000) {
    const startTime = process.hrtime();
    
    try {
      console.log(`Docker container check: ${host}:${port} (secure: ${secure})`);
      
      // DNS çözümleme
      let resolvedHost;
      try {
        const addresses = await dns.lookup(host);
        resolvedHost = addresses.address;
      } catch (dnsError) {
        return {
          success: false,
          responseTime: 0,
          error: `DNS resolution failed: ${dnsError.message}`,
          details: { host }
        };
      }

      // Docker bağlantı ayarları
      const dockerOptions = {
        host: resolvedHost,
        port: port,
        timeout: timeout
      };

      // HTTPS kullanılacaksa
      if (secure) {
        dockerOptions.protocol = 'https';
        if (certPath) {
          dockerOptions.ca = certPath;
        }
      }

      // Docker client oluştur
      const docker = new Docker(dockerOptions);

      // Paralel olarak Docker bilgilerini al
      const [containerList, systemInfo, dockerVersion, imageList, networkList] = await Promise.all([
        this.getContainerList(docker),
        this.getSystemInfo(docker),
        this.getDockerVersion(docker),
        this.getImageList(docker),
        this.getNetworkList(docker)
      ]);

      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));

      // Container sağlığını değerlendir
      const containerHealth = this.evaluateContainerHealth({
        containers: containerList,
        system: systemInfo,
        version: dockerVersion,
        images: imageList,
        networks: networkList
      });

      return {
        success: true,
        responseTime,
        error: '',
        details: {
          host: resolvedHost,
          protocol: 'Docker API',
          containers: containerList,
          system: systemInfo,
          version: dockerVersion,
          images: imageList,
          networks: networkList,
          health: containerHealth,
          timestamp: Date.now()
        }
      };

    } catch (error) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));
      
      return {
        success: false,
        responseTime,
        error: error.message,
        details: { host }
      };
    }
  }

  /**
   * Container listesini Docker API ile alır
   * @param {Docker} docker - Docker client
   * @returns {Promise<Array>} - Container bilgileri
   */
  async getContainerList(docker) {
    try {
      const containers = await docker.listContainers({ all: true });
      
      return containers.map(container => ({
        id: container.Id.substring(0, 12),
        name: container.Names[0].replace('/', ''),
        image: container.Image,
        state: container.State,
        status: container.Status,
        created: new Date(container.Created * 1000).toISOString(),
        ports: container.Ports.map(port => ({
          privatePort: port.PrivatePort,
          publicPort: port.PublicPort || null,
          type: port.Type
        })),
        labels: container.Labels || {},
        command: container.Command
      }));
    } catch (error) {
      console.error('Error getting container list:', error);
      return [{ id: 'Error', name: 'Error', image: 'Error', state: 'error', status: 'error', error: error.message }];
    }
  }

  /**
   * Sistem bilgilerini Docker API ile alır
   * @param {Docker} docker - Docker client
   * @returns {Promise<Object>} - Sistem bilgileri
   */
  async getSystemInfo(docker) {
    try {
      const info = await docker.info();
      
      return {
        containers: info.Containers || 0,
        containersRunning: info.ContainersRunning || 0,
        containersPaused: info.ContainersPaused || 0,
        containersStopped: info.ContainersStopped || 0,
        images: info.Images || 0,
        serverVersion: info.ServerVersion || 'Unknown',
        kernelVersion: info.KernelVersion || 'Unknown',
        operatingSystem: info.OperatingSystem || 'Unknown',
        architecture: info.Architecture || 'Unknown',
        cpus: info.NCPU || 0,
        memoryTotal: Math.round((info.MemTotal || 0) / (1024 * 1024 * 1024)), // GB
        dockerRootDir: info.DockerRootDir || 'Unknown',
        driver: info.Driver || 'Unknown'
      };
    } catch (error) {
      console.error('Error getting system info:', error);
      return { containers: 0, containersRunning: 0, images: 0, serverVersion: 'Error', error: error.message };
    }
  }

  /**
   * Docker versiyonunu Docker API ile alır
   * @param {Docker} docker - Docker client
   * @returns {Promise<Object>} - Version bilgileri
   */
  async getDockerVersion(docker) {
    try {
      const version = await docker.version();
      
      return {
        version: version.Version || 'Unknown',
        apiVersion: version.ApiVersion || 'Unknown',
        minAPIVersion: version.MinAPIVersion || 'Unknown',
        gitCommit: version.GitCommit || 'Unknown',
        goVersion: version.GoVersion || 'Unknown',
        os: version.Os || 'Unknown',
        arch: version.Arch || 'Unknown',
        buildTime: version.BuildTime || 'Unknown'
      };
    } catch (error) {
      console.error('Error getting Docker version:', error);
      return { version: 'Error', apiVersion: 'Unknown', error: error.message };
    }
  }

  /**
   * Image listesini Docker API ile alır
   * @param {Docker} docker - Docker client
   * @returns {Promise<Array>} - Image bilgileri
   */
  async getImageList(docker) {
    try {
      const images = await docker.listImages();
      
      return images.slice(0, 10).map(image => ({ // İlk 10 image
        id: image.Id.substring(7, 19), // sha256: kısmını çıkar
        repoTags: image.RepoTags || ['<none>'],
        size: Math.round((image.Size || 0) / (1024 * 1024)), // MB
        created: new Date((image.Created || 0) * 1000).toISOString()
      }));
    } catch (error) {
      console.error('Error getting image list:', error);
      return [{ id: 'Error', repoTags: ['Error'], size: 0, created: 'Error', error: error.message }];
    }
  }

  /**
   * Network listesini Docker API ile alır
   * @param {Docker} docker - Docker client
   * @returns {Promise<Array>} - Network bilgileri
   */
  async getNetworkList(docker) {
    try {
      const networks = await docker.listNetworks();
      
      return networks.map(network => ({
        id: network.Id.substring(0, 12),
        name: network.Name,
        driver: network.Driver,
        scope: network.Scope,
        internal: network.Internal || false,
        attachable: network.Attachable || false,
        containers: Object.keys(network.Containers || {}).length
      }));
    } catch (error) {
      console.error('Error getting network list:', error);
      return [{ id: 'Error', name: 'Error', driver: 'Error', scope: 'Error', error: error.message }];
    }
  }

  /**
   * Container sağlığını değerlendirir
   * @param {Object} dockerData - Docker verileri
   * @returns {Object} - Sağlık durumu
   */
  evaluateContainerHealth(dockerData) {
    const issues = [];
    let score = 100;

    // Container durumu kontrolü
    if (dockerData.containers && dockerData.containers.length > 0) {
      const runningContainers = dockerData.containers.filter(c => c.state === 'running').length;
      const stoppedContainers = dockerData.containers.filter(c => c.state === 'exited').length;
      const errorContainers = dockerData.containers.filter(c => c.state === 'error' || c.state === 'dead').length;

      if (errorContainers > 0) {
        issues.push(`${errorContainers} container hatalı durumda`);
        score -= (errorContainers * 20);
      }

      if (stoppedContainers > 0) {
        issues.push(`${stoppedContainers} container durdurulmuş`);
        score -= (stoppedContainers * 5);
      }

      // Çok fazla container varsa uyarı
      if (dockerData.containers.length > 50) {
        issues.push(`Çok fazla container: ${dockerData.containers.length}`);
        score -= 10;
      }
    }

    // Sistem kaynak kontrolü
    if (dockerData.system) {
      // Memory kontrolü (eğer varsa)
      if (dockerData.system.memoryTotal > 0) {
        // Bu bilgi Docker API'den tam olarak alınamıyor, genel kontrol
        if (dockerData.system.containers > dockerData.system.cpus * 10) {
          issues.push('CPU başına çok fazla container');
          score -= 15;
        }
      }
    }

    // Image kontrolü
    if (dockerData.images && dockerData.images.length > 100) {
      issues.push(`Çok fazla image: ${dockerData.images.length}`);
      score -= 5;
    }

    // Network kontrolü
    if (dockerData.networks && dockerData.networks.length > 20) {
      issues.push(`Çok fazla network: ${dockerData.networks.length}`);
      score -= 5;
    }

    // Durum belirleme
    let status = 'up';
    if (score < 30) {
      status = 'critical';
    } else if (score < 50) {
      status = 'warning';
    } else if (score < 80) {
      status = 'degraded';
    }

    return {
      status,
      score: Math.max(0, score),
      issues,
      summary: issues.length > 0 ? issues.join(', ') : 'Containerlar normal çalışıyor'
    };
  }

  /**
   * Bir cihazın Docker container durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @param {boolean} isManualCheck - Manuel kontrol mu?
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io, isManualCheck = false) {
    try {
      // Docker izleme yapılandırmasını al
      const config = device.monitors?.docker || {};

      // Varsayılan değerler
      const host = config.host || device.host;
      const port = config.port || 2376;
      const secure = config.secure || false;
      const certPath = config.certPath || null;
      const timeout = config.timeout || 10000;

      console.log(`Docker container check for ${device.name}: ${host}:${port} (secure: ${secure})`);

      // Docker container kontrolü
      const dockerResult = await this.checkDockerContainers(host, port, secure, certPath, timeout);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: dockerResult.success ? dockerResult.details.health.status : 'down',
        responseTime: dockerResult.responseTime,
        error: dockerResult.error || '',
        details: {
          protocol: 'Docker API',
          ...dockerResult.details
        }
      };

      // Sonuçları kaydet
      await this.saveResults(device.id, result, io, isManualCheck);

      return result;

    } catch (error) {
      console.error(`Docker monitoring error for ${device.name}:`, error);

      const result = {
        status: 'down',
        responseTime: 0,
        error: error.message,
        details: {
          protocol: 'Docker API',
          host: device.host
        }
      };

      // Hata sonucunu kaydet
      await this.saveResults(device.id, result, io, isManualCheck);

      return result;
    }
  }
}

// Singleton instance oluştur
const dockerMonitor = new DockerMonitor();

module.exports = {
  checkDockerContainers: (host, port, secure, certPath, timeout) =>
    dockerMonitor.checkDockerContainers(host, port, secure, certPath, timeout),
  monitorDevice: (device, io, isManualCheck) =>
    dockerMonitor.monitorDevice(device, io, isManualCheck),
  evaluateContainerHealth: (dockerData) => dockerMonitor.evaluateContainerHealth(dockerData),
  getResults: (deviceId) => dockerMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => dockerMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => dockerMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => dockerMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => dockerMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval) => dockerMonitor.updateNextCheckTime(deviceId, interval)
};
