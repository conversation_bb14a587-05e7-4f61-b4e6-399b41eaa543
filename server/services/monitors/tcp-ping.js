const net = require('net');
const redisClient = require('../../config/redis');

/**
 * TCP ping ile cihazın durumunu kontrol eder
 * @param {string} host - Cihazın IP adresi veya hostname
 * @param {number} port - Kontrol edilecek port (varsayılan: 80)
 * @param {number} timeout - Zaman aşımı süresi (ms)
 * @returns {Promise<Object>} - Ping sonuçları
 */
function tcpPing(host, port = 80, timeout = 1000) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    const socket = new net.Socket();

    socket.setTimeout(timeout);

    // Bağlantı başarılı olduğunda
    socket.on('connect', () => {
      const responseTime = Date.now() - startTime;
      socket.destroy();
      resolve({
        host,
        alive: true,
        time: responseTime,
        min: responseTime,
        max: responseTime,
        avg: responseTime,
        stddev: 0,
        packetLoss: 0,
        status: 'up'
      });
    });

    // Hata durumunda
    socket.on('error', (error) => {
      socket.destroy();
      resolve({
        host,
        alive: false,
        time: null,
        min: null,
        max: null,
        avg: null,
        stddev: null,
        packetLoss: 100,
        status: 'down',
        error: error.message
      });
    });

    // Zaman aşımı durumunda
    socket.on('timeout', () => {
      socket.destroy();
      resolve({
        host,
        alive: false,
        time: null,
        min: null,
        max: null,
        avg: null,
        stddev: null,
        packetLoss: 100,
        status: 'down',
        error: 'Timeout'
      });
    });

    // Bağlantıyı dene
    socket.connect(port, host);
  });
}

/**
 * Bir cihazın TCP ping durumunu kontrol eder ve sonuçları Redis'e kaydeder
 * @param {Object} device - Cihaz bilgileri
 * @param {Object} io - Socket.io nesnesi
 * @returns {Promise<Object>} - İzleme sonuçları
 */
const monitorDevice = async (device, io) => {
  try {
    const host = device.host;

    // Birden fazla portu dene (yaygın portlar)
    const commonPorts = [80, 443, 22, 21, 25, 3389, 8080, 8443];
    const port = device.monitors?.icmp?.port || 80; // Kullanıcı tanımlı port veya varsayılan

    // Önce kullanıcı tanımlı portu dene
    let result = await tcpPing(host, port);

    // Eğer başarısız olursa, yaygın portları dene
    if (!result.alive) {
      console.log(`TCP ping failed on port ${port} for ${host}, trying common ports...`);

      for (const commonPort of commonPorts) {
        if (commonPort === port) continue; // Zaten denenen portu atla

        const portResult = await tcpPing(host, commonPort);
        if (portResult.alive) {
          console.log(`TCP ping succeeded on port ${commonPort} for ${host}`);
          result = portResult;
          break;
        }
      }
    }

    // Sonuçları Redis'e kaydet
    const key = `monitor:icmp:${device.id}`;
    await redisClient.hmset(key, {
      'status': result.alive ? 'up' : 'down',
      'responseTime': result.time || 0,
      'lastCheck': Date.now(),
      'error': result.error || '',
      'details': JSON.stringify(result)
    });

    // TTL ayarla (24 saat)
    await redisClient.expire(key, 86400);

    // Geçmiş verileri kaydet (son 100 ölçüm)
    const historyKey = `history:icmp:${device.id}`;
    await redisClient.lpush(historyKey, JSON.stringify({
      timestamp: Date.now(),
      status: result.alive ? 'up' : 'down',
      responseTime: result.time || 0
    }));
    await redisClient.ltrim(historyKey, 0, 99);

    // Socket.io ile gerçek zamanlı güncelleme gönder
    if (io) {
      io.emit('monitor:update', {
        type: 'icmp',
        deviceId: device.id,
        data: {
          status: result.alive ? 'up' : 'down',
          responseTime: result.time || 0,
          lastCheck: Date.now(),
          error: result.error || '',
          details: result
        }
      });
    }

    return result;
  } catch (error) {
    console.error(`TCP ping error for ${device.host}:`, error);

    // Hata durumunda da Redis'e kaydet
    const key = `monitor:icmp:${device.id}`;
    await redisClient.hmset(key, {
      'status': 'error',
      'responseTime': 0,
      'lastCheck': Date.now(),
      'error': error.message
    });

    // Socket.io ile hata bilgisini gönder
    if (io) {
      io.emit('monitor:update', {
        type: 'icmp',
        deviceId: device.id,
        data: {
          status: 'error',
          responseTime: 0,
          lastCheck: Date.now(),
          error: error.message
        }
      });
    }

    return {
      host: device.host,
      alive: false,
      error: error.message,
      time: null
    };
  }
};

module.exports = {
  tcpPing,
  monitorDevice
};
