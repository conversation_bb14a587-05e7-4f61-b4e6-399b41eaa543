const BaseMonitor = require('./base-monitor');
const net = require('net');
const tls = require('tls');
const dns = require('dns').promises;

/**
 * SMTP İzleyici Sınıfı
 * SMTP, POP3, IMAP protokollerini destekler
 */
class SmtpMonitor extends BaseMonitor {
  constructor() {
    super('smtp');
    console.log('SMTP Monitor initialized');
  }

  /**
   * SMTP sunucusuna bağlantı testi yapar
   * @param {string} host - SMTP sunucu adresi
   * @param {number} port - SMTP port (25, 465, 587)
   * @param {boolean} secure - SSL/TLS kullanılacak mı
   * @param {number} timeout - Bağlantı timeout (ms)
   * @param {Object} auth - Authentication bilgileri (opsiyonel)
   * @returns {Promise<Object>} - Test sonucu
   */
  async checkSmtp(host, port = 25, secure = false, timeout = 5000, auth = null) {
    const startTime = process.hrtime();
    
    try {
      console.log(`SMTP check: ${host}:${port} (secure: ${secure})`);
      
      // DNS çözümleme
      let resolvedHost;
      try {
        const addresses = await dns.lookup(host);
        resolvedHost = addresses.address;
      } catch (dnsError) {
        return {
          success: false,
          responseTime: 0,
          error: `DNS resolution failed: ${dnsError.message}`,
          details: { host, port, secure }
        };
      }

      // SMTP bağlantısı
      const result = await this.connectToSmtp(resolvedHost, port, secure, timeout, auth);
      
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));
      
      return {
        success: result.success,
        responseTime,
        error: result.error || '',
        details: {
          host: resolvedHost,
          port,
          secure,
          protocol: 'SMTP',
          greeting: result.greeting || '',
          authSupported: result.authSupported || false,
          tlsSupported: result.tlsSupported || false
        }
      };
      
    } catch (error) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));
      
      return {
        success: false,
        responseTime,
        error: error.message,
        details: { host, port, secure }
      };
    }
  }

  /**
   * SMTP sunucusuna bağlanır ve temel komutları test eder
   * @param {string} host - Sunucu adresi
   * @param {number} port - Port numarası
   * @param {boolean} secure - SSL/TLS kullanımı
   * @param {number} timeout - Timeout süresi
   * @param {Object} auth - Authentication bilgileri
   * @returns {Promise<Object>} - Bağlantı sonucu
   */
  async connectToSmtp(host, port, secure, timeout, auth) {
    return new Promise((resolve) => {
      let socket;
      let isResolved = false;
      let greeting = '';
      let authSupported = false;
      let tlsSupported = false;
      
      const cleanup = () => {
        if (socket && !socket.destroyed) {
          socket.destroy();
        }
      };
      
      const resolveOnce = (result) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve(result);
        }
      };
      
      // Timeout
      const timer = setTimeout(() => {
        resolveOnce({
          success: false,
          error: `Connection timeout after ${timeout}ms`
        });
      }, timeout);
      
      try {
        // Socket oluştur
        if (secure) {
          socket = tls.connect({ host, port, rejectUnauthorized: false });
        } else {
          socket = net.createConnection({ host, port });
        }
        
        socket.setTimeout(timeout);
        
        let buffer = '';
        let step = 'greeting';
        
        socket.on('data', (data) => {
          buffer += data.toString();
          
          // Satır satır işle
          const lines = buffer.split('\r\n');
          buffer = lines.pop(); // Son satır incomplete olabilir
          
          for (const line of lines) {
            if (!line.trim()) continue;
            
            console.log(`SMTP Response: ${line}`);
            
            if (step === 'greeting') {
              if (line.startsWith('220')) {
                greeting = line;
                // EHLO komutu gönder
                socket.write('EHLO localhost\r\n');
                step = 'ehlo';
              } else {
                resolveOnce({
                  success: false,
                  error: `Invalid greeting: ${line}`
                });
                return;
              }
            } else if (step === 'ehlo') {
              if (line.startsWith('250')) {
                // EHLO yanıtlarını kontrol et
                if (line.includes('AUTH')) {
                  authSupported = true;
                }
                if (line.includes('STARTTLS')) {
                  tlsSupported = true;
                }
                
                // Son 250 satırı mı?
                if (line.startsWith('250 ') || !line.startsWith('250-')) {
                  // QUIT komutu gönder
                  socket.write('QUIT\r\n');
                  step = 'quit';
                }
              } else {
                resolveOnce({
                  success: false,
                  error: `EHLO failed: ${line}`
                });
                return;
              }
            } else if (step === 'quit') {
              if (line.startsWith('221')) {
                clearTimeout(timer);
                resolveOnce({
                  success: true,
                  greeting,
                  authSupported,
                  tlsSupported
                });
                return;
              }
            }
          }
        });
        
        socket.on('error', (error) => {
          clearTimeout(timer);
          resolveOnce({
            success: false,
            error: error.message
          });
        });
        
        socket.on('timeout', () => {
          clearTimeout(timer);
          resolveOnce({
            success: false,
            error: 'Socket timeout'
          });
        });
        
        socket.on('close', () => {
          if (step !== 'quit') {
            clearTimeout(timer);
            resolveOnce({
              success: false,
              error: 'Connection closed unexpectedly'
            });
          }
        });
        
      } catch (error) {
        clearTimeout(timer);
        resolveOnce({
          success: false,
          error: error.message
        });
      }
    });
  }

  /**
   * POP3 sunucusuna bağlantı testi yapar
   * @param {string} host - POP3 sunucu adresi
   * @param {number} port - POP3 port (110, 995)
   * @param {boolean} secure - SSL/TLS kullanılacak mı
   * @param {number} timeout - Bağlantı timeout (ms)
   * @returns {Promise<Object>} - Test sonucu
   */
  async checkPop3(host, port = 110, secure = false, timeout = 5000) {
    const startTime = process.hrtime();

    try {
      console.log(`POP3 check: ${host}:${port} (secure: ${secure})`);

      // DNS çözümleme
      let resolvedHost;
      try {
        const addresses = await dns.lookup(host);
        resolvedHost = addresses.address;
      } catch (dnsError) {
        return {
          success: false,
          responseTime: 0,
          error: `DNS resolution failed: ${dnsError.message}`,
          details: { host, port, secure }
        };
      }

      // POP3 bağlantısı
      const result = await this.connectToPop3(resolvedHost, port, secure, timeout);

      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));

      return {
        success: result.success,
        responseTime,
        error: result.error || '',
        details: {
          host: resolvedHost,
          port,
          secure,
          protocol: 'POP3',
          greeting: result.greeting || ''
        }
      };

    } catch (error) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));

      return {
        success: false,
        responseTime,
        error: error.message,
        details: { host, port, secure }
      };
    }
  }

  /**
   * POP3 sunucusuna bağlanır ve temel komutları test eder
   * @param {string} host - Sunucu adresi
   * @param {number} port - Port numarası
   * @param {boolean} secure - SSL/TLS kullanımı
   * @param {number} timeout - Timeout süresi
   * @returns {Promise<Object>} - Bağlantı sonucu
   */
  async connectToPop3(host, port, secure, timeout) {
    return new Promise((resolve) => {
      let socket;
      let isResolved = false;
      let greeting = '';

      const cleanup = () => {
        if (socket && !socket.destroyed) {
          socket.destroy();
        }
      };

      const resolveOnce = (result) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve(result);
        }
      };

      // Timeout
      const timer = setTimeout(() => {
        resolveOnce({
          success: false,
          error: `Connection timeout after ${timeout}ms`
        });
      }, timeout);

      try {
        // Socket oluştur
        if (secure) {
          socket = tls.connect({ host, port, rejectUnauthorized: false });
        } else {
          socket = net.createConnection({ host, port });
        }

        socket.setTimeout(timeout);

        let buffer = '';
        let step = 'greeting';

        socket.on('data', (data) => {
          buffer += data.toString();

          // Satır satır işle
          const lines = buffer.split('\r\n');
          buffer = lines.pop();

          for (const line of lines) {
            if (!line.trim()) continue;

            console.log(`POP3 Response: ${line}`);

            if (step === 'greeting') {
              if (line.startsWith('+OK')) {
                greeting = line;
                // QUIT komutu gönder
                socket.write('QUIT\r\n');
                step = 'quit';
              } else {
                resolveOnce({
                  success: false,
                  error: `Invalid greeting: ${line}`
                });
                return;
              }
            } else if (step === 'quit') {
              if (line.startsWith('+OK')) {
                clearTimeout(timer);
                resolveOnce({
                  success: true,
                  greeting
                });
                return;
              }
            }
          }
        });

        socket.on('error', (error) => {
          clearTimeout(timer);
          resolveOnce({
            success: false,
            error: error.message
          });
        });

        socket.on('timeout', () => {
          clearTimeout(timer);
          resolveOnce({
            success: false,
            error: 'Socket timeout'
          });
        });

        socket.on('close', () => {
          if (step !== 'quit') {
            clearTimeout(timer);
            resolveOnce({
              success: false,
              error: 'Connection closed unexpectedly'
            });
          }
        });

      } catch (error) {
        clearTimeout(timer);
        resolveOnce({
          success: false,
          error: error.message
        });
      }
    });
  }

  /**
   * IMAP sunucusuna bağlantı testi yapar
   * @param {string} host - IMAP sunucu adresi
   * @param {number} port - IMAP port (143, 993)
   * @param {boolean} secure - SSL/TLS kullanılacak mı
   * @param {number} timeout - Bağlantı timeout (ms)
   * @returns {Promise<Object>} - Test sonucu
   */
  async checkImap(host, port = 143, secure = false, timeout = 5000) {
    const startTime = process.hrtime();

    try {
      console.log(`IMAP check: ${host}:${port} (secure: ${secure})`);

      // DNS çözümleme
      let resolvedHost;
      try {
        const addresses = await dns.lookup(host);
        resolvedHost = addresses.address;
      } catch (dnsError) {
        return {
          success: false,
          responseTime: 0,
          error: `DNS resolution failed: ${dnsError.message}`,
          details: { host, port, secure }
        };
      }

      // IMAP bağlantısı
      const result = await this.connectToImap(resolvedHost, port, secure, timeout);

      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));

      return {
        success: result.success,
        responseTime,
        error: result.error || '',
        details: {
          host: resolvedHost,
          port,
          secure,
          protocol: 'IMAP',
          greeting: result.greeting || '',
          capabilities: result.capabilities || []
        }
      };

    } catch (error) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const responseTime = Math.round((seconds * 1000) + (nanoseconds / 1000000));

      return {
        success: false,
        responseTime,
        error: error.message,
        details: { host, port, secure }
      };
    }
  }

  /**
   * IMAP sunucusuna bağlanır ve temel komutları test eder
   * @param {string} host - Sunucu adresi
   * @param {number} port - Port numarası
   * @param {boolean} secure - SSL/TLS kullanımı
   * @param {number} timeout - Timeout süresi
   * @returns {Promise<Object>} - Bağlantı sonucu
   */
  async connectToImap(host, port, secure, timeout) {
    return new Promise((resolve) => {
      let socket;
      let isResolved = false;
      let greeting = '';
      let capabilities = [];

      const cleanup = () => {
        if (socket && !socket.destroyed) {
          socket.destroy();
        }
      };

      const resolveOnce = (result) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve(result);
        }
      };

      // Timeout
      const timer = setTimeout(() => {
        resolveOnce({
          success: false,
          error: `Connection timeout after ${timeout}ms`
        });
      }, timeout);

      try {
        // Socket oluştur
        if (secure) {
          socket = tls.connect({ host, port, rejectUnauthorized: false });
        } else {
          socket = net.createConnection({ host, port });
        }

        socket.setTimeout(timeout);

        let buffer = '';
        let step = 'greeting';
        let tagCounter = 1;

        socket.on('data', (data) => {
          buffer += data.toString();

          // Satır satır işle
          const lines = buffer.split('\r\n');
          buffer = lines.pop();

          for (const line of lines) {
            if (!line.trim()) continue;

            console.log(`IMAP Response: ${line}`);

            if (step === 'greeting') {
              if (line.startsWith('* OK')) {
                greeting = line;
                // CAPABILITY komutu gönder
                socket.write(`A${tagCounter} CAPABILITY\r\n`);
                tagCounter++;
                step = 'capability';
              } else {
                resolveOnce({
                  success: false,
                  error: `Invalid greeting: ${line}`
                });
                return;
              }
            } else if (step === 'capability') {
              if (line.startsWith('* CAPABILITY')) {
                capabilities = line.split(' ').slice(2);
              } else if (line.startsWith(`A${tagCounter - 1} OK`)) {
                // LOGOUT komutu gönder
                socket.write(`A${tagCounter} LOGOUT\r\n`);
                tagCounter++;
                step = 'logout';
              }
            } else if (step === 'logout') {
              if (line.startsWith(`A${tagCounter - 1} OK`)) {
                clearTimeout(timer);
                resolveOnce({
                  success: true,
                  greeting,
                  capabilities
                });
                return;
              }
            }
          }
        });

        socket.on('error', (error) => {
          clearTimeout(timer);
          resolveOnce({
            success: false,
            error: error.message
          });
        });

        socket.on('timeout', () => {
          clearTimeout(timer);
          resolveOnce({
            success: false,
            error: 'Socket timeout'
          });
        });

        socket.on('close', () => {
          if (step !== 'logout') {
            clearTimeout(timer);
            resolveOnce({
              success: false,
              error: 'Connection closed unexpectedly'
            });
          }
        });

      } catch (error) {
        clearTimeout(timer);
        resolveOnce({
          success: false,
          error: error.message
        });
      }
    });
  }

  /**
   * Bir cihazın email servislerini kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @param {boolean} isManualCheck - Manuel kontrol mu?
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io, isManualCheck = false) {
    try {
      // Email izleme yapılandırmasını al
      const config = device.monitors?.smtp || {};

      // Varsayılan değerler
      const protocol = config.protocol || 'smtp';
      const host = config.host || device.host;
      const port = config.port || this.getDefaultPort(protocol, config.secure);
      const secure = config.secure || false;
      const timeout = config.timeout || 5000;
      const auth = config.auth || null;

      console.log(`Email check for ${device.name}: ${protocol.toUpperCase()} ${host}:${port} (secure: ${secure})`);

      // Protokole göre kontrol fonksiyonunu çağır
      let emailResult;
      switch (protocol.toLowerCase()) {
        case 'smtp':
          emailResult = await this.checkSmtp(host, port, secure, timeout, auth);
          break;
        case 'pop3':
          emailResult = await this.checkPop3(host, port, secure, timeout);
          break;
        case 'imap':
          emailResult = await this.checkImap(host, port, secure, timeout);
          break;
        default:
          throw new Error(`Unsupported email protocol: ${protocol}`);
      }

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: emailResult.success ? 'up' : 'down',
        responseTime: emailResult.responseTime,
        error: emailResult.error || '',
        details: {
          protocol: protocol.toUpperCase(),
          ...emailResult.details
        }
      };

      // Sonuçları kaydet
      await this.saveResults(device.id, result, io, isManualCheck);

      return result;

    } catch (error) {
      console.error(`Email monitoring error for ${device.name}:`, error);

      const result = {
        status: 'down',
        responseTime: 0,
        error: error.message,
        details: {
          protocol: 'EMAIL',
          host: device.host
        }
      };

      // Hata sonucunu kaydet
      await this.saveResults(device.id, result, io, isManualCheck);

      return result;
    }
  }

  /**
   * Protokol ve güvenlik ayarına göre varsayılan port döndürür
   * @param {string} protocol - Email protokolü (smtp, pop3, imap)
   * @param {boolean} secure - SSL/TLS kullanımı
   * @returns {number} - Varsayılan port numarası
   */
  getDefaultPort(protocol, secure = false) {
    const ports = {
      smtp: secure ? 465 : 25,
      pop3: secure ? 995 : 110,
      imap: secure ? 993 : 143
    };

    return ports[protocol.toLowerCase()] || 25;
  }
}

// Singleton instance oluştur
const smtpMonitor = new SmtpMonitor();

module.exports = {
  checkSmtp: (host, port, secure, timeout, auth) => smtpMonitor.checkSmtp(host, port, secure, timeout, auth),
  checkPop3: (host, port, secure, timeout) => smtpMonitor.checkPop3(host, port, secure, timeout),
  checkImap: (host, port, secure, timeout) => smtpMonitor.checkImap(host, port, secure, timeout),
  monitorDevice: (device, io, isManualCheck) => smtpMonitor.monitorDevice(device, io, isManualCheck),
  getResults: (deviceId) => smtpMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => smtpMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => smtpMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => smtpMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => smtpMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval) => smtpMonitor.updateNextCheckTime(deviceId, interval)
};
