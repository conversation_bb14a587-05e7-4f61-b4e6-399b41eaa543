/**
 * IPMI izleme servisi
 * Sunucu donanım durumunu izlemek için ipmitool kullanır
 */
const { exec } = require('child_process');
const BaseMonitor = require('./base-monitor');

class IpmiMonitor extends BaseMonitor {
  constructor() {
    super('ipmi');
    console.log('IPMI Monitor initialized using ipmitool command');
  }

  /**
   * ipmitool komutunun mevcut olup olmadığını kontrol eder
   * @returns {Promise<boolean>} - ipmitool mevcut mu?
   */
  async checkIpmitoolAvailable() {
    return new Promise((resolve) => {
      exec('which ipmitool', (error) => {
        resolve(!error);
      });
    });
  }

  /**
   * IPMI bağlantısını test eder
   * @param {Object} options - IPMI bağlantı seçenekleri
   * @returns {Promise<Object>} - IPMI test sonuçları
   */
  async testIpmiConnection(options) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    try {
      // ipmitool mevcut mu kontrol et
      const ipmitoolAvailable = await this.checkIpmitoolAvailable();
      if (!ipmitoolAvailable) {
        throw new Error('ipmitool command not found. Please install ipmitool package.');
      }

      // IPMI komutunu oluştur
      const { host, username, password, interface: ipmiInterface = 'lanplus' } = options;
      
      if (!host || !username || !password) {
        throw new Error('IPMI host, username and password are required');
      }

      // Basit bir chassis status komutu ile bağlantıyı test et
      const command = `ipmitool -I ${ipmiInterface} -H ${host} -U ${username} -P ${password} chassis status`;
      
      console.log(`Executing IPMI command for ${host}`);

      const output = await this.executeIpmiCommand(command, options.timeout || 10000);
      
      result.responseTime = Date.now() - startTime;
      result.status = 'up';
      result.details = this.parseChassisStatus(output);

    } catch (error) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = error.message;
    }

    return result;
  }

  /**
   * IPMI komutunu çalıştırır
   * @param {string} command - Çalıştırılacak IPMI komutu
   * @param {number} timeout - Zaman aşımı (ms)
   * @returns {Promise<string>} - Komut çıktısı
   */
  executeIpmiCommand(command, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const child = exec(command, { timeout }, (error, stdout, stderr) => {
        if (error) {
          // Timeout hatası
          if (error.code === 'ETIMEDOUT') {
            reject(new Error('IPMI command timed out'));
            return;
          }
          
          // Diğer hatalar
          reject(new Error(`IPMI command failed: ${error.message}`));
          return;
        }

        if (stderr) {
          console.warn(`IPMI command stderr: ${stderr}`);
        }

        resolve(stdout.trim());
      });

      // Manuel timeout kontrolü
      setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error('IPMI command timed out'));
      }, timeout);
    });
  }

  /**
   * Chassis status çıktısını parse eder
   * @param {string} output - ipmitool chassis status çıktısı
   * @returns {Object} - Parse edilmiş chassis bilgileri
   */
  parseChassisStatus(output) {
    const details = {
      powerState: 'unknown',
      powerRestorePolicy: 'unknown',
      lastPowerEvent: 'unknown',
      chassisIntrusion: 'unknown',
      frontPanelLockout: 'unknown',
      driveFault: 'unknown',
      coolingFanFault: 'unknown'
    };

    const lines = output.split('\n');
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      if (trimmedLine.includes('System Power')) {
        details.powerState = trimmedLine.includes('on') ? 'on' : 'off';
      } else if (trimmedLine.includes('Power Restore Policy')) {
        details.powerRestorePolicy = trimmedLine.split(':')[1]?.trim() || 'unknown';
      } else if (trimmedLine.includes('Last Power Event')) {
        details.lastPowerEvent = trimmedLine.split(':')[1]?.trim() || 'unknown';
      } else if (trimmedLine.includes('Chassis Intrusion')) {
        details.chassisIntrusion = trimmedLine.split(':')[1]?.trim() || 'unknown';
      } else if (trimmedLine.includes('Front-Panel Lockout')) {
        details.frontPanelLockout = trimmedLine.split(':')[1]?.trim() || 'unknown';
      } else if (trimmedLine.includes('Drive Fault')) {
        details.driveFault = trimmedLine.split(':')[1]?.trim() || 'unknown';
      } else if (trimmedLine.includes('Cooling/Fan Fault')) {
        details.coolingFanFault = trimmedLine.split(':')[1]?.trim() || 'unknown';
      }
    });

    return details;
  }

  /**
   * Sensor bilgilerini alır
   * @param {Object} options - IPMI bağlantı seçenekleri
   * @returns {Promise<Array>} - Sensor bilgileri
   */
  async getSensorData(options) {
    try {
      const { host, username, password, interface: ipmiInterface = 'lanplus' } = options;
      
      // Sensor listesi komutunu çalıştır
      const command = `ipmitool -I ${ipmiInterface} -H ${host} -U ${username} -P ${password} sensor list`;
      const output = await this.executeIpmiCommand(command, options.timeout || 15000);
      
      return this.parseSensorData(output);
    } catch (error) {
      console.error('Error getting IPMI sensor data:', error);
      return [];
    }
  }

  /**
   * Sensor verilerini parse eder
   * @param {string} output - ipmitool sensor list çıktısı
   * @returns {Array} - Parse edilmiş sensor bilgileri
   */
  parseSensorData(output) {
    const sensors = [];
    const lines = output.split('\n');
    
    lines.forEach(line => {
      const parts = line.split('|').map(part => part.trim());
      
      if (parts.length >= 3) {
        const sensor = {
          name: parts[0],
          value: parts[1] || 'na',
          unit: parts[2] || '',
          status: parts[3] || 'ok',
          lowerNonRecoverable: parts[4] || '',
          lowerCritical: parts[5] || '',
          lowerNonCritical: parts[6] || '',
          upperNonCritical: parts[7] || '',
          upperCritical: parts[8] || '',
          upperNonRecoverable: parts[9] || ''
        };
        
        // Sadece geçerli sensor verilerini ekle
        if (sensor.name && sensor.name !== 'Sensor ID') {
          sensors.push(sensor);
        }
      }
    });
    
    return sensors;
  }

  /**
   * Bir cihazın IPMI durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // IPMI izleme yapılandırmasını al
      const config = device.monitors?.ipmi || {};
      
      const options = {
        host: config.host || device.host,
        username: config.username,
        password: config.password,
        interface: config.interface || 'lanplus',
        timeout: config.timeout ? parseInt(config.timeout) : 10000
      };

      console.log(`IPMI check for ${device.name}: Using host ${options.host} with interface ${options.interface}`);

      // IPMI bağlantısını test et
      const ipmiResult = await this.testIpmiConnection(options);
      
      // Eğer bağlantı başarılıysa, sensor verilerini de al
      let sensors = [];
      if (ipmiResult.status === 'up') {
        sensors = await this.getSensorData(options);
      }

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: ipmiResult.status,
        responseTime: ipmiResult.responseTime,
        error: ipmiResult.error || '',
        details: {
          ...ipmiResult.details,
          sensors: sensors.slice(0, 20) // İlk 20 sensoru al (çok fazla veri olmasın)
        }
      };

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      // Eğer interval değeri yoksa, varsayılan olarak 30 dakika kullan
      const intervalValue = device.monitors.ipmi && device.monitors.ipmi.interval ? device.monitors.ipmi.interval : '30';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // Sonuçları interval bilgisiyle kaydet
      await this.saveResults(device.id, result, io, interval);

      return {
        host: options.host,
        success: ipmiResult.status === 'up',
        responseTime: ipmiResult.responseTime,
        chassisStatus: ipmiResult.details,
        sensors,
        error: ipmiResult.error
      };
    } catch (error) {
      console.error(`IPMI monitoring error for ${device.host}:`, error);

      // İzleme aralığını belirle
      const intervalValue = device.monitors.ipmi && device.monitors.ipmi.interval ? device.monitors.ipmi.interval : '30';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan
      await this.saveError(device.id, error, io, interval);

      return {
        host: device.host,
        success: false,
        responseTime: 0,
        chassisStatus: null,
        sensors: [],
        error: error.message
      };
    }
  }
}

// Singleton instance oluştur
const ipmiMonitor = new IpmiMonitor();

module.exports = {
  testIpmiConnection: (options) => ipmiMonitor.testIpmiConnection(options),
  getSensorData: (options) => ipmiMonitor.getSensorData(options),
  executeIpmiCommand: (command, timeout) => ipmiMonitor.executeIpmiCommand(command, timeout),
  parseChassisStatus: (output) => ipmiMonitor.parseChassisStatus(output),
  parseSensorData: (output) => ipmiMonitor.parseSensorData(output),
  checkIpmitoolAvailable: () => ipmiMonitor.checkIpmitoolAvailable(),
  monitorDevice: (device, io) => ipmiMonitor.monitorDevice(device, io),
  getResults: (deviceId) => ipmiMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => ipmiMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => ipmiMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => ipmiMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => ipmiMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => ipmiMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
