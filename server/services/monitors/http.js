/**
 * HTTP izleme servisi
 */
const axios = require('axios');
const BaseMonitor = require('./base-monitor');

class HttpMonitor extends BaseMonitor {
  constructor() {
    super('http');
  }

  /**
   * HTTP status kodunu kategoriye göre değerlendirir
   * @param {number} statusCode - HTTP status kodu
   * @param {string} error - Hata mesajı (varsa)
   * @returns {Object} - Kategori ve mesaj bilgisi
   */
  categorizeHttpStatus(statusCode, error = null) {
    // Bağlantı hatası
    if (statusCode === 0 || error) {
      if (error && error.includes('timeout')) {
        return { category: 'down', message: '<PERSON>ğlantı zaman aşımı' };
      }
      if (error && error.includes('ENOTFOUND')) {
        return { category: 'critical', message: 'DNS çözümlenemiyor' };
      }
      if (error && error.includes('ECONNREFUSED')) {
        return { category: 'critical', message: '<PERSON><PERSON><PERSON><PERSON> reddedildi' };
      }
      return { category: 'down', message: '<PERSON>ğlantı kurulamadı' };
    }

    // Başarılı (200-399)
    if (statusCode >= 200 && statusCode < 400) {
      if (statusCode === 301 || statusCode === 302) {
        return { category: 'up', message: 'Yönlendiriliyor (Normal)' };
      }
      return { category: 'up', message: 'Çevrimiçi' };
    }

    // Uyarı - İstemci hataları (400-499)
    if (statusCode >= 400 && statusCode < 500) {
      const messages = {
        400: 'Hatalı istek - URL\'yi kontrol edin',
        401: 'Kimlik doğrulama gerekli',
        403: 'Erişim yasak - İzinleri kontrol edin',
        404: 'Sayfa bulunamadı - URL\'yi kontrol edin',
        405: 'HTTP metodunu kontrol edin',
        429: 'Çok fazla istek - Aralığı artırın'
      };
      return {
        category: 'warning',
        message: messages[statusCode] || `HTTP ${statusCode} - İstemci hatası`
      };
    }

    // Çevrimdışı - Sunucu hataları (500-599)
    if (statusCode >= 500) {
      const messages = {
        500: 'Sunucu hatası',
        502: 'Proxy hatası',
        503: 'Servis kullanılamıyor',
        504: 'Sunucu zaman aşımı'
      };
      return {
        category: 'down',
        message: messages[statusCode] || `HTTP ${statusCode} - Sunucu hatası`
      };
    }

    // Bilinmeyen durum
    return { category: 'unknown', message: `Bilinmeyen durum: ${statusCode}` };
  }

  /**
   * HTTP isteği gönderir ve yanıtı kontrol eder
   * @param {Object} options - HTTP isteği seçenekleri
   * @returns {Promise<Object>} - HTTP isteği sonuçları
   */
  async checkHttp(options) {
    const startTime = Date.now();

    try {
      const response = await axios({
        method: options.method || 'GET',
        url: options.url,
        timeout: options.timeout || 5000,
        headers: options.headers || {},
        validateStatus: () => true // Tüm durum kodlarını kabul et
      });

      const responseTime = Date.now() - startTime;

      // Status kategorisini belirle
      const statusInfo = this.categorizeHttpStatus(response.status);

      return {
        url: options.url,
        status: response.status,
        statusText: response.statusText,
        responseTime,
        success: statusInfo.category === 'up',
        category: statusInfo.category,
        message: statusInfo.message,
        contentLength: response.headers['content-length'] || 0,
        contentType: response.headers['content-type'] || '',
        error: null
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const statusCode = error.response ? error.response.status : 0;
      const statusInfo = this.categorizeHttpStatus(statusCode, error.message);

      return {
        url: options.url,
        status: statusCode,
        statusText: error.response ? error.response.statusText : error.message,
        responseTime,
        success: false,
        category: statusInfo.category,
        message: statusInfo.message,
        contentLength: 0,
        contentType: '',
        error: error.message
      };
    }
  }

  /**
   * Bir cihazın HTTP durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // HTTP izleme yapılandırmasını al
      const config = device.monitors?.http || {};

      const options = {
        url: config.url || `http://${device.host}`,
        method: config.method || 'GET',
        timeout: config.timeout || 5000,
        headers: config.headers || {}
      };

      const httpResult = await this.checkHttp(options);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: httpResult.category, // 'up', 'warning', 'down', 'critical'
        responseTime: httpResult.responseTime,
        error: httpResult.error || '',
        message: httpResult.message, // Kullanıcı dostu mesaj
        details: {
          url: httpResult.url,
          statusCode: httpResult.status,
          statusText: httpResult.statusText,
          contentLength: httpResult.contentLength,
          contentType: httpResult.contentType,
          category: httpResult.category
        }
      };

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
      const intervalValue = device.monitors.http && device.monitors.http.interval ? device.monitors.http.interval : '5';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`HTTP check for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Sonuçları interval bilgisiyle kaydet
      await this.saveResults(device.id, result, io, parsedInterval);

      return httpResult;
    } catch (error) {
      console.error(`HTTP monitoring error for ${device.host}:`, error);

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      // Eğer interval değeri yoksa, varsayılan olarak 5 dakika kullan
      const intervalValue = device.monitors.http && device.monitors.http.interval ? device.monitors.http.interval : '5';
      const interval = parseInt(intervalValue) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`HTTP check error for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Hata kategorisini belirle
      const statusInfo = this.categorizeHttpStatus(0, error.message);

      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan
      await this.saveError(device.id, error, io, parsedInterval);

      return {
        url: device.host,
        status: 0,
        statusText: error.message,
        responseTime: 0,
        success: false,
        category: statusInfo.category,
        message: statusInfo.message,
        error: error.message
      };
    }
  }
}

// Singleton instance oluştur
const httpMonitor = new HttpMonitor();

module.exports = {
  checkHttp: (options) => httpMonitor.checkHttp(options),
  monitorDevice: (device, io) => httpMonitor.monitorDevice(device, io),
  getResults: (deviceId) => httpMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => httpMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => httpMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => httpMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => httpMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => httpMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
