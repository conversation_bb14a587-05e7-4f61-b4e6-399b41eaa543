/**
 * DNS izleme servisi
 */
const dns = require('dns');
const dnsPacket = require('dns-packet');
const dgram = require('dgram');
const { promisify } = require('util');
const fs = require('fs').promises;
const path = require('path');
const BaseMonitor = require('./base-monitor');

// DNS çözümleme işlemini promise'e dönüştür
const resolveDns = promisify(dns.resolve);

class DnsMonitor extends BaseMonitor {
  constructor() {
    super('dns');
  }

  /**
   * DNS sunucusunu belirler
   * @param {Object} config - DNS izleyici yapılandırması
   * @returns {string|null} - DNS sunucusu (null ise sistem DNS'i kullanılır)
   */
  async getDnsServer(config) {
    const serverSetting = config.server;

    // Eğer 'system' ise, null döndür (sistem DNS'i kullanılacak)
    if (serverSetting === 'system') {
      return null;
    }

    // Eğer 'default' ise, sistem ayarlarından varsayılan DNS sunucusunu al
    if (serverSetting === 'default') {
      try {
        // Ayarlar dosyasını oku
        const settingsPath = path.join(__dirname, '../../data/settings.json');
        const settingsData = await fs.readFile(settingsPath, 'utf8');
        const settings = JSON.parse(settingsData);

        // Varsayılan DNS sunucusunu döndür
        if (settings.defaultDnsServer && settings.defaultDnsServer.match(/^\d+\.\d+\.\d+\.\d+$/)) {
          return settings.defaultDnsServer;
        }
      } catch (error) {
        console.error('Varsayılan DNS sunucusu okunamadı:', error);
      }

      // Hata durumunda veya ayar bulunamazsa Google DNS'i kullan
      return '*******';
    }

    // Eğer 'custom' ise ve özel DNS sunucusu belirtilmişse, o adresi döndür
    if (serverSetting === 'custom' && config.customDnsServer && config.customDnsServer.match(/^\d+\.\d+\.\d+\.\d+$/)) {
      return config.customDnsServer;
    }

    // Eğer geçerli bir IP adresi ise, o adresi döndür
    if (serverSetting && serverSetting.match(/^\d+\.\d+\.\d+\.\d+$/)) {
      return serverSetting;
    }

    // Varsayılan olarak Google DNS'i kullan
    return '*******';
  }

  /**
   * DNS sunucusunu kontrol eder
   * @param {string} host - DNS sunucusu adresi
   * @param {Object} options - DNS sorgu seçenekleri
   * @returns {Promise<Object>} - DNS sorgu sonuçları
   */
  async checkDnsServer(host, options = {}) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    try {
      // DNS sunucusunu ayarla
      const originalServers = dns.getServers();
      dns.setServers([host]);

      // Sorgulanacak domain
      const domain = options.domain || 'google.com';
      const recordType = options.recordType || 'A';

      // DNS sorgusunu yap
      const records = await resolveDns(domain, recordType);

      // Yanıt süresini hesapla
      result.responseTime = Date.now() - startTime;
      result.status = 'up';
      result.details = {
        records,
        recordType,
        domain
      };

      // Orijinal DNS sunucularını geri yükle
      dns.setServers(originalServers);
    } catch (err) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = err.message;
    }

    return result;
  }

  /**
   * Düşük seviyeli DNS sorgusu yapar
   * @param {string} host - DNS sunucusu adresi
   * @param {Object} options - DNS sorgu seçenekleri
   * @returns {Promise<Object>} - DNS sorgu sonuçları
   */
  async performDnsQuery(host, options = {}) {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const socket = dgram.createSocket('udp4');
      let timeoutId;

      const domain = options.domain || 'google.com';
      const recordType = options.recordType || 'A';
      const port = options.port || 53;
      const timeout = options.timeout || 5000;

      // DNS paketi oluştur
      const query = dnsPacket.encode({
        type: 'query',
        id: Math.floor(Math.random() * 65535),
        flags: dnsPacket.RECURSION_DESIRED,
        questions: [{
          type: recordType,
          name: domain
        }]
      });

      // Zaman aşımı işleyicisi
      timeoutId = setTimeout(() => {
        socket.close();
        reject(new Error('DNS query timed out'));
      }, timeout);

      // Yanıt işleyicisi
      socket.on('message', (message) => {
        clearTimeout(timeoutId);
        const responseTime = Date.now() - startTime;

        try {
          const response = dnsPacket.decode(message);
          socket.close();
          resolve({
            status: 'up',
            responseTime,
            details: response
          });
        } catch (err) {
          socket.close();
          reject(new Error(`Failed to decode DNS response: ${err.message}`));
        }
      });

      // Hata işleyicisi
      socket.on('error', (err) => {
        clearTimeout(timeoutId);
        socket.close();
        reject(err);
      });

      // Sorguyu gönder
      socket.send(query, 0, query.length, port, host, (err) => {
        if (err) {
          clearTimeout(timeoutId);
          socket.close();
          reject(err);
        }
      });
    }).catch(err => {
      return {
        status: 'down',
        responseTime: Date.now() - startTime,
        error: err.message
      };
    });
  }

  /**
   * Bir cihazın DNS durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io, isManualCheck = false) {
    try {
      // DNS izleme yapılandırmasını al
      const config = device.monitors?.dns || {};

      // DNS sunucusunu belirle
      let dnsServer = await this.getDnsServer(config);

      // DNS sorgu seçeneklerini belirle
      // Eğer domain belirtilmemişse ve host bir IP adresi değilse, host'u kullan
      let defaultDomain = '';
      if (device.host && !device.host.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        defaultDomain = device.host;
      }

      // Domain belirtilmemişse ve varsayılan domain de yoksa (IP adresi durumu), hata fırlat
      if (!config.domain && !defaultDomain) {
        throw new Error('DNS izlemesi için bir domain adı belirtilmelidir. IP adresleri için lütfen izlenecek bir domain adı girin.');
      }

      const options = {
        domain: config.domain || defaultDomain,
        recordType: 'A', // Her zaman A kaydını kullan
        timeout: config.timeout ? parseInt(config.timeout) : 5000
      };

      // DNS sunucusu kaynağını belirle
      let dnsServerSource;
      if (config.server === 'system') {
        dnsServerSource = 'system DNS';
      } else if (config.server === 'custom') {
        dnsServerSource = 'custom DNS';
      } else if (config.server === 'default') {
        dnsServerSource = 'default DNS';
      } else {
        dnsServerSource = 'predefined DNS';
      }

      console.log(`DNS check for ${device.name}: Using DNS server ${dnsServer || 'system DNS'} (${dnsServerSource}) to query ${options.domain} with timeout ${options.timeout}ms`);

      // DNS sorgusunu yap
      const dnsResult = await this.performDnsQuery(dnsServer, options);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: dnsResult.status,
        responseTime: dnsResult.responseTime,
        error: dnsResult.error || '',
        details: {
          server: dnsServer,
          domain: options.domain,
          resolvedIp: dnsResult.details && dnsResult.details.answers ? dnsResult.details.answers.map(a => a.data).join(', ') : '',
          response: dnsResult.details
        }
      };

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      let intervalValue = device.monitors.dns.interval;
      console.log(`DNS check for ${device.name}: Using interval value: ${intervalValue} minutes`);
      const interval = parseInt(intervalValue) * 60 * 1000;
      console.log(`DNS check for ${device.name}: Converted interval: ${interval} ms (${interval / 1000} seconds)`);
      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`DNS check for ${device.name}: Parsed interval: ${parsedInterval} ms`);


      // Sonuçları interval bilgisiyle kaydet (manuel kontrol parametresini de geçir)
      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      await this.saveResults(device.id, result, io, parsedInterval, isManualCheck);

      return dnsResult;
    } catch (error) {
      console.error(`DNS monitoring error for ${device.host}:`, error);

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      let intervalValue = device.monitors.dns.interval;
      console.log(`DNS check error for ${device.name}: Using interval value: ${intervalValue} minutes`);
      const interval = parseInt(intervalValue) * 60 * 1000;
      console.log(`DNS check error for ${device.name}: Converted interval: ${interval} ms (${interval / 1000} seconds)`);
      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`DNS check error for ${device.name}: Parsed interval: ${parsedInterval} ms`);


      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan
      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      await this.saveError(device.id, error, io, parsedInterval, isManualCheck);

      return {
        status: 'error',
        responseTime: 0,
        error: error.message
      };
    }
  }
}

// Singleton instance oluştur
const dnsMonitor = new DnsMonitor();

module.exports = {
  checkDnsServer: (host, options) => dnsMonitor.checkDnsServer(host, options),
  performDnsQuery: (host, options) => dnsMonitor.performDnsQuery(host, options),
  getDnsServer: (serverSetting) => dnsMonitor.getDnsServer(serverSetting),
  monitorDevice: (device, io, isManualCheck = false) => dnsMonitor.monitorDevice(device, io, isManualCheck),
  getResults: (deviceId) => dnsMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => dnsMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => dnsMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => dnsMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => dnsMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => dnsMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
