/**
 * API izleme servisi
 */
const axios = require('axios');
const BaseMonitor = require('./base-monitor');

class ApiMonitor extends BaseMonitor {
  constructor() {
    super('api');
  }

  /**
   * API endpoint'ini kontrol eder
   * @param {string} url - Kontrol edilecek API URL'si
   * @param {Object} options - API kontrol seçenekleri
   * @returns {Promise<Object>} - API kontrol sonuçları
   */
  async checkApiEndpoint(url, options = {}) {
    const startTime = Date.now();
    const result = {
      status: 'unknown',
      responseTime: 0,
      error: null,
      details: {}
    };

    try {
      // HTTP isteği yapılandırması
      const config = {
        method: options.method || 'GET',
        url: url,
        timeout: options.timeout || 5000,
        validateStatus: null, // Tüm HTTP durum kodlarını kabul et
        maxRedirects: options.maxRedirects || 5
      };

      // İstek gövdesi ekle (POST, PUT, PATCH için)
      if (['POST', 'PUT', 'PATCH'].includes(config.method.toUpperCase()) && options.body) {
        config.data = options.body;
      }

      // Headers ekle
      if (options.headers) {
        config.headers = options.headers;
      } else {
        config.headers = {};
      }

      // API token/anahtar ekle
      if (options.apiKey) {
        // Token tipi ve konumuna göre ekle
        if (options.apiKeyLocation === 'header') {
          // Header'a ekle (örn: Authorization: Bearer TOKEN)
          const headerName = options.apiKeyHeaderName || 'Authorization';
          const headerValue = options.apiKeyPrefix ?
            `${options.apiKeyPrefix} ${options.apiKey}` :
            options.apiKey;

          config.headers[headerName] = headerValue;
        } else if (options.apiKeyLocation === 'query') {
          // URL query parametresi olarak ekle
          const paramName = options.apiKeyParamName || 'api_key';

          // URL'de zaten query parametresi var mı kontrol et
          if (url.includes('?')) {
            config.url = `${url}&${paramName}=${options.apiKey}`;
          } else {
            config.url = `${url}?${paramName}=${options.apiKey}`;
          }
        }
      }

      // Basic auth ekle
      if (options.auth) {
        config.auth = options.auth;
      }

      // İsteği gönder
      const response = await axios(config);

      // Yanıt süresini hesapla
      result.responseTime = Date.now() - startTime;

      // Beklenen durum kodu kontrolü
      const expectedStatus = options.expectedStatus || 200;
      if (response.status === expectedStatus) {
        result.status = 'up';
      } else {
        result.status = 'down';
        result.error = `Unexpected status code: ${response.status}, expected: ${expectedStatus}`;
      }

      // Yanıt detaylarını ekle (basitleştirilmiş)
      result.details = {
        statusCode: response.status,
        statusText: response.statusText,
        contentType: response.headers['content-type'],
        responseSize: JSON.stringify(response.data).length,
        responseData: typeof response.data === 'object' ?
          JSON.stringify(response.data).substring(0, 100) + (JSON.stringify(response.data).length > 100 ? '...' : '') :
          (typeof response.data === 'string' ?
            response.data.substring(0, 100) + (response.data.length > 100 ? '...' : '') :
            String(response.data)),
        // API token/anahtar kullanıldı mı bilgisi ekle
        authenticationUsed: options.apiKey ? true : false,
        authType: options.apiKey ?
          (options.apiKeyLocation === 'header' ? 'API Key (Header)' : 'API Key (Query)') :
          (options.auth ? 'Basic Auth' : 'None')
      };

      // Sadece önemli headers'ları ekle
      const importantHeaders = ['content-type', 'server', 'date'];
      result.details.headers = {};
      importantHeaders.forEach(header => {
        if (response.headers[header]) {
          result.details.headers[header] = response.headers[header];
        }
      });
    } catch (err) {
      result.responseTime = Date.now() - startTime;
      result.status = 'down';
      result.error = err.message;

      // Axios hata detaylarını ekle (basitleştirilmiş)
      if (err.response) {
        result.details = {
          statusCode: err.response.status,
          statusText: err.response.statusText,
          // API token/anahtar kullanıldı mı bilgisi ekle
          authenticationUsed: options.apiKey ? true : false,
          authType: options.apiKey ?
            (options.apiKeyLocation === 'header' ? 'API Key (Header)' : 'API Key (Query)') :
            (options.auth ? 'Basic Auth' : 'None')
        };
      } else if (err.request) {
        result.details = {
          request: 'Request was made but no response was received',
          // API token/anahtar kullanıldı mı bilgisi ekle
          authenticationUsed: options.apiKey ? true : false,
          authType: options.apiKey ?
            (options.apiKeyLocation === 'header' ? 'API Key (Header)' : 'API Key (Query)') :
            (options.auth ? 'Basic Auth' : 'None')
        };
      }
    }

    return result;
  }

  /**
   * Bir cihazın API durumunu kontrol eder ve sonuçları Redis'e kaydeder
   * @param {Object} device - Cihaz bilgileri
   * @param {Object} io - Socket.io nesnesi
   * @returns {Promise<Object>} - İzleme sonuçları
   */
  async monitorDevice(device, io) {
    try {
      // API izleme yapılandırmasını al
      const config = device.monitors?.api || {};

      // API URL'sini belirle
      const url = config.url || `http://${device.host}/api`;

      // API kontrol seçeneklerini belirle
      const options = {
        method: config.method || 'GET',
        timeout: config.timeout || 5000,
        expectedStatus: config.expectedStatus || 200,
        headers: config.headers || {},
        body: config.body || null,
        apiKey: config.apiKey || null,
        apiKeyLocation: config.apiKeyLocation || 'header',
        apiKeyHeaderName: config.apiKeyHeaderName || 'Authorization',
        apiKeyPrefix: config.apiKeyPrefix || 'Bearer',
        apiKeyParamName: config.apiKeyParamName || 'api_key',
        auth: config.auth || null,
        maxRedirects: config.maxRedirects || 5
      };

      console.log(`API check for ${device.name}: Using URL ${url} with method ${options.method}`);

      // API endpoint'ini kontrol et
      const apiResult = await this.checkApiEndpoint(url, options);

      // Sonuçları BaseMonitor sınıfının saveResults metodunu kullanarak kaydet
      const result = {
        status: apiResult.status,
        responseTime: apiResult.responseTime,
        error: apiResult.error || '',
        details: apiResult.details
      };

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      const interval = parseInt(device.monitors.api.interval) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`API check for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Sonuçları interval bilgisiyle kaydet
      await this.saveResults(device.id, result, io, parsedInterval);

      return apiResult;
    } catch (error) {
      console.error(`API monitoring error for ${device.host}:`, error);

      // İzleme aralığını belirle
      // Frontend'den gelen değer dakika cinsinden, milisaniyeye çevir (dakika * 60 * 1000)
      const interval = parseInt(device.monitors.api.interval) * 60 * 1000;

      // ICMP ile aynı şekilde parseInt kullanarak interval değerini sayıya çevirelim
      const parsedInterval = parseInt(interval);
      console.log(`API check error for ${device.name}: Interval: ${interval}ms, Parsed: ${parsedInterval}ms`);

      // Hata durumunda BaseMonitor sınıfının saveError metodunu kullan (daha kısa interval ile)
      await this.saveError(device.id, error, io, parsedInterval, true);

      return {
        status: 'error',
        responseTime: 0,
        error: error.message,
        details: {}
      };
    }
  }
}

// Singleton instance oluştur
const apiMonitor = new ApiMonitor();

module.exports = {
  checkApiEndpoint: (url, options) => apiMonitor.checkApiEndpoint(url, options),
  monitorDevice: (device, io) => apiMonitor.monitorDevice(device, io),
  getResults: (deviceId) => apiMonitor.getResults(deviceId),
  getHistory: (deviceId, limit) => apiMonitor.getHistory(deviceId, limit),
  deleteData: (deviceId) => apiMonitor.deleteData(deviceId),
  getLastCheckTime: (deviceId) => apiMonitor.getLastCheckTime(deviceId),
  getNextCheckTime: (deviceId) => apiMonitor.getNextCheckTime(deviceId),
  updateNextCheckTime: (deviceId, interval, isInitialUpdate) => apiMonitor.updateNextCheckTime(deviceId, interval, isInitialUpdate)
};
