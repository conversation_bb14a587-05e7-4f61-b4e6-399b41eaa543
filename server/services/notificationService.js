/**
 * Birleştirilmiş bildirim sistemi servisi
 * Bu servis, hem cihaz uyarılarını hem de sistem bildirimlerini yönetir
 */
const redisClient = require('./redis');
const socketService = require('./socketService');
const emailService = require('./emailService');
const { v4: uuidv4 } = require('uuid');
const {
  NOTIFICATION_TYPES,
  NOTIFICATION_SOURCES,
  NOTIFICATION_CATEGORIES,
  NOTIFICATION_SEVERITY,
  NOTIFICATION_STATUS
} = require('../constants/notifications');

/**
 * JSON parse işlemini güvenli bir şekilde yapar
 * @param {string} str - JSON string
 * @param {Object|Array} defaultValue - Hata durumunda dönecek varsayılan değer
 * @returns {Object|Array} - Parse edilmiş nesne veya varsayılan değer
 */
const safeJsonParse = (str, defaultValue) => {
  if (!str || typeof str !== 'string') return defaultValue;
  try {
    return JSON.parse(str);
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return defaultValue;
  }
};

/**
 * Yeni bir bildirim oluşturur
 * @param {Object} notificationData - Bildirim verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createNotification = async (notificationData) => {
  try {
    const notificationId = uuidv4();
    const timestamp = new Date().toISOString();
    const timestampMs = new Date(timestamp).getTime();

    const notification = {
      id: notificationId,
      type: notificationData.type || NOTIFICATION_TYPES.SYSTEM,
      category: notificationData.category || NOTIFICATION_CATEGORIES.SYSTEM,
      severity: notificationData.severity || NOTIFICATION_SEVERITY.INFO,
      title: notificationData.title,
      message: notificationData.message,
      source: notificationData.source || { type: 'system', name: 'System' },
      metadata: notificationData.metadata || {},
      timestamp,
      status: NOTIFICATION_STATUS.NEW,
      actions: notificationData.actions || []
    };

    // Redis'e bildirimi kaydet
    await redisClient.hmset(`notification:${notificationId}`, {
      ...notification,
      source: JSON.stringify(notification.source),
      metadata: JSON.stringify(notification.metadata),
      actions: JSON.stringify(notification.actions)
    });

    // Redis pipeline oluştur - Tüm indeksleri oluştur
    const pipeline = redisClient.multi();

    // 1. Ana bildirim listesi
    pipeline.zadd('notifications', timestampMs, notificationId);

    // 2. Tip indeksleri (device/system)
    pipeline.zadd(`notifications:type:${notification.type}`, timestampMs, notificationId);

    // 3. Kategori indeksleri (connection, performance, vb.)
    pipeline.zadd(`notifications:category:${notification.category}`, timestampMs, notificationId);

    // 4. Önem derecesi indeksleri (critical, warning, info)
    pipeline.zadd(`notifications:severity:${notification.severity}`, timestampMs, notificationId);

    // 5. Durum indeksleri (new, read, acknowledged, resolved)
    pipeline.zadd(`notifications:status:${notification.status}`, timestampMs, notificationId);

    // 6. Source indeksleri (cihaz/sistem bazlı)
    if (notification.source && notification.source.type) {
      if (notification.source.id) {
        // Spesifik kaynak (örn: device:123)
        pipeline.zadd(`notifications:source:${notification.source.type}:${notification.source.id}`, timestampMs, notificationId);
      }
    }

    // 7. Kombinasyon indeksleri (performans için)
    pipeline.zadd(`notifications:type:${notification.type}:status:${notification.status}`, timestampMs, notificationId);
    pipeline.zadd(`notifications:type:${notification.type}:severity:${notification.severity}`, timestampMs, notificationId);
    pipeline.zadd(`notifications:category:${notification.category}:status:${notification.status}`, timestampMs, notificationId);
    pipeline.zadd(`notifications:category:${notification.category}:severity:${notification.severity}`, timestampMs, notificationId);

    // Pipeline'ı çalıştır
    await pipeline.exec();

    // Socket.io ile bildirimi gönder
    if (socketService.getIO) {
      try {
        // ✅ Frontend ile uyumlu format: {notification: {...}}
        socketService.emitToAll('notification:new', {
          notification: notification,
          timestamp: Date.now()
        });
        console.log('📡 Socket event gönderildi: notification:new');

        // 🔢 Real-time: Bildirim sayılarını güncelle ve gönder
        const updatedCounts = await getCounts();
        socketService.emitToAll('notification:counts-updated', {
          counts: updatedCounts,
          timestamp: Date.now()
        });
        console.log('📊 Socket event gönderildi: notification:counts-updated');
      } catch (error) {
        console.error('Socket.io ile bildirim gönderilirken hata:', error);
      }
    }

    // E-posta bildirimi gönder
    try {
      await sendEmailNotification(notification);
    } catch (error) {
      console.error('E-posta bildirimi gönderilirken hata:', error);
      // E-posta hatası bildirim oluşturmayı engellemez
    }

    return notification;
  } catch (error) {
    console.error('Bildirim oluşturulurken hata:', error);
    throw error;
  }
};

/**
 * Bir bildirimi getirir
 * @param {string} notificationId - Bildirim ID
 * @returns {Promise<Object>} - Bildirim
 */
const getNotification = async (notificationId) => {
  try {
    const notification = await redisClient.hgetall(`notification:${notificationId}`);

    if (!notification || Object.keys(notification).length === 0) {
      return null;
    }

    // JSON alanlarını parse et
    notification.source = safeJsonParse(notification.source, {});
    notification.metadata = safeJsonParse(notification.metadata, {});
    notification.actions = safeJsonParse(notification.actions, []);

    return notification;
  } catch (error) {
    console.error(`Bildirim getirilirken hata (ID: ${notificationId}):`, error);
    throw error;
  }
};

/**
 * Bildirimleri filtrelere göre getirir - Optimize edilmiş versiyon
 * @param {Object} filters - Filtreler
 * @returns {Promise<Array>} - Bildirimler
 */
const getNotifications = async (filters = {}) => {
  try {
    const {
      category,
      severity,
      status,
      search,
      source,
      limit = 50,
      offset = 0
    } = filters;

    console.log('🔍 getNotifications called with filters:', filters);

    // Arama sorgusu varsa, özel arama fonksiyonunu kullan
    if (search && search.trim() !== '') {
      console.log('📝 Using search-optimized path');
      return await getNotificationsWithSearch(filters);
    }

    // Source filtresi varsa, özel source fonksiyonunu kullan
    if (source && (source.type || source.id)) {
      console.log('🎯 Using source-optimized path');
      return await getNotificationsWithSource(filters);
    }

    // Redis seviyesinde optimize edilmiş filtreleme
    console.log('⚡ Using Redis-optimized path');

    // En uygun Redis anahtarını seç
    let redisKey = await selectOptimalRedisKey(filters);
    console.log('🔑 Selected Redis key:', redisKey);

    // Eğer çok spesifik filtreler varsa, daha büyük veri seti al
    const needsPostFiltering = (category && category !== 'all' && !['device', 'system'].includes(category)) ||
                              (severity && severity !== 'all') ||
                              (status && status !== 'all');

    let fetchLimit = limit;
    let fetchOffset = offset;

    if (needsPostFiltering) {
      // Post-filtering gerekiyorsa daha fazla veri al
      fetchLimit = Math.min(limit * 3, 200); // Maksimum 200 kayıt
      fetchOffset = Math.max(0, offset - limit); // Biraz geriye git
      console.log('🔄 Post-filtering needed, fetching more data:', { fetchLimit, fetchOffset });
    }

    // Redis'ten veri çek
    const notificationIds = await redisClient.zrevrange(redisKey, fetchOffset, fetchOffset + fetchLimit - 1);

    if (!notificationIds || notificationIds.length === 0) {
      console.log('📭 No notifications found in Redis');
      return [];
    }

    console.log(`📦 Fetched ${notificationIds.length} notification IDs from Redis`);

    // Bildirimleri paralel olarak getir
    const notificationPromises = notificationIds.map(id => getNotification(id));
    const allNotifications = await Promise.all(notificationPromises);
    const validNotifications = allNotifications.filter(n => n !== null);

    console.log(`✅ Got ${validNotifications.length} valid notifications`);

    // Filtreleme uygula
    let filteredNotifications = await applyFilters(validNotifications, filters);
    console.log(`🔍 After filtering: ${filteredNotifications.length} notifications`);

    // Sayfalama uygula (eğer post-filtering yaptıysak)
    if (needsPostFiltering) {
      const startIndex = Math.max(0, offset - fetchOffset);
      filteredNotifications = filteredNotifications.slice(startIndex, startIndex + limit);
      console.log(`📄 After pagination: ${filteredNotifications.length} notifications`);
    }

    return filteredNotifications;
  } catch (error) {
    console.error('Bildirimler getirilirken hata:', error);
    throw error;
  }
};

/**
 * En uygun Redis anahtarını seçer
 * @param {Object} filters - Filtreler
 * @returns {Promise<string>} - Redis anahtarı
 */
const selectOptimalRedisKey = async (filters) => {
  const { category, severity, status } = filters;

  // Öncelik sırası: status > severity > category > all
  // Çünkü genellikle status filtreleri daha az sonuç döndürür

  if (status && status !== 'all') {
    const statusKey = `notifications:status:${status}`;
    const statusCount = await redisClient.zcard(statusKey);
    if (statusCount > 0) {
      console.log(`📊 Using status key: ${statusKey} (${statusCount} items)`);
      return statusKey;
    }
  }

  if (severity && severity !== 'all') {
    const severityKey = `notifications:severity:${severity}`;
    const severityCount = await redisClient.zcard(severityKey);
    if (severityCount > 0) {
      console.log(`📊 Using severity key: ${severityKey} (${severityCount} items)`);
      return severityKey;
    }
  }

  if (category && category !== 'all') {
    if (category === 'device' || category === 'system') {
      const typeKey = `notifications:type:${category}`;
      const typeCount = await redisClient.zcard(typeKey);
      if (typeCount > 0) {
        console.log(`📊 Using type key: ${typeKey} (${typeCount} items)`);
        return typeKey;
      }
    } else {
      // Harmanlanmış kategori için özel anahtar kontrol et
      const categoryKey = `notifications:category:${category.split('_')[1] || category}`;
      const categoryCount = await redisClient.zcard(categoryKey);
      if (categoryCount > 0) {
        console.log(`📊 Using category key: ${categoryKey} (${categoryCount} items)`);
        return categoryKey;
      }
    }
  }

  // Varsayılan olarak ana anahtar
  console.log('📊 Using default key: notifications');
  return 'notifications';
};

/**
 * Bildirimlere filtre uygular
 * @param {Array} notifications - Bildirimler
 * @param {Object} filters - Filtreler
 * @returns {Promise<Array>} - Filtrelenmiş bildirimler
 */
const applyFilters = async (notifications, filters) => {
  const { category, severity, status } = filters;

  return notifications.filter(notification => {
    if (!notification) return false;

    // Kategori filtresi
    let categoryMatch = true;
    if (category && category !== 'all') {
      if (category === 'device' || category === 'system') {
        // Ana kategori filtresi (source type'a göre)
        categoryMatch = notification.source?.type === category;
      } else {
        // Alt kategori filtresi (notification category'ye göre)
        categoryMatch = notification.category === category;
      }
    }

    // Durum filtresi
    const statusMatch = !status || status === 'all' || notification.status === status;

    // Önem derecesi filtresi
    const severityMatch = !severity || severity === 'all' || notification.severity === severity;

    return categoryMatch && statusMatch && severityMatch;
  });
};

/**
 * Source filtresi ile bildirimleri getirir
 * @param {Object} filters - Filtreler
 * @returns {Promise<Array>} - Bildirimler
 */
const getNotificationsWithSource = async (filters) => {
  const { source, limit = 50, offset = 0 } = filters;

  console.log('🎯 getNotificationsWithSource called with source:', source);

  let sourceKey;
  if (source.id) {
    sourceKey = `notifications:source:${source.type}:${source.id}`;
  } else {
    sourceKey = `notifications:type:${source.type}`;
  }

  console.log('🔑 Using source key:', sourceKey);

  // Source anahtarından bildirimleri al
  const notificationIds = await redisClient.zrevrange(sourceKey, offset, offset + limit - 1);

  if (!notificationIds || notificationIds.length === 0) {
    return [];
  }

  // Bildirimleri getir
  const notificationPromises = notificationIds.map(id => getNotification(id));
  const allNotifications = await Promise.all(notificationPromises);
  const validNotifications = allNotifications.filter(n => n !== null);

  // Diğer filtreleri uygula
  return await applyFilters(validNotifications, filters);
};

/**
 * Arama sorgusu ile bildirimleri getirir (daha yavaş ama kapsamlı)
 * @param {Object} filters - Filtreler
 * @returns {Promise<Array>} - Bildirimler
 */
const getNotificationsWithSearch = async (filters) => {
  const {
    type,
    category,
    severity,
    status,
    source,
    search,
    limit,
    offset = 0
  } = filters;

  console.log('📝 getNotificationsWithSearch called with search:', search);

  // Arama için optimize edilmiş strateji
  const isCountRequest = limit === undefined || limit === null || !('limit' in filters);

  // Önce en uygun Redis anahtarını seç (arama dışındaki filtreler için)
  const searchFilters = { ...filters };
  delete searchFilters.search; // Arama filtresini geçici olarak kaldır

  let baseKey = 'notifications';
  if (Object.keys(searchFilters).some(key => searchFilters[key] && searchFilters[key] !== 'all')) {
    baseKey = await selectOptimalRedisKey(searchFilters);
  }

  console.log('🔑 Search using base key:', baseKey);

  // Arama için daha akıllı limit kullan
  let searchLimit = isCountRequest ? -1 : Math.min(1000, limit * 10); // Maksimum 1000 kayıt
  let notificationIds;

  if (searchLimit === -1) {
    // Count isteği için tüm bildirimleri al
    notificationIds = await redisClient.zrevrange(baseKey, 0, -1);
  } else {
    // Normal istek için sınırlı sayıda al
    notificationIds = await redisClient.zrevrange(baseKey, 0, searchLimit - 1);
  }

  console.log(`📦 Fetched ${notificationIds?.length || 0} notification IDs for search`);

  if (!notificationIds || notificationIds.length === 0) {
    return [];
  }

  // Bildirimleri paralel olarak getir
  const notificationPromises = notificationIds.map(id => getNotification(id));
  const allNotifications = await Promise.all(notificationPromises);
  const validNotifications = allNotifications.filter(n => n !== null);

  console.log(`✅ Got ${validNotifications.length} valid notifications for search`);

  // Önce diğer filtreleri uygula (arama dışında)
  let preFilteredNotifications = await applyFilters(validNotifications, searchFilters);
  console.log(`🔍 After pre-filtering: ${preFilteredNotifications.length} notifications`);

  // Source filtresi ayrıca uygula (applyFilters'da yok)
  if (source) {
    preFilteredNotifications = preFilteredNotifications.filter(notification => {
      if (source.id) {
        return notification.source &&
               notification.source.type === source.type &&
               notification.source.id === source.id;
      } else {
        return notification.source &&
               notification.source.type === source.type;
      }
    });
    console.log(`🎯 After source filtering: ${preFilteredNotifications.length} notifications`);
  }

  // Arama sorgusu uygula
  let searchedNotifications = preFilteredNotifications;
  if (search && search.trim() !== '') {
    const searchLower = search.toLowerCase().trim();
    console.log(`🔍 Applying search filter: "${searchLower}"`);

    searchedNotifications = preFilteredNotifications.filter(notification => {
      const titleMatch = notification.title && notification.title.toLowerCase().includes(searchLower);
      const messageMatch = notification.message && notification.message.toLowerCase().includes(searchLower);
      const sourceMatch = notification.source && notification.source.name &&
                         notification.source.name.toLowerCase().includes(searchLower);

      return titleMatch || messageMatch || sourceMatch;
    });

    console.log(`📝 After search filtering: ${searchedNotifications.length} notifications`);
  }

  // Sonuçları timestamp'e göre sırala (en yeni önce)
  searchedNotifications.sort((a, b) => {
    const timestampA = new Date(a.timestamp).getTime();
    const timestampB = new Date(b.timestamp).getTime();
    return timestampB - timestampA; // Descending order (en yeni önce)
  });

  // Sayfalama uygula (count isteği değilse)
  if (isCountRequest) {
    console.log(`📊 Returning ${searchedNotifications.length} notifications for count`);
    return searchedNotifications;
  } else {
    const paginatedResults = searchedNotifications.slice(offset, offset + limit);
    console.log(`📄 Returning ${paginatedResults.length} notifications (page ${Math.floor(offset/limit) + 1})`);
    return paginatedResults;
  }
};

/**
 * Bildirimlerin sayısını filtrelere göre getirir
 * @param {Object} filters - Filtreler
 * @returns {Promise<number>} - Bildirim sayısı
 */
const getNotificationCount = async (filters = {}) => {
  try {
    const { category, severity, status, search } = filters;

    // Arama sorgusu varsa, getNotificationsWithSearch kullan (limit olmadan)
    if (search && search.trim() !== '') {
      // Arama için tüm bildirimleri al (limit ve offset olmadan)
      const searchFilters = {
        ...filters
        // limit ve offset'i tamamen kaldır (undefined bile geçmeyelim)
      };
      delete searchFilters.limit;
      delete searchFilters.offset;

      const notifications = await getNotificationsWithSearch(searchFilters);
      return notifications.length;
    }

    // Harmanlanmış kategori için Redis anahtarı seçimi (getNotifications ile aynı mantık)
    let countKey;
    if (category && category !== 'all') {
      if (category === 'device' || category === 'system') {
        // Ana kategori için tüm bildirimleri al ve filtrele
        countKey = 'notifications';
      } else {
        // Alt kategori için tüm bildirimleri al ve filtrele
        countKey = 'notifications';
      }
    } else {
      countKey = 'notifications';
    }

    // Redis'ten sayıyı al
    const exists = await redisClient.exists(countKey);
    if (exists) {
      let totalCount = await redisClient.zcard(countKey);

      // Eğer ek filtreler varsa (severity, status) veya harmanlanmış kategori filtresi varsa, bildirimleri getir ve filtrele
      if (severity || status || (category && category !== 'all' && category !== 'device' && category !== 'system')) {
        const notifications = await getNotifications({
          ...filters,
          limit: 1000,
          offset: 0
        });
        return notifications.length;
      }

      return totalCount;
    }

    // Anahtar yoksa 0 döndür
    return 0;
  } catch (error) {
    console.error('Bildirim sayısı getirilirken hata:', error);
    throw error;
  }
};

/**
 * Bir bildirimin durumunu günceller
 * @param {string} notificationId - Bildirim ID
 * @param {string} status - Yeni durum
 * @param {Object} updateData - Ek güncelleme verileri
 * @returns {Promise<Object>} - Güncellenen bildirim
 */
const updateNotificationStatus = async (notificationId, status, updateData = {}) => {
  try {
    const notification = await getNotification(notificationId);

    if (!notification) {
      throw new Error(`Bildirim bulunamadı: ${notificationId}`);
    }

    const oldStatus = notification.status;
    const updates = {
      status,
      ...updateData
    };

    // Redis'te bildirimi güncelle
    await redisClient.hmset(`notification:${notificationId}`, updates);

    // Güncellenmiş bildirimi getir
    const updatedNotification = await getNotification(notificationId);
    const timestampMs = new Date(updatedNotification.timestamp).getTime();

    // Redis pipeline oluştur - Durum değişikliği için indeksleri güncelle
    const pipeline = redisClient.multi();

    // Eski durum indekslerinden kaldır
    pipeline.zrem(`notifications:status:${oldStatus}`, notificationId);
    pipeline.zrem(`notifications:type:${notification.type}:status:${oldStatus}`, notificationId);
    pipeline.zrem(`notifications:category:${notification.category}:status:${oldStatus}`, notificationId);

    // Yeni durum indekslerine ekle
    pipeline.zadd(`notifications:status:${status}`, timestampMs, notificationId);
    pipeline.zadd(`notifications:type:${notification.type}:status:${status}`, timestampMs, notificationId);
    pipeline.zadd(`notifications:category:${notification.category}:status:${status}`, timestampMs, notificationId);

    // Pipeline'ı çalıştır
    await pipeline.exec();

    // Socket.io ile güncellemeyi gönder
    if (socketService.getIO) {
      try {
        // ✅ Frontend ile uyumlu format: {notification: {...}}
        socketService.emitToAll('notification:update', {
          notification: updatedNotification,
          timestamp: Date.now()
        });
        console.log('📡 Socket event gönderildi: notification:update');

        // 🔢 Real-time: Bildirim sayılarını güncelle ve gönder (durum değişikliği)
        const updatedCounts = await getCounts();
        socketService.emitToAll('notification:counts-updated', {
          counts: updatedCounts,
          timestamp: Date.now()
        });
        console.log('📊 Socket event gönderildi: notification:counts-updated (status change)');
      } catch (error) {
        console.error('Socket.io ile bildirim güncellemesi gönderilirken hata:', error);
      }
    }

    return updatedNotification;
  } catch (error) {
    console.error(`Bildirim durumu güncellenirken hata (ID: ${notificationId}):`, error);
    throw error;
  }
};

/**
 * Bir bildirimi okundu olarak işaretler
 * @param {string} notificationId - Bildirim ID
 * @returns {Promise<Object>} - Güncellenen bildirim
 */
const markNotificationAsRead = async (notificationId) => {
  return updateNotificationStatus(notificationId, NOTIFICATION_STATUS.READ, {
    readAt: new Date().toISOString()
  });
};

/**
 * Tüm bildirimleri okundu olarak işaretler
 * @returns {Promise<Object>} - İşlem sonucu
 */
const markAllNotificationsAsRead = async () => {
  try {
    // Okunmamış bildirimleri al
    const unreadNotifications = await getNotifications({
      status: NOTIFICATION_STATUS.NEW,
      limit: 1000 // Yüksek bir limit kullan
    });

    if (!unreadNotifications || unreadNotifications.length === 0) {
      return { success: true, count: 0 };
    }

    // Tüm bildirimleri paralel olarak güncelle
    const updatePromises = unreadNotifications.map(notification =>
      updateNotificationStatus(notification.id, NOTIFICATION_STATUS.READ, {
        readAt: new Date().toISOString()
      })
    );

    await Promise.all(updatePromises);

    // Socket.io ile toplu güncelleme gönder
    if (socketService.getIO) {
      try {
        socketService.emitToAll('notifications:mark-all-read', {
          count: unreadNotifications.length,
          timestamp: Date.now()
        });
        console.log('📡 Socket event gönderildi: notifications:mark-all-read');

        // 🔢 Real-time: Bildirim sayılarını güncelle ve gönder (toplu okundu)
        const updatedCounts = await getCounts();
        socketService.emitToAll('notification:counts-updated', {
          counts: updatedCounts,
          timestamp: Date.now()
        });
        console.log('📊 Socket event gönderildi: notification:counts-updated (mark all read)');
      } catch (error) {
        console.error('Socket.io ile toplu okundu işaretleme gönderilirken hata:', error);
      }
    }

    return { success: true, count: unreadNotifications.length };
  } catch (error) {
    console.error('Tüm bildirimler okundu olarak işaretlenirken hata:', error);
    throw error;
  }
};

/**
 * Bir bildirimi onaylanmış olarak işaretler
 * @param {string} notificationId - Bildirim ID
 * @param {string} userId - Kullanıcı ID
 * @param {string} username - Kullanıcı adı
 * @returns {Promise<Object>} - Güncellenen bildirim
 */
const acknowledgeNotification = async (notificationId, userId, username) => {
  return updateNotificationStatus(notificationId, NOTIFICATION_STATUS.ACKNOWLEDGED, {
    acknowledgedBy: userId,
    acknowledgedByUsername: username,
    acknowledgedAt: new Date().toISOString()
  });
};

/**
 * Bir bildirimi çözülmüş olarak işaretler
 * @param {string} notificationId - Bildirim ID
 * @param {string} userId - Kullanıcı ID
 * @param {string} username - Kullanıcı adı
 * @param {string} resolution - Çözüm açıklaması
 * @returns {Promise<Object>} - Güncellenen bildirim
 */
const resolveNotification = async (notificationId, userId, username, resolution) => {
  return updateNotificationStatus(notificationId, NOTIFICATION_STATUS.RESOLVED, {
    resolvedBy: userId,
    resolvedByUsername: username,
    resolvedAt: new Date().toISOString(),
    resolution
  });
};

/**
 * Cihaz durumuna göre bildirim oluşturur
 * @param {string} deviceId - Cihaz ID
 * @param {string} deviceName - Cihaz adı
 * @param {string} monitorType - İzleme türü
 * @param {Object} status - Durum bilgileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createDeviceStatusNotification = async (deviceId, deviceName, monitorType, status) => {
  // Bildirim türünü ve mesajını belirle
  let category, title, message, severity;
  const host = status.host || deviceId;
  const responseTime = status.responseTime || 0;
  const error = status.error || 'Bilinmeyen hata';
  const statusCode = status.statusCode || '';
  const timestamp = new Date().toLocaleString('tr-TR');

  // Standart bildirim formatı:
  // [Cihaz Adı] - [Sorun Türü]
  // [Detaylı açıklama: Neden, ne zaman, nasıl]

  switch (monitorType) {
    case 'icmp':
      if (status.status === 'down') {
        category = NOTIFICATION_CATEGORIES.DEVICE_CONNECTION;
        title = `${deviceName} - Cihaz Erişilemez`;
        message = `${deviceName} (${host}) ICMP ping yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 100) {
        category = NOTIFICATION_CATEGORIES.DEVICE_PERFORMANCE;
        title = `${deviceName} - Yüksek Gecikme`;
        message = `${deviceName} (${host}) ICMP ping yanıt süresi ${responseTime}ms. Normal değer: <100ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'http':
      if (status.status === 'down') {
        category = NOTIFICATION_CATEGORIES.DEVICE_SERVICE;
        title = `${deviceName} - HTTP Servis Erişilemez`;
        message = `${deviceName} (${host}) HTTP servis yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 1000) {
        category = NOTIFICATION_CATEGORIES.DEVICE_PERFORMANCE;
        title = `${deviceName} - HTTP Yüksek Gecikme`;
        message = `${deviceName} (${host}) HTTP yanıt süresi ${responseTime}ms. Normal değer: <1000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (statusCode >= 400) {
        category = NOTIFICATION_CATEGORIES.DEVICE_SERVICE;
        title = `${deviceName} - HTTP Hata Kodu`;
        message = `${deviceName} (${host}) HTTP ${statusCode} hata kodu döndürdü.`;
        severity = statusCode >= 500 ? NOTIFICATION_SEVERITY.CRITICAL : NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'tcp':
      if (status.status === 'down') {
        category = NOTIFICATION_CATEGORIES.DEVICE_SERVICE;
        title = `${deviceName} - TCP Port Erişilemez`;
        message = `${deviceName} (${host}) TCP port ${status.port || 'bilinmeyen'} yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 500) {
        category = NOTIFICATION_CATEGORIES.DEVICE_PERFORMANCE;
        title = `${deviceName} - TCP Yüksek Gecikme`;
        message = `${deviceName} (${host}) TCP port ${status.port || 'bilinmeyen'} yanıt süresi ${responseTime}ms. Normal değer: <500ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'dns':
      if (status.status === 'down') {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - DNS Servis Erişilemez`;
        message = `${deviceName} (${host}) DNS sorgusu başarısız. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 200) {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - DNS Yüksek Gecikme`;
        message = `${deviceName} (${host}) DNS sorgu süresi ${responseTime}ms. Normal değer: <200ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'ssl':
      if (status.status === 'down') {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - SSL Sertifika Hatası`;
        message = `${deviceName} (${host}) SSL sertifikası geçersiz. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (status.daysRemaining && status.daysRemaining < 30) {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - SSL Sertifikası Yakında Sona Erecek`;
        message = `${deviceName} (${host}) SSL sertifikası ${status.daysRemaining} gün içinde sona erecek.`;
        severity = status.daysRemaining < 7 ? NOTIFICATION_SEVERITY.CRITICAL : NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'database':
      if (status.status === 'down') {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - Veritabanı Erişilemez`;
        message = `${deviceName} (${host}) veritabanı bağlantısı başarısız. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 1000) {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - Veritabanı Yüksek Gecikme`;
        message = `${deviceName} (${host}) veritabanı sorgu süresi ${responseTime}ms. Normal değer: <1000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'api':
      if (status.status === 'down') {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - API Erişilemez`;
        message = `${deviceName} (${host}) API yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 2000) {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - API Yüksek Gecikme`;
        message = `${deviceName} (${host}) API yanıt süresi ${responseTime}ms. Normal değer: <2000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (statusCode >= 400) {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - API Hata Kodu`;
        message = `${deviceName} (${host}) API ${statusCode} hata kodu döndürdü.`;
        severity = statusCode >= 500 ? NOTIFICATION_SEVERITY.CRITICAL : NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'snmp':
      if (status.status === 'down') {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - SNMP Erişilemez`;
        message = `${deviceName} (${host}) SNMP yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 500) {
        category = 'device'; // ✅ Basit kategori
        title = `${deviceName} - SNMP Yüksek Gecikme`;
        message = `${deviceName} (${host}) SNMP yanıt süresi ${responseTime}ms. Normal değer: <500ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null; // Bildirim oluşturma
      }
      break;

    case 'smtp':
      if (status.status === 'down') {
        category = 'device';
        title = `${deviceName} - SMTP Servis Erişilemez`;
        message = `${deviceName} (${host}) SMTP sunucu yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 1000) {
        category = 'device';
        title = `${deviceName} - SMTP Yüksek Gecikme`;
        message = `${deviceName} (${host}) SMTP yanıt süresi ${responseTime}ms. Normal değer: <1000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null;
      }
      break;

    case 'windows':
      if (status.status === 'down') {
        category = 'device';
        title = `${deviceName} - Windows Sistem Erişilemez`;
        message = `${deviceName} (${host}) Windows WinRM bağlantısı başarısız. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 2000) {
        category = 'device';
        title = `${deviceName} - Windows Sistem Yüksek Gecikme`;
        message = `${deviceName} (${host}) Windows WinRM yanıt süresi ${responseTime}ms. Normal değer: <2000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (status.cpuUsage && status.cpuUsage > 90) {
        category = 'device';
        title = `${deviceName} - Windows Yüksek CPU Kullanımı`;
        message = `${deviceName} (${host}) CPU kullanımı %${status.cpuUsage}. Kritik seviye: >90%.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (status.memoryUsage && status.memoryUsage > 90) {
        category = 'device';
        title = `${deviceName} - Windows Yüksek RAM Kullanımı`;
        message = `${deviceName} (${host}) RAM kullanımı %${status.memoryUsage}. Kritik seviye: >90%.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null;
      }
      break;

    case 'linux':
      if (status.status === 'down') {
        category = 'device';
        title = `${deviceName} - Linux Sistem Erişilemez`;
        message = `${deviceName} (${host}) SSH bağlantısı başarısız. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 2000) {
        category = 'device';
        title = `${deviceName} - Linux Sistem Yüksek Gecikme`;
        message = `${deviceName} (${host}) SSH yanıt süresi ${responseTime}ms. Normal değer: <2000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (status.cpuUsage && status.cpuUsage > 90) {
        category = 'device';
        title = `${deviceName} - Linux Yüksek CPU Kullanımı`;
        message = `${deviceName} (${host}) CPU kullanımı %${status.cpuUsage}. Kritik seviye: >90%.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (status.memoryUsage && status.memoryUsage > 90) {
        category = 'device';
        title = `${deviceName} - Linux Yüksek RAM Kullanımı`;
        message = `${deviceName} (${host}) RAM kullanımı %${status.memoryUsage}. Kritik seviye: >90%.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null;
      }
      break;

    case 'ipmi':
      if (status.status === 'down') {
        category = 'device';
        title = `${deviceName} - IPMI/BMC Erişilemez`;
        message = `${deviceName} (${host}) IPMI/BMC yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 3000) {
        category = 'device';
        title = `${deviceName} - IPMI Yüksek Gecikme`;
        message = `${deviceName} (${host}) IPMI yanıt süresi ${responseTime}ms. Normal değer: <3000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (status.temperature && status.temperature > 80) {
        category = 'device';
        title = `${deviceName} - Yüksek Sıcaklık`;
        message = `${deviceName} (${host}) sistem sıcaklığı ${status.temperature}°C. Kritik seviye: >80°C.`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (status.fanStatus && status.fanStatus.includes('error')) {
        category = 'device';
        title = `${deviceName} - Fan Hatası`;
        message = `${deviceName} (${host}) fan durumu: ${status.fanStatus}.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else {
        return null;
      }
      break;

    case 'docker':
      if (status.status === 'down') {
        category = 'device';
        title = `${deviceName} - Docker API Erişilemez`;
        message = `${deviceName} (${host}) Docker API yanıt vermiyor. Hata: ${error}`;
        severity = NOTIFICATION_SEVERITY.CRITICAL;
      } else if (responseTime > 1000) {
        category = 'device';
        title = `${deviceName} - Docker API Yüksek Gecikme`;
        message = `${deviceName} (${host}) Docker API yanıt süresi ${responseTime}ms. Normal değer: <1000ms.`;
        severity = NOTIFICATION_SEVERITY.WARNING;
      } else if (status.runningContainers !== undefined && status.totalContainers !== undefined) {
        const stoppedContainers = status.totalContainers - status.runningContainers;
        if (stoppedContainers > 0) {
          category = 'device';
          title = `${deviceName} - Durmuş Container'lar`;
          message = `${deviceName} (${host}) ${stoppedContainers} container durmuş durumda. Toplam: ${status.totalContainers}, Çalışan: ${status.runningContainers}.`;
          severity = NOTIFICATION_SEVERITY.WARNING;
        } else {
          return null;
        }
      } else {
        return null;
      }
      break;

    default:
      return null; // Bildirim oluşturma
  }

  // Bildirim oluştur
  return createNotification({
    type: NOTIFICATION_TYPES.DEVICE,
    category,
    severity,
    title,
    message,
    source: {
      id: deviceId,
      name: deviceName,
      type: 'device',
      monitorType
    },
    metadata: {
      monitorType,
      status: status.status,
      responseTime: status.responseTime,
      error: status.error,
      timestamp: timestamp
    },
    actions: [
      {
        name: 'view',
        label: 'Cihazı Görüntüle',
        link: `/devices/${deviceId}`
      },
      {
        name: 'acknowledge',
        label: 'Onayla'
      }
    ]
  });
};

/**
 * Sistem bildirimi oluşturur
 * @param {Object} notificationData - Bildirim verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createSystemNotification = async (notificationData) => {
  // Standart bildirim formatı:
  // [Sistem Bileşeni] - [Durum/Olay]
  // [Detaylı açıklama: Etkilenen bileşenler, değerler]

  const timestamp = new Date().toLocaleString('tr-TR');
  let title = notificationData.title;
  let message = notificationData.message;

  // Başlık formatını standartlaştır
  if (title && !title.includes(' - ') && notificationData.sourceName) {
    title = `${notificationData.sourceName} - ${title}`;
  }

  // Mesaj temiz bırakılıyor - zaman bilgisi ayrı alanda gösteriliyor

  return createNotification({
    type: NOTIFICATION_TYPES.SYSTEM,
    category: notificationData.category || 'system', // ✅ Basit kategori
    severity: notificationData.severity || NOTIFICATION_SEVERITY.INFO,
    title: title,
    message: message,
    source: {
      type: 'system',
      name: notificationData.sourceName || 'Sistem'
    },
    metadata: {
      ...notificationData.metadata || {},
      timestamp: timestamp
    },
    actions: notificationData.actions || [
      {
        name: 'view',
        label: 'Görüntüle',
        link: notificationData.link || '/settings?tab=system'
      }
    ]
  });
};

/**
 * Kullanıcı bildirimi oluşturur
 * @param {Object} notificationData - Bildirim verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createUserNotification = async (notificationData) => {
  // Standart bildirim formatı:
  // [İşlem Türü] - [Kullanıcı Adı]
  // [Detaylı açıklama: Kim, ne zaman, nereden]

  const timestamp = new Date().toLocaleString('tr-TR');
  let title = notificationData.title;
  let message = notificationData.message;
  const username = notificationData.username || 'Sistem';
  const action = notificationData.action || 'info';
  const ipAddress = notificationData.ipAddress || '';
  const userAgent = notificationData.userAgent || '';

  // Başlık formatını standartlaştır
  if (title && !title.includes(' - ') && username) {
    title = `${action.charAt(0).toUpperCase() + action.slice(1)} - ${username}`;
  }

  // Mesaja detaylı bilgi ekle (zaman bilgisi hariç - ayrı alanda gösteriliyor)
  if (message) {
    let detailedMessage = message;

    if (ipAddress) {
      detailedMessage += ` IP: ${ipAddress}.`;
    }

    if (userAgent) {
      detailedMessage += ` Tarayıcı: ${userAgent}.`;
    }

    message = detailedMessage;
  }

  return createNotification({
    type: NOTIFICATION_TYPES.SYSTEM,
    category: notificationData.category || 'system', // ✅ Basit kategori
    severity: notificationData.severity || NOTIFICATION_SEVERITY.INFO,
    title: title,
    message: message,
    source: {
      type: 'system',
      id: notificationData.userId || 'system',
      name: username
    },
    metadata: {
      ...notificationData.metadata || {},
      action: action,
      ipAddress: ipAddress,
      userAgent: userAgent,
      timestamp: timestamp
    },
    actions: notificationData.actions || [
      {
        name: 'view',
        label: 'Kullanıcıyı Görüntüle',
        link: notificationData.userId ? `/users/${notificationData.userId}` : '/settings?tab=users'
      }
    ]
  });
};

/**
 * ✅ Test bildirimi oluşturur - Basit sistem
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createTestNotification = async () => {
  const types = [NOTIFICATION_TYPES.DEVICE, NOTIFICATION_TYPES.SYSTEM];
  const categories = ['device', 'system']; // ✅ Basit kategoriler
  const severities = [
    NOTIFICATION_SEVERITY.CRITICAL,
    NOTIFICATION_SEVERITY.WARNING,
    NOTIFICATION_SEVERITY.INFO,
    NOTIFICATION_SEVERITY.SUCCESS
  ];

  const randomType = types[Math.floor(Math.random() * types.length)];
  const randomCategory = randomType === NOTIFICATION_TYPES.DEVICE ? 'device' : 'system'; // ✅ Type ile uyumlu kategori
  const randomSeverity = severities[Math.floor(Math.random() * severities.length)];

  if (randomType === NOTIFICATION_TYPES.DEVICE) {
    return createNotification({
      type: randomType,
      category: randomCategory, // ✅ 'device'
      severity: randomSeverity,
      title: `Test Cihaz Bildirimi - ${randomSeverity}`,
      message: `Bu bir test cihaz bildirimidir. Önem: ${randomSeverity}`,
      source: {
        id: 'test-device-id',
        name: 'Test Cihazı',
        type: 'device'
      },
      metadata: {
        monitorType: 'test',
        status: randomSeverity === NOTIFICATION_SEVERITY.CRITICAL ? 'down' : 'up',
        responseTime: Math.floor(Math.random() * 1000)
      },
      actions: [
        {
          name: 'view',
          label: 'Cihazı Görüntüle',
          link: '/devices/test-device-id'
        },
        {
          name: 'acknowledge',
          label: 'Onayla'
        }
      ]
    });
  } else {
    return createNotification({
      type: randomType,
      category: randomCategory, // ✅ 'system'
      severity: randomSeverity,
      title: `Test Sistem Bildirimi - ${randomSeverity}`,
      message: `Bu bir test sistem bildirimidir. Önem: ${randomSeverity}`,
      source: {
        type: 'system',
        name: 'Test Sistem'
      },
      metadata: {
        testValue: Math.floor(Math.random() * 100)
      },
      actions: [
        {
          name: 'view',
          label: 'Görüntüle',
          link: '/settings?tab=system'
        }
      ]
    });
  }
};

// Eski sistemlerle uyumluluk için yardımcı fonksiyonlar
// Bu fonksiyonlar, geçiş sürecinde eski sistemlerin yeni sisteme yönlendirilmesini sağlar

/**
 * ✅ Eski alert sisteminden yeni basit bildirim sistemine dönüştürme
 * @param {Object} alertData - Eski alert verileri
 * @returns {Promise<Object>} - Yeni bildirim
 */
const createAlertCompatibility = async (alertData) => {
  let category, severity;

  // ✅ Eski alert türünü basit kategoriye dönüştür
  switch (alertData.type) {
    case 'Cihaz Erişilemez':
    case 'Yüksek Gecikme':
    case 'Paket Kaybı':
    case 'CPU Kullanımı':
    case 'Bellek Kullanımı':
    case 'Disk Kullanımı':
    case 'Servis Erişilemez':
    case 'Eşik Aşıldı':
      // Tüm cihaz sorunları 'device' kategorisinde
      category = 'device';
      break;
    case 'Sistem Hatası':
    default:
      // Sistem sorunları 'system' kategorisinde
      category = 'system';
      break;
  }

  // Eski alert seviyesini yeni seviyeye dönüştür
  switch (alertData.severity) {
    case 'critical':
      severity = NOTIFICATION_SEVERITY.CRITICAL;
      break;
    case 'warning':
      severity = NOTIFICATION_SEVERITY.WARNING;
      break;
    case 'info':
      severity = NOTIFICATION_SEVERITY.INFO;
      break;
    default:
      severity = NOTIFICATION_SEVERITY.INFO;
  }

  return createNotification({
    type: alertData.deviceId ? NOTIFICATION_TYPES.DEVICE : NOTIFICATION_TYPES.SYSTEM,
    category,
    severity,
    title: alertData.deviceName ? `${alertData.deviceName} - ${alertData.type}` : alertData.type,
    message: alertData.message,
    source: alertData.deviceId ? {
      id: alertData.deviceId,
      name: alertData.deviceName,
      type: 'device'
    } : {
      type: 'system',
      name: 'System'
    },
    metadata: alertData.data || {},
    actions: [
      {
        name: 'view',
        label: 'Görüntüle',
        link: alertData.deviceId ? `/devices/${alertData.deviceId}` : '/settings?tab=system'
      },
      alertData.deviceId ? {
        name: 'acknowledge',
        label: 'Onayla'
      } : null
    ].filter(Boolean)
  });
};

/**
 * Kullanıcı oluşturulduğunda bildirim oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createUserCreatedNotification = async (userData) => {
  return createSystemNotification({
    title: 'Yeni Kullanıcı Oluşturuldu',
    message: `"${userData.username}" kullanıcı adıyla yeni bir kullanıcı oluşturuldu.`,
    category: 'system', // ✅ Basit kategori
    severity: NOTIFICATION_SEVERITY.INFO,
    sourceName: 'User Management',
    metadata: {
      userId: userData.id,
      username: userData.username,
      email: userData.email,
      role: userData.role,
      timestamp: Date.now()
    },
    link: '/users'
  });
};

/**
 * Kullanıcı güncellendiğinde bildirim oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @param {Object} updatedFields - Güncellenen alanlar
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createUserUpdatedNotification = async (userData, updatedFields) => {
  const updatedFieldNames = Object.keys(updatedFields).filter(field => field !== 'updatedAt');

  return createSystemNotification({
    title: 'Kullanıcı Güncellendi',
    message: `"${userData.username}" kullanıcısının bilgileri güncellendi. Güncellenen alanlar: ${updatedFieldNames.join(', ')}`,
    category: 'system', // ✅ Basit kategori
    severity: NOTIFICATION_SEVERITY.INFO,
    sourceName: 'User Management',
    metadata: {
      userId: userData.id,
      username: userData.username,
      updatedFields,
      timestamp: Date.now()
    },
    link: '/users'
  });
};

/**
 * Kullanıcı silindiğinde bildirim oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createUserDeletedNotification = async (userData) => {
  return createSystemNotification({
    title: 'Kullanıcı Silindi',
    message: `"${userData.username}" kullanıcısı sistemden silindi.`,
    category: 'system', // ✅ Basit kategori
    severity: NOTIFICATION_SEVERITY.WARNING,
    sourceName: 'User Management',
    metadata: {
      userId: userData.id,
      username: userData.username,
      email: userData.email,
      role: userData.role,
      timestamp: Date.now()
    },
    link: '/users'
  });
};

/**
 * Kullanıcı şifresi değiştirildiğinde bildirim oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createPasswordChangedNotification = async (userData) => {
  return createSystemNotification({
    title: 'Kullanıcı Şifresi Değiştirildi',
    message: `"${userData.username}" kullanıcısının şifresi değiştirildi.`,
    category: 'system', // ✅ Basit kategori
    severity: NOTIFICATION_SEVERITY.INFO,
    sourceName: 'User Management',
    metadata: {
      userId: userData.id,
      username: userData.username,
      timestamp: Date.now()
    },
    link: '/users'
  });
};

/**
 * Kullanıcı giriş yaptığında bildirim oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @param {string} ipAddress - IP adresi
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createUserLoginNotification = async (userData, ipAddress = 'unknown') => {
  return createSystemNotification({
    title: 'Kullanıcı Girişi',
    message: `"${userData.username}" kullanıcısı sisteme giriş yaptı.`,
    category: 'system', // ✅ Basit sistem - security artık system kategorisinde
    severity: NOTIFICATION_SEVERITY.WARNING, // Warning yaparak Genel Uyarılar kategorisine al
    sourceName: 'Authentication',
    metadata: {
      userId: userData.id,
      username: userData.username,
      ipAddress,
      timestamp: Date.now(),
      action: 'login'
    },
    link: '/users'
  });
};

/**
 * Kullanıcı çıkış yaptığında bildirim oluşturur
 * @param {Object} userData - Kullanıcı verileri
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createUserLogoutNotification = async (userData) => {
  return createSystemNotification({
    title: 'Kullanıcı Çıkışı',
    message: `"${userData.username}" kullanıcısı sistemden çıkış yaptı.`,
    category: 'system', // ✅ Basit sistem - security artık system kategorisinde
    severity: NOTIFICATION_SEVERITY.INFO,
    sourceName: 'Authentication',
    metadata: {
      userId: userData.id,
      username: userData.username,
      timestamp: Date.now(),
      action: 'logout'
    },
    link: '/users'
  });
};

/**
 * Güvenlik bildirimi oluşturur
 * @param {string} title - Bildirim başlığı
 * @param {string} message - Bildirim mesajı
 * @param {string} severity - Bildirim önem derecesi (critical, warning, info)
 * @param {string} userId - Kullanıcı ID (opsiyonel)
 * @param {Object} metadata - Ek bilgiler (opsiyonel)
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createSecurityNotification = async (title, message, severity = 'info', userId = null, metadata = {}) => {
  // Önem derecesini doğrula
  let validatedSeverity;
  switch (severity.toLowerCase()) {
    case 'critical':
    case 'high':
      validatedSeverity = NOTIFICATION_SEVERITY.CRITICAL;
      break;
    case 'warning':
    case 'medium':
      validatedSeverity = NOTIFICATION_SEVERITY.WARNING;
      break;
    case 'success':
      validatedSeverity = NOTIFICATION_SEVERITY.SUCCESS;
      break;
    case 'info':
    case 'low':
    default:
      validatedSeverity = NOTIFICATION_SEVERITY.INFO;
      break;
  }

  return createSystemNotification({
    title,
    message,
    category: 'system', // ✅ Basit sistem - security artık system kategorisinde
    severity: validatedSeverity,
    sourceName: 'Security',
    metadata: {
      ...metadata,
      userId,
      timestamp: Date.now()
    },
    link: '/settings?tab=security'
  });
};

/**
 * Bakım bildirimi oluşturur
 * @param {string} title - Bildirim başlığı
 * @param {string} message - Bildirim mesajı
 * @param {string} severity - Bildirim önem derecesi (critical, warning, info)
 * @param {Object} metadata - Ek bilgiler (opsiyonel)
 * @returns {Promise<Object>} - Oluşturulan bildirim
 */
const createMaintenanceNotification = async (title, message, severity = 'info', metadata = {}) => {
  // Önem derecesini doğrula
  let validatedSeverity;
  switch (severity.toLowerCase()) {
    case 'critical':
    case 'high':
      validatedSeverity = NOTIFICATION_SEVERITY.CRITICAL;
      break;
    case 'warning':
    case 'medium':
      validatedSeverity = NOTIFICATION_SEVERITY.WARNING;
      break;
    case 'success':
      validatedSeverity = NOTIFICATION_SEVERITY.SUCCESS;
      break;
    case 'info':
    case 'low':
    default:
      validatedSeverity = NOTIFICATION_SEVERITY.INFO;
      break;
  }

  return createSystemNotification({
    title,
    message,
    category: 'system', // ✅ Basit sistem - maintenance artık system kategorisinde
    severity: validatedSeverity,
    sourceName: 'Maintenance',
    metadata: {
      ...metadata,
      timestamp: Date.now()
    },
    link: '/settings?tab=system'
  });
};

/**
 * Belirli bir cihazla ilgili tüm bildirimleri siler
 * @param {string} deviceId - Cihaz ID
 * @returns {Promise<Object>} - Silme sonucu
 */
const deleteDeviceNotifications = async (deviceId) => {
  try {
    console.log(`Cihaz bildirimleri siliniyor: ${deviceId}`);

    // Cihazla ilgili bildirimleri al
    const deviceNotificationIds = await redisClient.zrange(`notifications:source:device:${deviceId}`, 0, -1);

    if (!deviceNotificationIds || deviceNotificationIds.length === 0) {
      console.log(`Cihaz ${deviceId} için bildirim bulunamadı`);
      return { success: true, count: 0 };
    }

    console.log(`${deviceNotificationIds.length} cihaz bildirimi siliniyor...`);

    // Redis pipeline oluştur
    const pipeline = redisClient.multi();

    // Her bildirimi tüm listelerden kaldır
    for (const notificationId of deviceNotificationIds) {
      const notification = await redisClient.hgetall(`notification:${notificationId}`);

      if (notification) {
        // JSON alanlarını parse et
        const source = safeJsonParse(notification.source, {});

        // Ana bildirim listesinden kaldır
        pipeline.zrem('notifications', notificationId);

        // Tür listesinden kaldır
        pipeline.zrem(`notifications:type:${notification.type}`, notificationId);

        // Kategori listesinden kaldır
        pipeline.zrem(`notifications:category:${notification.category}`, notificationId);

        // Önem derecesi listesinden kaldır
        pipeline.zrem(`notifications:severity:${notification.severity}`, notificationId);

        // Durum listesinden kaldır
        pipeline.zrem(`notifications:status:${notification.status}`, notificationId);

        // Tür ve kategori kombinasyonlarından kaldır
        pipeline.zrem(`notifications:type:${notification.type}:category:${notification.category}`, notificationId);
        pipeline.zrem(`notifications:type:${notification.type}:severity:${notification.severity}`, notificationId);
        pipeline.zrem(`notifications:category:${notification.category}:severity:${notification.severity}`, notificationId);
        pipeline.zrem(`notifications:type:${notification.type}:status:${notification.status}`, notificationId);
        pipeline.zrem(`notifications:category:${notification.category}:status:${notification.status}`, notificationId);
        pipeline.zrem(`notifications:severity:${notification.severity}:status:${notification.status}`, notificationId);

        // Kaynak listesinden kaldır
        pipeline.zrem(`notifications:source:device:${deviceId}`, notificationId);

        // Bildirim detaylarını sil
        pipeline.del(`notification:${notificationId}`);
      }
    }

    // Kaynak listesini tamamen sil
    pipeline.del(`notifications:source:device:${deviceId}`);

    // Pipeline'ı çalıştır
    await pipeline.exec();

    console.log(`${deviceNotificationIds.length} cihaz bildirimi başarıyla silindi`);

    // Socket.io ile bildirim güncellemesi gönder
    if (socketService.getIO) {
      try {
        socketService.emitToAll('notifications:device:deleted', {
          deviceId,
          deletedCount: deviceNotificationIds.length,
          deletedNotificationIds: deviceNotificationIds
        });
      } catch (error) {
        console.error('Socket.io ile cihaz bildirimi silme güncellemesi gönderilirken hata:', error);
      }
    }

    return { success: true, count: deviceNotificationIds.length };
  } catch (error) {
    console.error(`Cihaz bildirimleri silinirken hata (Device ID: ${deviceId}):`, error);
    throw error;
  }
};

/**
 * Eski bildirimleri temizler
 * @param {number} days - Kaç günden eski bildirimlerin temizleneceği
 * @returns {Promise<Object>} - Temizleme sonucu
 */
const cleanupOldNotifications = async (days = 30) => {
  try {
    const now = Date.now();
    const cutoffTime = now - (days * 24 * 60 * 60 * 1000); // days günden eski

    // Tüm bildirimleri al
    const allNotificationIds = await redisClient.zrangebyscore('notifications', 0, cutoffTime);

    if (!allNotificationIds || allNotificationIds.length === 0) {
      return { success: true, count: 0 };
    }

    console.log(`${allNotificationIds.length} eski bildirim temizleniyor...`);

    // Redis pipeline oluştur
    const pipeline = redisClient.multi();

    // Her bir bildirimi sil
    for (const notificationId of allNotificationIds) {
      // Bildirim detaylarını al
      const notification = await getNotification(notificationId);

      if (notification) {
        // Ana bildirim listesinden kaldır
        pipeline.zrem('notifications', notificationId);

        // Tür listesinden kaldır
        pipeline.zrem(`notifications:type:${notification.type}`, notificationId);

        // Kategori listesinden kaldır
        pipeline.zrem(`notifications:category:${notification.category}`, notificationId);

        // Önem derecesi listesinden kaldır
        pipeline.zrem(`notifications:severity:${notification.severity}`, notificationId);

        // Durum listesinden kaldır
        pipeline.zrem(`notifications:status:${notification.status}`, notificationId);

        // Tür ve kategori listesinden kaldır
        pipeline.zrem(`notifications:type:${notification.type}:category:${notification.category}`, notificationId);

        // Tür ve önem derecesi listesinden kaldır
        pipeline.zrem(`notifications:type:${notification.type}:severity:${notification.severity}`, notificationId);

        // Tür ve durum listesinden kaldır
        pipeline.zrem(`notifications:type:${notification.type}:status:${notification.status}`, notificationId);

        // Kategori ve önem derecesi listesinden kaldır
        pipeline.zrem(`notifications:category:${notification.category}:severity:${notification.severity}`, notificationId);

        // Kategori ve durum listesinden kaldır
        pipeline.zrem(`notifications:category:${notification.category}:status:${notification.status}`, notificationId);

        // Kaynak listesinden kaldır
        if (notification.source && notification.source.type && notification.source.id) {
          pipeline.zrem(`notifications:source:${notification.source.type}:${notification.source.id}`, notificationId);
        }

        // Bildirim detaylarını sil
        pipeline.del(`notification:${notificationId}`);
      }
    }

    // Pipeline'ı çalıştır
    await pipeline.exec();

    return { success: true, count: allNotificationIds.length };
  } catch (error) {
    console.error('Eski bildirimler temizlenirken hata:', error);
    throw error;
  }
};

/**
 * Saatlik olarak çalışacak temizleme işlemi
 * @returns {Promise<void>}
 */
const setupCleanupTask = async () => {
  try {
    // Saatlik olarak çalışacak temizleme işlemi
    setInterval(async () => {
      try {
        console.log('Bildirim temizleme görevi çalışıyor...');

        // 30 günden eski bildirimleri temizle
        const result = await cleanupOldNotifications(30);

        console.log(`Bildirim temizleme tamamlandı: ${result.count} bildirim temizlendi.`);
      } catch (error) {
        console.error('Bildirim temizleme görevi çalışırken hata:', error);
      }
    }, 60 * 60 * 1000); // Her saat

    console.log('Bildirim temizleme görevi başlatıldı.');
  } catch (error) {
    console.error('Bildirim temizleme görevi başlatılırken hata:', error);
  }
};

// ✅ Karmaşık kategori sistemi tamamen kaldırıldı
// Artık basit source/severity/status sistemi kullanılıyor

// ✅ Yeni Basit Bildirim Sayıları Sistemi
const getCounts = async (filters = {}) => {
  try {
    console.log('📊 getCounts: Starting with filters:', filters);
    const startTime = Date.now();

    // Tüm bildirimleri al (filtrelenmiş veya filtresiz)
    const countFilters = { ...filters };
    delete countFilters.limit;   // Limit'i kaldır
    delete countFilters.offset;  // Offset'i kaldır

    const allNotifications = await getNotificationsWithSearch(countFilters);
    console.log(`📊 Found ${allNotifications.length} notifications`);

    // Basit sayıları hesapla
    const counts = calculateSimpleCounts(allNotifications);

    const endTime = Date.now();
    console.log(`📊 getCounts: Completed in ${endTime - startTime}ms`);

    return {
      total: counts.total,
      unread: counts.unread,

      // 📱 Kaynak bazlı sayılar
      sources: {
        device: counts.sources.device,
        system: counts.sources.system
      },

      // ⚡ Önem derecesi bazlı sayılar
      severities: {
        critical: counts.severities.critical,
        warning: counts.severities.warning,
        info: counts.severities.info,
        success: counts.severities.success
      },

      // 📋 Durum bazlı sayılar
      statuses: {
        new: counts.statuses.new,
        read: counts.statuses.read,
        resolved: counts.statuses.resolved
      }
    };
  } catch (error) {
    console.error('Error getting notification counts:', error);
    return {
      total: 0,
      unread: 0,
      sources: { device: 0, system: 0 },
      severities: { critical: 0, warning: 0, info: 0, success: 0 },
      statuses: { new: 0, read: 0, resolved: 0 }
    };
  }
};

// ✅ Yeni Basit Sayı Hesaplama Fonksiyonu
const calculateSimpleCounts = (notifications) => {
  const counts = {
    total: notifications.length,
    unread: 0,
    sources: { device: 0, system: 0 },
    severities: { critical: 0, warning: 0, info: 0, success: 0 },
    statuses: { new: 0, read: 0, resolved: 0 }
  };

  notifications.forEach(notification => {
    // Unread sayısı (new status)
    if (notification.status === 'new') {
      counts.unread++;
    }

    // Kaynak bazlı sayılar
    const sourceType = notification.source?.type || 'system';
    if (counts.sources[sourceType] !== undefined) {
      counts.sources[sourceType]++;
    }

    // Önem derecesi bazlı sayılar
    if (counts.severities[notification.severity] !== undefined) {
      counts.severities[notification.severity]++;
    }

    // Durum bazlı sayılar
    if (counts.statuses[notification.status] !== undefined) {
      counts.statuses[notification.status]++;
    }
  });

  return counts;
};

// ✅ Eski karmaşık calculateDetailedCounts fonksiyonu kaldırıldı

/**
 * E-posta bildirimi gönderir
 * @param {Object} notification - Bildirim verisi
 */
const sendEmailNotification = async (notification) => {
  try {
    // E-posta bildirimleri etkin mi kontrol et
    const settingsService = require('./settingsService');
    const settings = await settingsService.getSettings();

    if (!settings.emailNotifications) {
      console.log('📧 E-posta bildirimleri devre dışı');
      return;
    }

    // Bildirim türüne göre e-posta gönder
    if (notification.source && notification.source.type === 'device') {
      // Cihaz uyarısı e-postası
      await emailService.sendDeviceAlert({
        deviceName: notification.source.name || 'Bilinmeyen Cihaz',
        deviceHost: notification.source.host || notification.source.id || '',
        type: notification.category || 'Genel',
        message: notification.message,
        severity: notification.severity,
        timestamp: notification.timestamp
      });
      console.log('📧 Cihaz uyarısı e-postası gönderildi');
    } else {
      // Sistem bildirimi e-postası
      await emailService.sendSystemNotification({
        title: notification.title,
        message: notification.message,
        severity: notification.severity,
        timestamp: notification.timestamp
      });
      console.log('📧 Sistem bildirimi e-postası gönderildi');
    }
  } catch (error) {
    console.error('E-posta bildirimi gönderilirken hata:', error);
    // E-posta hatası throw etme - bildirim oluşturmayı engellemez
  }
};

// Uygulama başladığında temizleme görevini başlat
setupCleanupTask();

module.exports = {
  // ✅ Basit Sabitler
  NOTIFICATION_TYPES,
  NOTIFICATION_SOURCES,
  NOTIFICATION_SEVERITY,
  NOTIFICATION_STATUS,

  // Ana fonksiyonlar
  createNotification,
  getNotification,
  getNotifications,
  getNotificationCount,
  getCounts,
  updateNotificationStatus,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  resolveNotification,
  deleteDeviceNotifications,
  cleanupOldNotifications,

  // Özel bildirim oluşturma fonksiyonları
  createDeviceStatusNotification,
  createSystemNotification,
  createUserNotification,
  createTestNotification,
  createUserCreatedNotification,
  createUserUpdatedNotification,
  createUserDeletedNotification,
  createPasswordChangedNotification,
  createUserLoginNotification,
  createUserLogoutNotification,
  createSecurityNotification,
  createMaintenanceNotification,

  // Uyumluluk fonksiyonları
  createAlertCompatibility
};
