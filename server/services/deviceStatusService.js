/**
 * Cihaz durumu hesaplama servisi
 */

/**
 * Cihaz durumunu hesaplar
 * @param {Object} deviceData - Cihaz bilgileri
 * @param {Object} monitorResults - İzleme sonuçları
 * @returns {Object} - Hesaplanan durum bilgileri (status, reason)
 */
const calculateDeviceStatus = (deviceData, monitorResults) => {
  // İzleme türleri için öncelik değerleri
  const monitorPriorities = {
    icmp: 3,     // Yüksek öncelik
    http: 2,     // Orta öncelik
    tcp: 2,      // Orta öncelik
    dns: 1,      // Düşük öncelik
    ssl: 1,      // Düşük öncelik
    database: 3, // Yüksek öncelik
    api: 2,      // Orta öncelik
    snmp: 1,     // Düşük öncelik
    smtp: 2,     // Orta öncelik - Email servisleri önemli
    windows: 3,  // Yüksek öncelik - Sistem izleme kritik
    linux: 3,    // Yüksek öncelik - Sistem izleme kritik
    ipmi: 2,     // Orta öncelik - Donanım izleme önemli
    docker: 2    // Orta öncelik - Container servisleri önemli
  };

  // Etkinleştirilmiş izleme protokollerini bul
  const monitors = deviceData.monitors || {};
  const enabledMonitors = [];
  const monitorStatuses = [];
  const monitorLastChecks = [];

  // ICMP her zaman kontrol edilir
  if (monitorResults.icmp) {
    enabledMonitors.push('icmp');
    monitorStatuses.push(monitorResults.icmp.status);
    monitorLastChecks.push(parseInt(monitorResults.icmp.lastCheck || 0));
  }

  // Diğer protokolleri kontrol et
  const monitorTypes = ['http', 'tcp', 'dns', 'ssl', 'database', 'api', 'snmp', 'smtp', 'system', 'ipmi', 'docker'];
  monitorTypes.forEach(type => {
    if (monitors[type]?.enabled && monitorResults[type]) {
      enabledMonitors.push(type);
      monitorStatuses.push(monitorResults[type].status);
      monitorLastChecks.push(parseInt(monitorResults[type].lastCheck || 0));
    }
  });

  // Etkinleştirilmiş izleme yoksa 'unknown' döndür
  if (enabledMonitors.length === 0) {
    return {
      status: 'unknown',
      reason: 'Etkinleştirilmiş izleme yok'
    };
  }

  // Zaman bazlı kontrol kaldırıldı - sadece izleme türlerinin durumlarına göre hesaplama yapılacak
  // İzleme türlerinin son kontrol ve sonraki kontrol bilgileri korunuyor, ancak cihaz durumu hesaplamasında kullanılmıyor

  // Durum analizi
  let hasUpMonitors = false;
  let hasDownMonitors = false;
  let hasErrorMonitors = false;
  let highestDownPriority = 0;

  // Down olan servisleri ve önceliklerini bul
  const downMonitors = [];
  const errorMonitors = [];

  enabledMonitors.forEach((type, index) => {
    const status = monitorStatuses[index];
    const priority = monitorPriorities[type] || 1;

    if (status === 'up') {
      hasUpMonitors = true;
    } else if (status === 'down') {
      hasDownMonitors = true;
      downMonitors.push(type.toUpperCase());

      if (priority > highestDownPriority) {
        highestDownPriority = priority;
      }
    } else if (status === 'error') {
      hasErrorMonitors = true;
      errorMonitors.push(type.toUpperCase());

      if (priority > highestDownPriority) {
        highestDownPriority = priority;
      }
    }
  });

  // Durum ve sebep belirleme
  let status = 'unknown';
  let reason = '';

  // Tüm izlemeler başarılıysa 'up' döndür
  if (hasUpMonitors && !hasDownMonitors && !hasErrorMonitors) {
    status = 'up';
    reason = 'Tüm servisler çalışıyor';
  }
  // Tüm izlemeler başarısızsa 'down' döndür
  else if (!hasUpMonitors && (hasDownMonitors || hasErrorMonitors)) {
    status = 'down';
    reason = `${[...downMonitors, ...errorMonitors].join(', ')} servisleri yanıt vermiyor`;
  }
  // Yüksek öncelikli bir servis down ise 'down' döndür
  else if (highestDownPriority >= 3) {
    status = 'down';

    // Hangi yüksek öncelikli servisler down?
    const criticalServices = [];
    enabledMonitors.forEach((type, index) => {
      const monitorStatus = monitorStatuses[index];
      const priority = monitorPriorities[type] || 1;

      if ((monitorStatus === 'down' || monitorStatus === 'error') && priority >= 3) {
        criticalServices.push(type.toUpperCase());
      }
    });

    reason = `Kritik servisler (${criticalServices.join(', ')}) yanıt vermiyor`;
  }
  // Orta öncelikli bir servis down ise 'critical' döndür
  else if (highestDownPriority === 2) {
    status = 'critical';

    // Hangi orta öncelikli servisler down?
    const mediumServices = [];
    enabledMonitors.forEach((type, index) => {
      const monitorStatus = monitorStatuses[index];
      const priority = monitorPriorities[type] || 1;

      if ((monitorStatus === 'down' || monitorStatus === 'error') && priority === 2) {
        mediumServices.push(type.toUpperCase());
      }
    });

    reason = `Önemli servisler (${mediumServices.join(', ')}) yanıt vermiyor`;
  }
  // Sadece düşük öncelikli servisler down ise 'warning' döndür
  else if (hasUpMonitors && (hasDownMonitors || hasErrorMonitors)) {
    status = 'warning';

    // Hangi düşük öncelikli servisler down?
    const lowServices = [];
    enabledMonitors.forEach((type, index) => {
      const monitorStatus = monitorStatuses[index];
      const priority = monitorPriorities[type] || 1;

      if ((monitorStatus === 'down' || monitorStatus === 'error') && priority === 1) {
        lowServices.push(type.toUpperCase());
      }
    });

    reason = `Düşük öncelikli servisler (${lowServices.join(', ')}) yanıt vermiyor`;
  }
  // Sadece error durumları varsa 'degraded' döndür
  else if (hasUpMonitors && !hasDownMonitors && hasErrorMonitors) {
    status = 'degraded';
    reason = `${errorMonitors.join(', ')} servislerinde hata var`;
  }

  return { status, reason };
};

/**
 * Cihaz durumunu hesaplar ve Redis'e kaydeder
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} deviceData - Cihaz bilgileri
 * @param {Object} monitorResults - İzleme sonuçları
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Object} - Hesaplanan durum bilgileri
 */
const calculateAndSaveDeviceStatus = async (deviceId, deviceData, monitorResults, redisClient) => {
  try {
    // Durumu ve sebebi hesapla
    const { status, reason } = calculateDeviceStatus(deviceData, monitorResults);

    // Son durumları al (flapping kontrolü için)
    const historyKey = `device:status:history:${deviceId}`;
    const statusHistory = await redisClient.lrange(historyKey, 0, 9); // Son 10 durum

    // Yeni durumu geçmişe ekle
    await redisClient.lpush(historyKey, JSON.stringify({
      status,
      reason,
      timestamp: Date.now()
    }));
    await redisClient.ltrim(historyKey, 0, 9); // Sadece son 10 durumu tut

    // Flapping kontrolü
    let finalStatus = status;
    let finalReason = reason;

    if (statusHistory.length >= 5) {
      const lastStatuses = statusHistory.map(item => JSON.parse(item).status);
      const uniqueStatuses = new Set(lastStatuses);

      // Son 5 durumda 3 veya daha fazla farklı durum varsa "flapping"
      if (uniqueStatuses.size >= 3) {
        finalStatus = 'flapping';
        finalReason = 'Cihaz durumu sürekli değişiyor';
      }
    }

    // Redis'e kaydet
    await redisClient.hset(`device:status:${deviceId}`, {
      status: finalStatus,
      reason: finalReason,
      rawStatus: status // Hesaplanan ham durum
    });

    return { status: finalStatus, reason: finalReason };
  } catch (error) {
    console.error(`Error calculating device status for ${deviceId}:`, error);
    return {
      status: 'unknown',
      reason: 'Hesaplama sırasında hata oluştu: ' + error.message
    };
  }
};

/**
 * Cihaz durumunu Redis'ten alır
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} redisClient - Redis istemcisi
 * @returns {Object} - Durum bilgileri
 */
const getDeviceStatus = async (deviceId, redisClient) => {
  try {
    const statusData = await redisClient.hgetall(`device:status:${deviceId}`);
    if (!statusData) {
      return {
        status: 'unknown',
        reason: 'Durum bilgisi bulunamadı'
      };
    }
    return statusData;
  } catch (error) {
    console.error(`Error getting device status for ${deviceId}:`, error);
    return {
      status: 'unknown',
      reason: 'Durum bilgisi alınırken hata oluştu'
    };
  }
};

/**
 * Cihazın durum geçmişini Redis'ten alır
 * @param {string} deviceId - Cihaz ID'si
 * @param {Object} redisClient - Redis istemcisi
 * @param {number} limit - Kaç kayıt alınacağı
 * @returns {Array} - Durum geçmişi
 */
const getDeviceStatusHistory = async (deviceId, redisClient, limit = 10) => {
  try {
    const historyKey = `device:status:history:${deviceId}`;
    const history = await redisClient.lrange(historyKey, 0, limit - 1);

    if (!history || history.length === 0) {
      return [];
    }

    return history.map(item => JSON.parse(item));
  } catch (error) {
    console.error(`Error getting device status history for ${deviceId}:`, error);
    return [];
  }
};

module.exports = {
  calculateDeviceStatus,
  calculateAndSaveDeviceStatus,
  getDeviceStatus,
  getDeviceStatusHistory
};
