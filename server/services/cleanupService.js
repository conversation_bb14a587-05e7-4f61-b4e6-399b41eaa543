/**
 * Te<PERSON>zleme servisi
 * Eski verileri ve bildirimleri temizler
 */
const redisClient = require('./redis');
const settingsService = require('./settingsService');

/**
 * <PERSON><PERSON>li bir tarihten önce oluşturulan bildirimleri temizler
 * @param {number} days - <PERSON><PERSON><PERSON> say<PERSON>
 * @returns {Promise<Object>} - Temizleme sonucu
 */
const cleanupOldNotifications = async (days = 30) => {
  try {
    const now = Date.now();
    const cutoffTime = now - (days * 24 * 60 * 60 * 1000); // days günden eski

    // Tüm bildirim ID'lerini al
    const notificationIds = await redisClient.zrangebyscore('notifications', 0, cutoffTime);

    if (!notificationIds || notificationIds.length === 0) {
      return { success: true, count: 0 };
    }

    console.log(`Cleaning up ${notificationIds.length} old notifications older than ${days} days`);

    // Her bildirim için temizleme işlemi yap - daha verimli bir şekilde
    // Önce tüm bildirimlerin detaylarını al
    const pipeline = redisClient.pipeline();
    notificationIds.forEach(id => {
      pipeline.hgetall(`notification:${id}`);
    });

    const results = await pipeline.exec();
    const notifications = results.map(result => result[1]);

    // Silme işlemleri için yeni bir pipeline oluştur
    const deletePipeline = redisClient.pipeline();

    notifications.forEach((notification, index) => {
      const id = notificationIds[index];

      if (!notification) return;

      // Bildirim kaynağı varsa, kaynak listesinden de sil
      if (notification.source) {
        try {
          const source = JSON.parse(notification.source);
          if (source && source.type && source.id) {
            deletePipeline.zrem(`notifications:source:${source.type}:${source.id}`, id);
          }
        } catch (e) {
          console.error(`Error parsing notification source for ${id}:`, e);
        }
      }

      // Bildirim türü listesinden sil
      if (notification.type) {
        deletePipeline.zrem(`notifications:type:${notification.type}`, id);
      }

      // Ana bildirim listesinden sil
      deletePipeline.zrem('notifications', id);

      // Bildirim detaylarını sil
      deletePipeline.del(`notification:${id}`);
    });

    // Tüm silme işlemlerini bir seferde gerçekleştir
    await deletePipeline.exec();

    return { success: true, count: notificationIds.length };
  } catch (error) {
    console.error('Error cleaning up old notifications:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Belirli bir izleme türü için eski verileri temizler
 * @param {string} monitorType - İzleme türü (icmp, http, tcp, snmp, dns, ssl, database, api)
 * @param {number} days - Gün sayısı
 * @returns {Promise<Object>} - Temizleme sonucu
 */
const cleanupOldMonitoringData = async (monitorType, days = 30) => {
  try {
    const now = Date.now();
    const cutoffTime = now - (days * 24 * 60 * 60 * 1000); // days günden eski
    let count = 0;

    // Tüm cihazları al
    const deviceIds = await redisClient.smembers('devices');

    // Her cihaz için temizleme işlemi yap
    for (const deviceId of deviceIds) {
      // Geçmiş verileri al
      const historyKey = `history:${monitorType}:${deviceId}`;
      const historyData = await redisClient.lrange(historyKey, 0, -1);

      if (!historyData || historyData.length === 0) continue;

      // Eski verileri bul
      const oldDataIndices = [];
      for (let i = 0; i < historyData.length; i++) {
        try {
          const data = JSON.parse(historyData[i]);
          if (data.timestamp && data.timestamp < cutoffTime) {
            oldDataIndices.push(i);
          }
        } catch (e) {
          console.error(`Error parsing history data for ${monitorType}:${deviceId}:`, e);
        }
      }

      // Eski verileri sil
      if (oldDataIndices.length > 0) {
        // Yeni bir liste oluştur (eski verileri içermeyen)
        const newHistoryData = historyData.filter((_, index) => !oldDataIndices.includes(index));

        // Listeyi güncelle
        if (newHistoryData.length > 0) {
          // Daha verimli bir şekilde listeyi güncelle
          const pipeline = redisClient.pipeline();
          pipeline.del(historyKey);
          newHistoryData.forEach(item => {
            pipeline.rpush(historyKey, item);
          });
          await pipeline.exec();
        } else {
          // Tüm veriler eskiyse, listeyi tamamen sil
          await redisClient.del(historyKey);
        }

        count += oldDataIndices.length;
      }
    }

    return { success: true, count };
  } catch (error) {
    console.error(`Error cleaning up old ${monitorType} data:`, error);
    return { success: false, error: error.message };
  }
};

/**
 * Tüm temizleme işlemlerini çalıştırır
 * @returns {Promise<Object>} - Temizleme sonucu
 */
const runCleanupTasks = async () => {
  try {
    // Ayarları al
    const settings = await settingsService.getSettings();

    // Bildirim saklama süresi
    const notificationRetentionDays = parseInt(settings.notificationRetentionDays || 30);

    // İzleme türleri için saklama süreleri
    const icmpRetentionDays = parseInt(settings.icmpRetentionDays || 30);
    const httpRetentionDays = parseInt(settings.httpRetentionDays || 30);
    const tcpRetentionDays = parseInt(settings.tcpRetentionDays || 30);
    const snmpRetentionDays = parseInt(settings.snmpRetentionDays || 30);
    const dnsRetentionDays = parseInt(settings.dnsRetentionDays || 30);
    const sslRetentionDays = parseInt(settings.sslRetentionDays || 30);
    const databaseRetentionDays = parseInt(settings.databaseRetentionDays || 30);
    const apiRetentionDays = parseInt(settings.apiRetentionDays || 30);
    const smtpRetentionDays = parseInt(settings.smtpRetentionDays || 30);
    const systemRetentionDays = parseInt(settings.systemRetentionDays || 30);
    const dockerRetentionDays = parseInt(settings.dockerRetentionDays || 30);

    // Eski bildirimleri temizle
    const notificationResult = await cleanupOldNotifications(notificationRetentionDays);

    // Eski izleme verilerini temizle
    const icmpResult = await cleanupOldMonitoringData('icmp', icmpRetentionDays);
    const httpResult = await cleanupOldMonitoringData('http', httpRetentionDays);
    const tcpResult = await cleanupOldMonitoringData('tcp', tcpRetentionDays);
    const snmpResult = await cleanupOldMonitoringData('snmp', snmpRetentionDays);
    const dnsResult = await cleanupOldMonitoringData('dns', dnsRetentionDays);
    const sslResult = await cleanupOldMonitoringData('ssl', sslRetentionDays);
    const databaseResult = await cleanupOldMonitoringData('database', databaseRetentionDays);
    const apiResult = await cleanupOldMonitoringData('api', apiRetentionDays);
    const smtpResult = await cleanupOldMonitoringData('smtp', smtpRetentionDays);
    const systemResult = await cleanupOldMonitoringData('system', systemRetentionDays);
    const dockerResult = await cleanupOldMonitoringData('docker', dockerRetentionDays);

    return {
      success: true,
      notifications: notificationResult,
      monitoring: {
        icmp: icmpResult,
        http: httpResult,
        tcp: tcpResult,
        snmp: snmpResult,
        dns: dnsResult,
        ssl: sslResult,
        database: databaseResult,
        api: apiResult,
        smtp: smtpResult,
        system: systemResult,
        docker: dockerResult
      }
    };
  } catch (error) {
    console.error('Error running cleanup tasks:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  cleanupOldNotifications,
  cleanupOldMonitoringData,
  runCleanupTasks
};
