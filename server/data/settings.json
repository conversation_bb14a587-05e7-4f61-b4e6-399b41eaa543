{"emailNotifications": false, "emailServer": "smtp.gmail.com", "emailPort": 587, "emailUser": "<EMAIL>", "emailPassword": "uwdxplenuuadviab", "emailFrom": "<EMAIL>", "emailTo": "<EMAIL>,<EMAIL>", "emailSecure": true, "notificationRetentionDays": "30", "notifyOnCritical": true, "notifyOnWarning": true, "notifyOnInfo": true, "notifyOnSuccess": true, "notifyOnDevice": true, "notifyOnSystem": true, "toastsEnabled": true, "toastDuration": "8000", "toastDeduplication": false, "toastGrouping": false, "toastSounds": false, "language": "en", "timezone": "Europe/Istanbul", "dateFormat": "DD.MM.YYYY", "timeFormat": "24h", "appTitle": "NetWatch", "companyName": "NetWatch", "defaultPingInterval": "1", "defaultHttpInterval": "10", "defaultDnsInterval": "10", "defaultSslInterval": "60", "defaultTcpInterval": "5", "defaultSnmpInterval": "5", "defaultDatabaseInterval": "10", "defaultApiInterval": "10", "defaultSmtpInterval": "15", "defaultWindowsInterval": "15", "defaultDnsServer": "8.8.8.8", "pingRetentionDays": "30", "httpRetentionDays": "1", "dnsRetentionDays": "30", "sslRetentionDays": "30", "tcpRetentionDays": "30", "snmpRetentionDays": "30", "databaseRetentionDays": "90", "apiRetentionDays": "30", "smtpRetentionDays": "30", "windowsRetentionDays": "30", "sessionTimeout": "60", "passwordPolicy": "medium", "passwordExpiryDays": "60", "maxLoginAttempts": "5", "accountLockDuration": "30", "bruteForceProtection": true, "securityLogRetention": "30", "autoLogout": true, "systemHealthCheckInterval": "1"}