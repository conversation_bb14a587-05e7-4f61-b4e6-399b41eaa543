const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const cookieParser = require('cookie-parser');

// Yapılandırma dosyasını yükle
dotenv.config();

// Sunucu IP adresini localhost olarak ayarla
// Artık otomatik IP tespitine gerek yok, çünkü her zaman localhost kullanıyoruz
const SERVER_IP = 'localhost';
console.log('Sunucu IP adresi:', SERVER_IP);

// Redis bağlantısını başlat
const redisClient = require('./services/redis');

// Zamanlayıcı servisini başlat
const scheduler = require('./services/scheduler');

// Socket.io servisini yükle
const socketService = require('./services/socketService');

// <PERSON><PERSON><PERSON><PERSON> servis<PERSON> yükle
const notificationService = require('./services/notificationService');

// Express uygulamasını oluştur
const app = express();
const server = http.createServer(app);

// En basit CORS yapılandırması - tüm isteklere izin ver
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'false');

  // Preflight OPTIONS isteklerine hemen yanıt ver
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// CORS middleware'ini ekle
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
  credentials: false
}));

// JSON ve URL-encoded verileri işle
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Cookie parser'ı ekle
app.use(cookieParser());

// Oturum zaman aşımı middleware'ini ekle
const { sessionTimeoutMiddleware } = require('./middleware/sessionTimeout');
app.use(sessionTimeoutMiddleware);

// API rotalarını yükle
const deviceRoutes = require('./routes/devices');
const monitorRoutes = require('./routes/monitors');
const alertRoutes = require('./routes/alerts');
const notificationRoutes = require('./routes/notifications'); // Yeni bildirim rotası
const settingsRoutes = require('./routes/settings');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const systemRoutes = require('./routes/system');

// Sunucu bilgilerini döndüren endpoint
app.get('/api/server-info', (req, res) => {
  res.json({
    ip: SERVER_IP,
    port: process.env.PORT || 5000
  });
});

app.use('/api/devices', deviceRoutes);
app.use('/api/monitors', monitorRoutes);
app.use('/api/alerts', alertRoutes);
app.use('/api/notifications', notificationRoutes); // Yeni bildirim rotası
app.use('/api/settings', settingsRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/system', systemRoutes);

// Socket.io servisini başlat
const io = socketService.init(server);

// Socket.io nesnesini global olarak erişilebilir yap
app.set('io', io);

// Production modunda statik dosyaları sun
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/build')));

  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/build', 'index.html'));
  });
} else {
  // Development modunda statik dosyaları sun
  app.use(express.static(path.join(__dirname, '../client/public')));
}

// Sunucuyu başlat
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // Zamanlayıcıyı başlat
  scheduler.initScheduler(io);

  // Zamanlayıcı durumunu global olarak sakla
  global.schedulerRunning = true;
  global.lastSchedulerRun = Date.now();

  // Test uyarısı oluşturma kodu kaldırıldı
  // Test bildirimlerini manuel olarak oluşturmak için /api/notifications/test ve /api/alerts/test endpoint'leri kullanılabilir
});

// Beklenmeyen hataları yakala
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = { app, server };
