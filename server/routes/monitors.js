const express = require('express');
const router = express.Router();
const redisClient = require('../config/redis');
const scheduler = require('../services/scheduler');

// İzleme modüllerini yükle
const icmpMonitor = require('../services/monitors/icmp');
const httpMonitor = require('../services/monitors/http');
const tcpMonitor = require('../services/monitors/tcp');
const snmpMonitor = require('../services/monitors/snmp');

// Yeni izleme modüllerini yükle
const dnsMonitor = require('../services/monitors/dns-monitor');
const sslMonitor = require('../services/monitors/ssl-monitor');
const databaseMonitor = require('../services/monitors/database-monitor');
const apiMonitor = require('../services/monitors/api-monitor');
const smtpMonitor = require('../services/monitors/smtp-monitor');
const systemMonitor = require('../services/monitors/system-monitor');
const dockerMonitor = require('../services/monitors/docker-monitor');

/**
 * Bir cihazın durumunu manuel olarak kontrol eder
 * POST /api/monitors/check/:id
 */
router.post('/check/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Cihaz detaylarını al
    const deviceData = await redisClient.hgetall(`device:${id}`);
    if (!deviceData) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Monitors alanını JSON'dan parse et
    if (deviceData.monitors) {
      try {
        deviceData.monitors = JSON.parse(deviceData.monitors);
      } catch (e) {
        deviceData.monitors = {};
      }
    } else {
      deviceData.monitors = {};
    }

    // Alerts alanını JSON'dan parse et
    if (deviceData.alerts) {
      try {
        deviceData.alerts = JSON.parse(deviceData.alerts);
      } catch (e) {
        deviceData.alerts = [];
      }
    } else {
      deviceData.alerts = [];
    }

    // Cihazı izle
    const io = req.app.get('io');

    // Cihaz için son kontrol zamanlarını oluştur
    const deviceId = id;
    const monitors = deviceData.monitors || {};

    // Global lastCheckTimes değişkenini başlat
    if (!global.lastCheckTimes) {
      global.lastCheckTimes = {};
    }

    // Cihaz için son kontrol zamanlarını oluştur
    global.lastCheckTimes[deviceId] = global.lastCheckTimes[deviceId] || {};

    // Kontrol edilecek izleme türlerini belirle
    const monitorTypes = ['icmp', 'http', 'tcp', 'snmp', 'dns', 'ssl', 'database', 'api', 'smtp', 'system', 'ipmi', 'docker'];
    const monitorsToCheck = [];

    for (const type of monitorTypes) {
      if (type === 'icmp' || (monitors[type] && monitors[type].enabled)) {
        monitorsToCheck.push(type);
      }
    }

    // Cihazı manuel olarak kontrol et (isManualCheck = true)
    const results = await scheduler.monitorDevice({ id, ...deviceData }, io, [], true);

    // Kontrol işlemi başarıyla tamamlandıktan sonra son kontrol zamanlarını güncelle
    const now = Date.now();
    for (const type of monitorsToCheck) {
      global.lastCheckTimes[deviceId][type] = now;
    }

    console.log(`Updated last check times for device ${deviceData.name} (${deviceId}) after manual check`);

    // Manuel kontrol sonuçlarını döndür
    res.json(results);

    // Manuel kontrol sonrası otomatik kontrolü hemen başlat
    // Bu, HTTP yanıtını beklemeden arka planda gerçekleşecek
    setTimeout(async () => {
      try {
        console.log(`Starting automatic check for device ${deviceData.name} (${deviceId}) after manual check`);
        // Otomatik kontrol için isManualCheck = false
        await scheduler.monitorDevice({ id, ...deviceData }, io, [], false);
        console.log(`Automatic check for device ${deviceData.name} (${deviceId}) completed after manual check`);
      } catch (error) {
        console.error(`Error starting automatic check for device ${deviceData.name} (${deviceId}) after manual check:`, error);
      }
    }, 1000); // 1 saniye sonra otomatik kontrolü başlat
  } catch (error) {
    console.error('Error checking device:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın ICMP durumunu getirir
 * GET /api/monitors/icmp/:id
 */
router.get('/icmp/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // ICMP durumunu al
    const icmpData = await redisClient.hgetall(`monitor:icmp:${id}`);
    if (!icmpData) {
      // ICMP izleme devre dışı bırakıldığı için varsayılan olarak 'up' durumu döndür
      return res.json({
        status: 'up',
        responseTime: 0,
        lastCheck: Date.now(),
        error: '',
        details: JSON.stringify({
          alive: true,
          time: 0,
          min: 0,
          max: 0,
          avg: 0,
          stddev: 0,
          packetLoss: 0,
          status: 'up'
        })
      });
    }

    res.json(icmpData);
  } catch (error) {
    console.error('Error getting ICMP status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın HTTP durumunu getirir
 * GET /api/monitors/http/:id
 */
router.get('/http/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // HTTP durumunu al
    const httpData = await redisClient.hgetall(`monitor:http:${id}`);
    if (!httpData) {
      return res.json({
        status: 'unknown',
        statusCode: 0,
        responseTime: 0,
        lastCheck: null,
        nextCheck: null,
        error: ''
      });
    }

    // Details alanını JSON'dan parse et
    if (httpData.details) {
      try {
        httpData.details = JSON.parse(httpData.details);
      } catch (e) {
        httpData.details = {};
      }
    } else {
      httpData.details = {};
    }

    // Sayısal değerleri dönüştür
    if (httpData.lastCheck) httpData.lastCheck = parseInt(httpData.lastCheck);
    if (httpData.nextCheck) httpData.nextCheck = parseInt(httpData.nextCheck);
    if (httpData.responseTime) httpData.responseTime = parseInt(httpData.responseTime);

    res.json(httpData);
  } catch (error) {
    console.error('Error getting HTTP status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın TCP port durumunu getirir
 * GET /api/monitors/tcp/:id/:port
 */
router.get('/tcp/:id/:port', async (req, res) => {
  try {
    const { id, port } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // TCP durumunu al
    const tcpData = await redisClient.hgetall(`monitor:tcp:${id}:${port}`);
    if (!tcpData) {
      return res.json({
        status: 'unknown',
        port: parseInt(port),
        responseTime: 0,
        lastCheck: null,
        error: ''
      });
    }

    res.json(tcpData);
  } catch (error) {
    console.error('Error getting TCP status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın SNMP durumunu getirir
 * GET /api/monitors/snmp/:id
 */
router.get('/snmp/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // SNMP durumunu al
    const snmpData = await redisClient.hgetall(`monitor:snmp:${id}`);
    if (!snmpData) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        data: '{}'
      });
    }

    // Data alanını JSON'dan parse et
    if (snmpData.data) {
      try {
        snmpData.data = JSON.parse(snmpData.data);
      } catch (e) {
        snmpData.data = {};
      }
    } else {
      snmpData.data = {};
    }

    res.json(snmpData);
  } catch (error) {
    console.error('Error getting SNMP status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın DNS durumunu getirir
 * GET /api/monitors/dns/:id
 */
router.get('/dns/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // DNS durumunu al
    const dnsData = await redisClient.hgetall(`monitor:dns:${id}`);
    if (!dnsData || Object.keys(dnsData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (dnsData.details) {
      try {
        dnsData.details = JSON.parse(dnsData.details);
      } catch (e) {
        dnsData.details = {};
      }
    } else {
      dnsData.details = {};
    }

    res.json(dnsData);
  } catch (error) {
    console.error('Error getting DNS status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın SSL durumunu getirir
 * GET /api/monitors/ssl/:id
 */
router.get('/ssl/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // SSL durumunu al
    const sslData = await redisClient.hgetall(`monitor:ssl:${id}`);
    if (!sslData || Object.keys(sslData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (sslData.details) {
      try {
        sslData.details = JSON.parse(sslData.details);
      } catch (e) {
        sslData.details = {};
      }
    } else {
      sslData.details = {};
    }

    res.json(sslData);
  } catch (error) {
    console.error('Error getting SSL status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın Database durumunu getirir
 * GET /api/monitors/database/:id
 */
router.get('/database/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Database durumunu al
    const dbData = await redisClient.hgetall(`monitor:database:${id}`);
    if (!dbData || Object.keys(dbData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (dbData.details) {
      try {
        dbData.details = JSON.parse(dbData.details);
      } catch (e) {
        dbData.details = {};
      }
    } else {
      dbData.details = {};
    }

    res.json(dbData);
  } catch (error) {
    console.error('Error getting Database status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın SMTP durumunu getirir
 * GET /api/monitors/smtp/:id
 */
router.get('/smtp/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // SMTP durumunu al
    const smtpData = await redisClient.hgetall(`monitor:smtp:${id}`);
    if (!smtpData || Object.keys(smtpData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (smtpData.details) {
      try {
        smtpData.details = JSON.parse(smtpData.details);
      } catch (e) {
        smtpData.details = {};
      }
    } else {
      smtpData.details = {};
    }

    res.json(smtpData);
  } catch (error) {
    console.error('Error getting SMTP status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın sistem durumunu getirir
 * GET /api/monitors/system/:id
 */
router.get('/system/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Sistem durumunu al
    const systemData = await redisClient.hgetall(`monitor:system:${id}`);
    if (!systemData || Object.keys(systemData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (systemData.details) {
      try {
        systemData.details = JSON.parse(systemData.details);
      } catch (e) {
        systemData.details = {};
      }
    } else {
      systemData.details = {};
    }

    res.json(systemData);
  } catch (error) {
    console.error('Error getting System status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});



/**
 * Bir cihazın IPMI donanım durumunu getirir
 * GET /api/monitors/ipmi/:id
 */
router.get('/ipmi/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // IPMI durumunu al
    const ipmiData = await redisClient.hgetall(`monitor:ipmi:${id}`);
    if (!ipmiData || Object.keys(ipmiData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (ipmiData.details) {
      try {
        ipmiData.details = JSON.parse(ipmiData.details);
      } catch (e) {
        ipmiData.details = {};
      }
    } else {
      ipmiData.details = {};
    }

    res.json(ipmiData);
  } catch (error) {
    console.error('Error getting IPMI status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın Docker konteyner durumunu getirir
 * GET /api/monitors/docker/:id
 */
router.get('/docker/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Docker durumunu al
    const dockerData = await redisClient.hgetall(`monitor:docker:${id}`);
    if (!dockerData || Object.keys(dockerData).length === 0) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (dockerData.details) {
      try {
        dockerData.details = JSON.parse(dockerData.details);
      } catch (e) {
        dockerData.details = {};
      }
    } else {
      dockerData.details = {};
    }

    res.json(dockerData);
  } catch (error) {
    console.error('Error getting Docker status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş ICMP verilerini getirir
 * GET /api/monitors/history/icmp/:id
 * Query parametreleri:
 * - timeRange: Zaman aralığı (1h, 6h, 24h, 7d, 30d)
 */
router.get('/history/icmp/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { timeRange } = req.query;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:icmp:${id}`, 0, -1);

    // Eğer geçmiş veri yoksa, varsayılan veri oluştur
    if (!historyData || historyData.length === 0) {
      // Son 24 saat için saatlik veri oluştur
      const fakeHistory = [];
      const now = Date.now();
      for (let i = 0; i < 24; i++) {
        fakeHistory.push({
          timestamp: now - (i * 3600000), // Her saat için geriye git
          status: 'up',
          responseTime: 0
        });
      }
      return res.json(fakeHistory);
    }

    // JSON'a dönüştür
    let history = historyData.map(item => JSON.parse(item));

    // Zaman aralığına göre filtrele
    if (timeRange) {
      const now = Date.now();
      let timeLimit;

      switch (timeRange) {
        case '1h':
          timeLimit = now - (60 * 60 * 1000); // 1 saat
          break;
        case '6h':
          timeLimit = now - (6 * 60 * 60 * 1000); // 6 saat
          break;
        case '24h':
          timeLimit = now - (24 * 60 * 60 * 1000); // 24 saat
          break;
        case '7d':
          timeLimit = now - (7 * 24 * 60 * 60 * 1000); // 7 gün
          break;
        case '30d':
          timeLimit = now - (30 * 24 * 60 * 60 * 1000); // 30 gün
          break;
        default:
          timeLimit = 0; // Filtreleme yapma
      }

      if (timeLimit > 0) {
        history = history.filter(item => new Date(item.timestamp).getTime() >= timeLimit);
      }
    }

    res.json(history);
  } catch (error) {
    console.error('Error getting ICMP history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş HTTP verilerini getirir
 * GET /api/monitors/history/http/:id
 */
router.get('/history/http/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:http:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting HTTP history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş TCP port verilerini getirir
 * GET /api/monitors/history/tcp/:id/:port
 */
router.get('/history/tcp/:id/:port', async (req, res) => {
  try {
    const { id, port } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:tcp:${id}:${port}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting TCP history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş SNMP verilerini getirir
 * GET /api/monitors/history/snmp/:id
 */
router.get('/history/snmp/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:snmp:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting SNMP history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın DNS durumunu getirir
 * GET /api/monitors/dns/:id
 */
router.get('/dns/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // DNS durumunu al
    const dnsData = await redisClient.hgetall(`monitor:dns:${id}`);
    if (!dnsData) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (dnsData.details) {
      try {
        dnsData.details = JSON.parse(dnsData.details);
      } catch (e) {
        dnsData.details = {};
      }
    } else {
      dnsData.details = {};
    }

    res.json(dnsData);
  } catch (error) {
    console.error('Error getting DNS status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın SSL durumunu getirir
 * GET /api/monitors/ssl/:id
 */
router.get('/ssl/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // SSL durumunu al
    const sslData = await redisClient.hgetall(`monitor:ssl:${id}`);
    if (!sslData) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (sslData.details) {
      try {
        sslData.details = JSON.parse(sslData.details);
      } catch (e) {
        sslData.details = {};
      }
    } else {
      sslData.details = {};
    }

    res.json(sslData);
  } catch (error) {
    console.error('Error getting SSL status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın veritabanı durumunu getirir
 * GET /api/monitors/database/:id
 */
router.get('/database/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Veritabanı durumunu al
    const dbData = await redisClient.hgetall(`monitor:database:${id}`);
    if (!dbData) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (dbData.details) {
      try {
        dbData.details = JSON.parse(dbData.details);
      } catch (e) {
        dbData.details = {};
      }
    } else {
      dbData.details = {};
    }

    res.json(dbData);
  } catch (error) {
    console.error('Error getting database status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın API durumunu getirir
 * GET /api/monitors/api/:id
 */
router.get('/api/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // API durumunu al
    const apiData = await redisClient.hgetall(`monitor:api:${id}`);
    if (!apiData) {
      return res.json({
        status: 'unknown',
        responseTime: 0,
        lastCheck: null,
        error: '',
        details: '{}'
      });
    }

    // Details alanını JSON'dan parse et
    if (apiData.details) {
      try {
        apiData.details = JSON.parse(apiData.details);
      } catch (e) {
        apiData.details = {};
      }
    } else {
      apiData.details = {};
    }

    res.json(apiData);
  } catch (error) {
    console.error('Error getting API status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş DNS verilerini getirir
 * GET /api/monitors/history/dns/:id
 */
router.get('/history/dns/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:dns:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting DNS history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş SSL verilerini getirir
 * GET /api/monitors/history/ssl/:id
 */
router.get('/history/ssl/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:ssl:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting SSL history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş veritabanı verilerini getirir
 * GET /api/monitors/history/database/:id
 */
router.get('/history/database/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:database:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting database history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş API verilerini getirir
 * GET /api/monitors/history/api/:id
 */
router.get('/history/api/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:api:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting API history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş SMTP verilerini getirir
 * GET /api/monitors/history/smtp/:id
 */
router.get('/history/smtp/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:smtp:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting SMTP history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});



/**
 * Bir cihazın geçmiş IPMI verilerini getirir
 * GET /api/monitors/history/ipmi/:id
 */
router.get('/history/ipmi/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:ipmi:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting IPMI history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Bir cihazın geçmiş Docker verilerini getirir
 * GET /api/monitors/history/docker/:id
 */
router.get('/history/docker/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Geçmiş verileri al
    const historyData = await redisClient.lrange(`history:docker:${id}`, 0, -1);

    // JSON'a dönüştür
    const history = historyData.map(item => JSON.parse(item));

    res.json(history);
  } catch (error) {
    console.error('Error getting Docker history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Tüm cihazların durum geçmişini getirir
 * GET /api/monitors/status/history
 */
router.get('/status/history', async (req, res) => {
  try {
    // Zaman aralığı parametresini al (varsayılan: 1440 dakika = 24 saat)
    const timeRange = parseInt(req.query.timeRange) || 1440;

    // Şu anki zaman
    const now = Date.now();

    // Başlangıç zamanı (timeRange dakika öncesi)
    const startTime = now - (timeRange * 60 * 1000);

    // Tüm cihazları al
    const deviceIds = await redisClient.smembers('devices');

    // Zaman noktalarını belirle
    const timePoints = [];
    const dataPoints = 12; // Gösterilecek veri noktası sayısı

    if (timeRange <= 1440) { // 24 saat veya daha az
      // Saatlik veri noktaları
      for (let i = dataPoints - 1; i >= 0; i--) {
        const timePoint = now - (timeRange * 60 * 1000 * i / (dataPoints - 1));
        timePoints.push(timePoint);
      }
    } else {
      // Günlük veri noktaları
      for (let i = dataPoints - 1; i >= 0; i--) {
        const timePoint = now - (timeRange * 60 * 1000 * i / (dataPoints - 1));
        timePoints.push(timePoint);
      }
    }

    // Her zaman noktası için durum dağılımını hesapla
    const statusData = {
      labels: [],
      datasets: [
        {
          label: 'Çevrimiçi',
          data: [],
          backgroundColor: 'rgba(16, 185, 129, 0.7)', // Yeşil
          borderColor: 'rgba(16, 185, 129, 1)',
          borderWidth: 1,
        },
        {
          label: 'Çevrimdışı',
          data: [],
          backgroundColor: 'rgba(239, 68, 68, 0.7)', // Kırmızı
          borderColor: 'rgba(239, 68, 68, 1)',
          borderWidth: 1,
        },
        {
          label: 'Uyarı Veren',
          data: [],
          backgroundColor: 'rgba(245, 158, 11, 0.7)', // Sarı
          borderColor: 'rgba(245, 158, 11, 1)',
          borderWidth: 1,
        },
        {
          label: 'Kritik',
          data: [],
          backgroundColor: 'rgba(249, 115, 22, 0.7)', // Turuncu
          borderColor: 'rgba(249, 115, 22, 1)',
          borderWidth: 1,
        },
        {
          label: 'Performans Düşük',
          data: [],
          backgroundColor: 'rgba(139, 92, 246, 0.7)', // Mor
          borderColor: 'rgba(139, 92, 246, 1)',
          borderWidth: 1,
        },
        {
          label: 'Kararsız',
          data: [],
          backgroundColor: 'rgba(236, 72, 153, 0.7)', // Pembe
          borderColor: 'rgba(236, 72, 153, 1)',
          borderWidth: 1,
        },
        {
          label: 'Kısmi Veri',
          data: [],
          backgroundColor: 'rgba(14, 165, 233, 0.7)', // Açık Mavi
          borderColor: 'rgba(14, 165, 233, 1)',
          borderWidth: 1,
        },
        {
          label: 'Bilinmiyor',
          data: [],
          backgroundColor: 'rgba(156, 163, 175, 0.7)', // Gri
          borderColor: 'rgba(156, 163, 175, 1)',
          borderWidth: 1,
        }
      ]
    };

    // Etiketleri oluştur
    statusData.labels = timePoints.map(timestamp => {
      const date = new Date(timestamp);
      if (timeRange <= 1440) { // 24 saat veya daha az
        return date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
      } else {
        return date.toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit' });
      }
    });

    // Her zaman noktası için durum dağılımını hesapla
    for (const timePoint of timePoints) {
      let online = 0;
      let offline = 0;
      let warning = 0;
      let critical = 0;
      let degraded = 0;
      let flapping = 0;
      let partial = 0;
      let unknown = 0;

      // Her cihaz için en son durumu kontrol et
      for (const deviceId of deviceIds) {
        try {
          // Cihazın hesaplanmış durumunu al
          const deviceStatusKey = `device:status:${deviceId}`;
          const deviceStatus = await redisClient.hgetall(deviceStatusKey);

          // Hesaplanmış durum varsa kullan
          if (deviceStatus && deviceStatus.status) {
            // Duruma göre sayıları artır
            switch (deviceStatus.status) {
              case 'up':
                online++;
                break;
              case 'down':
                offline++;
                break;
              case 'warning':
                warning++;
                break;
              case 'critical':
                critical++;
                break;
              case 'degraded':
                degraded++;
                break;
              case 'flapping':
                flapping++;
                break;
              case 'partial':
                partial++;
                break;
              default:
                unknown++;
                break;
            }
          } else {
            // Hesaplanmış durum yoksa, ICMP geçmişini kontrol et
            const icmpHistory = await redisClient.lrange(`history:icmp:${deviceId}`, 0, 99);

            // Bu zaman noktasından önceki en son durumu bul
            let lastStatus = 'unknown';
            let lastTimestamp = 0;

            for (const item of icmpHistory) {
              try {
                const data = JSON.parse(item);
                if (data.timestamp && data.timestamp <= timePoint && data.timestamp > lastTimestamp) {
                  lastStatus = data.status || 'unknown';
                  lastTimestamp = data.timestamp;
                }
              } catch (e) {
                console.error(`Error parsing ICMP history for device ${deviceId}:`, e);
              }
            }

            // ICMP durumuna göre sayıları artır
            if (lastStatus === 'up') online++;
            else if (lastStatus === 'down') offline++;
            else unknown++;
          }
        } catch (e) {
          console.error(`Error processing device ${deviceId}:`, e);
          unknown++;
        }
      }

      // Verileri ekle
      statusData.datasets[0].data.push(online);
      statusData.datasets[1].data.push(offline);
      statusData.datasets[2].data.push(warning);
      statusData.datasets[3].data.push(critical);
      statusData.datasets[4].data.push(degraded);
      statusData.datasets[5].data.push(flapping);
      statusData.datasets[6].data.push(partial);
      statusData.datasets[7].data.push(unknown);
    }

    res.json(statusData);
  } catch (error) {
    console.error('Error getting status history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Cihazların performans özetini getirir
 * GET /api/monitors/performance/summary
 */
router.get('/performance/summary', async (req, res) => {
  try {
    // Cihaz ID'lerini al
    const deviceIds = req.query.deviceId ?
      (Array.isArray(req.query.deviceId) ? req.query.deviceId : [req.query.deviceId]) :
      await redisClient.smembers('devices');

    // Performans özeti için sonuç dizisi
    const performanceSummary = [];

    // Her cihaz için performans verilerini al
    for (const deviceId of deviceIds) {
      // Cihaz bilgilerini al
      const deviceData = await redisClient.hgetall(`device:${deviceId}`);
      if (!deviceData) continue;

      // ICMP durumunu al
      const icmpData = await redisClient.hgetall(`monitor:icmp:${deviceId}`);

      // ICMP geçmişini al (son 100 kayıt)
      const icmpHistory = await redisClient.lrange(`history:icmp:${deviceId}`, 0, 99);

      // Yanıt süresi istatistiklerini hesapla
      let minResponseTime = Number.MAX_VALUE;
      let maxResponseTime = 0;
      let totalResponseTime = 0;
      let responseTimeCount = 0;

      // Geçmiş verileri işle
      for (const item of icmpHistory) {
        try {
          const data = JSON.parse(item);

          if (data.responseTime) {
            const responseTime = parseFloat(data.responseTime);

            if (!isNaN(responseTime)) {
              minResponseTime = Math.min(minResponseTime, responseTime);
              maxResponseTime = Math.max(maxResponseTime, responseTime);
              totalResponseTime += responseTime;
              responseTimeCount++;
            }
          }
        } catch (e) {
          console.error(`Error parsing ICMP history for device ${deviceId}:`, e);
        }
      }

      // Ortalama yanıt süresini hesapla
      const avgResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;

      // Cihaz performans özetini oluştur
      performanceSummary.push({
        deviceId,
        deviceName: deviceData.name,
        status: icmpData ? icmpData.status : 'unknown',
        currentResponseTime: icmpData ? parseFloat(icmpData.responseTime) || 0 : 0,
        minResponseTime: minResponseTime !== Number.MAX_VALUE ? minResponseTime : 0,
        maxResponseTime,
        avgResponseTime,
        lastCheck: icmpData ? parseInt(icmpData.lastCheck) || Date.now() : Date.now(),
        uptime: 0, // Şu an için hesaplanmıyor
        packetLoss: 0 // Şu an için hesaplanmıyor
      });
    }

    res.json(performanceSummary);
  } catch (error) {
    console.error('Error getting performance summary:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Tüm cihazların durumunu getirir
 * GET /api/monitors/status/all
 */
router.get('/status/all', async (req, res) => {
  try {
    // Cihaz durumu hesaplama servisini yükle
    const deviceStatusService = require('../services/deviceStatusService');

    // Tüm cihaz ID'lerini al
    const deviceIds = await redisClient.smembers('devices');

    // Her cihazın durumunu al
    const statuses = {};

    for (const id of deviceIds) {
      // Cihaz detaylarını al
      const deviceData = await redisClient.hgetall(`device:${id}`);
      if (!deviceData) continue;

      // Monitors alanını JSON'dan parse et
      if (deviceData.monitors) {
        try {
          deviceData.monitors = JSON.parse(deviceData.monitors);
        } catch (e) {
          deviceData.monitors = {};
        }
      } else {
        deviceData.monitors = {};
      }

      // ICMP durumunu al
      const icmpData = await redisClient.hgetall(`monitor:icmp:${id}`);

      // HTTP durumunu al
      const httpData = await redisClient.hgetall(`monitor:http:${id}`);

      // SNMP durumunu al
      const snmpData = await redisClient.hgetall(`monitor:snmp:${id}`);

      // DNS durumunu al
      const dnsData = await redisClient.hgetall(`monitor:dns:${id}`);

      // SSL durumunu al
      const sslData = await redisClient.hgetall(`monitor:ssl:${id}`);

      // Veritabanı durumunu al
      const dbData = await redisClient.hgetall(`monitor:database:${id}`);

      // API durumunu al
      const apiData = await redisClient.hgetall(`monitor:api:${id}`);

      // SMTP durumunu al
      const smtpData = await redisClient.hgetall(`monitor:smtp:${id}`);

      // System durumunu al
      const systemData = await redisClient.hgetall(`monitor:system:${id}`);

      // IPMI durumunu al
      const ipmiData = await redisClient.hgetall(`monitor:ipmi:${id}`);

      // Docker durumunu al
      const dockerData = await redisClient.hgetall(`monitor:docker:${id}`);

      // TCP durumunu al
      const tcpData = {};
      if (deviceData.monitors.tcp && deviceData.monitors.tcp.enabled) {
        const port = deviceData.monitors.tcp.port || 80;
        tcpData[port] = await redisClient.hgetall(`monitor:tcp:${id}:${port}`);
      }

      // Tüm izleme sonuçlarını bir araya getir
      const monitorResults = {
        icmp: icmpData,
        http: httpData,
        tcp: tcpData,
        snmp: snmpData,
        dns: dnsData,
        ssl: sslData,
        database: dbData,
        api: apiData,
        smtp: smtpData,
        system: systemData,
        ipmi: ipmiData,
        docker: dockerData
      };

      // Cihaz durumunu hesapla ve kaydet
      const calculatedStatus = await deviceStatusService.calculateAndSaveDeviceStatus(id, deviceData, monitorResults, redisClient);

      // Hesaplanan durumu al
      const statusData = await deviceStatusService.getDeviceStatus(id, redisClient);

      statuses[id] = {
        name: deviceData.name,
        host: deviceData.host,
        group: deviceData.group || 'Default',
        calculatedStatus: statusData.status, // Hesaplanan durum
        lastCalculated: statusData.lastCalculated, // Son hesaplama zamanı
        icmp: icmpData || {
          status: 'unknown',
          responseTime: '0',
          lastCheck: Date.now().toString(),
          error: '',
          details: JSON.stringify({
            alive: false,
            time: 0,
            min: 0,
            max: 0,
            avg: 0,
            stddev: 0,
            packetLoss: 100,
            status: 'unknown'
          })
        },
        http: httpData || { status: 'unknown' },
        tcp: tcpData,
        snmp: snmpData || { status: 'unknown' },
        dns: dnsData || { status: 'unknown' },
        ssl: sslData || { status: 'unknown' },
        database: dbData || { status: 'unknown' },
        api: apiData || { status: 'unknown' },
        smtp: smtpData || { status: 'unknown' },
        system: systemData || { status: 'unknown' },
        ipmi: ipmiData || { status: 'unknown' },
        docker: dockerData || { status: 'unknown' }
      };
    }

    res.json(statuses);
  } catch (error) {
    console.error('Error getting all statuses:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Belirli bir cihazın durumunu getirir
 * GET /api/monitors/status/:id
 */
router.get('/status/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Cihaz durumu hesaplama servisini yükle
    const deviceStatusService = require('../services/deviceStatusService');

    // Cihazın var olup olmadığını kontrol et
    const exists = await redisClient.sismember('devices', id);
    if (!exists) {
      return res.status(404).json({ error: 'Device not found' });
    }

    // Cihaz detaylarını al
    const deviceData = await redisClient.hgetall(`device:${id}`);

    // Monitors alanını JSON'dan parse et
    if (deviceData.monitors) {
      try {
        deviceData.monitors = JSON.parse(deviceData.monitors);
      } catch (e) {
        deviceData.monitors = {};
      }
    } else {
      deviceData.monitors = {};
    }

    // ICMP durumunu al
    const icmpData = await redisClient.hgetall(`monitor:icmp:${id}`);

    // HTTP durumunu al
    const httpData = await redisClient.hgetall(`monitor:http:${id}`);

    // SNMP durumunu al
    const snmpData = await redisClient.hgetall(`monitor:snmp:${id}`);

    // DNS durumunu al
    const dnsData = await redisClient.hgetall(`monitor:dns:${id}`);

    // SSL durumunu al
    const sslData = await redisClient.hgetall(`monitor:ssl:${id}`);

    // Veritabanı durumunu al
    const dbData = await redisClient.hgetall(`monitor:database:${id}`);

    // API durumunu al
    const apiData = await redisClient.hgetall(`monitor:api:${id}`);

    // SMTP durumunu al
    const smtpData = await redisClient.hgetall(`monitor:smtp:${id}`);

    // System durumunu al
    let systemData = await redisClient.hgetall(`monitor:system:${id}`);
    if (!systemData || Object.keys(systemData).length === 0) {
      systemData = null;
    } else {
      // Details alanını JSON'dan parse et
      if (systemData.details) {
        try {
          systemData.details = JSON.parse(systemData.details);
        } catch (e) {
          systemData.details = {};
        }
      }
    }

    // IPMI durumunu al
    let ipmiData = await redisClient.hgetall(`monitor:ipmi:${id}`);
    if (!ipmiData || Object.keys(ipmiData).length === 0) {
      ipmiData = null;
    } else {
      // Details alanını JSON'dan parse et
      if (ipmiData.details) {
        try {
          ipmiData.details = JSON.parse(ipmiData.details);
        } catch (e) {
          ipmiData.details = {};
        }
      }
    }

    // Docker durumunu al
    let dockerData = await redisClient.hgetall(`monitor:docker:${id}`);
    if (!dockerData || Object.keys(dockerData).length === 0) {
      dockerData = null;
    } else {
      // Details alanını JSON'dan parse et
      if (dockerData.details) {
        try {
          dockerData.details = JSON.parse(dockerData.details);
        } catch (e) {
          dockerData.details = {};
        }
      }
    }

    // TCP durumunu al
    const tcpData = {};
    if (deviceData.monitors.tcp && deviceData.monitors.tcp.enabled) {
      const port = deviceData.monitors.tcp.port || 80;
      tcpData[port] = await redisClient.hgetall(`monitor:tcp:${id}:${port}`);
    }

    // Tüm izleme sonuçlarını bir araya getir
    const monitorResults = {
      icmp: icmpData,
      http: httpData,
      tcp: tcpData,
      snmp: snmpData,
      dns: dnsData,
      ssl: sslData,
      database: dbData,
      api: apiData,
      smtp: smtpData,
      system: systemData,
      ipmi: ipmiData,
      docker: dockerData
    };

    // Cihaz durumunu hesapla ve kaydet
    const calculatedStatus = await deviceStatusService.calculateAndSaveDeviceStatus(id, deviceData, monitorResults, redisClient);

    // Hesaplanan durumu al
    const statusData = await deviceStatusService.getDeviceStatus(id, redisClient);

    const status = {
      name: deviceData.name,
      host: deviceData.host,
      group: deviceData.group || 'Default',
      calculatedStatus: statusData.status, // Hesaplanan durum
      lastCalculated: statusData.lastCalculated, // Son hesaplama zamanı
      icmp: icmpData || {
        status: 'unknown',
        responseTime: '0',
        lastCheck: Date.now().toString(),
        error: '',
        details: JSON.stringify({
          alive: false,
          time: 0,
          min: 0,
          max: 0,
          avg: 0,
          stddev: 0,
          packetLoss: 100,
          status: 'unknown'
        })
      },
      http: httpData || { status: 'unknown' },
      tcp: tcpData,
      snmp: snmpData || { status: 'unknown' },
      dns: dnsData || { status: 'unknown' },
      ssl: sslData || { status: 'unknown' },
      database: dbData || { status: 'unknown' },
      api: apiData || { status: 'unknown' },
      smtp: smtpData || { status: 'unknown' },
      system: systemData || { status: 'unknown' },
      ipmi: ipmiData || { status: 'unknown' },
      docker: dockerData || { status: 'unknown' }
    };

    res.json(status);
  } catch (error) {
    console.error(`Error getting status for device ${req.params.id}:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Birden fazla cihazı toplu olarak kontrol eder
 * POST /api/monitors/check/bulk
 */
router.post('/check/bulk', async (req, res) => {
  try {
    const { deviceIds } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({ error: 'Device IDs are required' });
    }

    const results = {};
    const io = req.app.get('io');

    // Her cihazı kontrol et
    for (const id of deviceIds) {
      // Cihazın var olup olmadığını kontrol et
      const exists = await redisClient.sismember('devices', id);
      if (!exists) {
        results[id] = { error: 'Device not found' };
        continue;
      }

      // Cihaz detaylarını al
      const deviceData = await redisClient.hgetall(`device:${id}`);
      if (!deviceData) {
        results[id] = { error: 'Device not found' };
        continue;
      }

      // Monitors alanını JSON'dan parse et
      if (deviceData.monitors) {
        try {
          deviceData.monitors = JSON.parse(deviceData.monitors);
        } catch (e) {
          deviceData.monitors = {};
        }
      } else {
        deviceData.monitors = {};
      }

      // Alerts alanını JSON'dan parse et
      if (deviceData.alerts) {
        try {
          deviceData.alerts = JSON.parse(deviceData.alerts);
        } catch (e) {
          deviceData.alerts = [];
        }
      } else {
        deviceData.alerts = [];
      }

      // Cihazı izle
      results[id] = await scheduler.monitorDevice({ id, ...deviceData }, io);
    }

    res.json(results);
  } catch (error) {
    console.error('Error checking devices in bulk:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
