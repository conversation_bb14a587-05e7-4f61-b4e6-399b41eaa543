/**
 * İzleyici ayarları için yardımcı fonksiyonlar
 */

/**
 * Bir string'in IP adresi olup olmadığını kontrol eder
 * @param {string} host - Kontrol edilecek string
 * @returns {boolean} - IP adresi ise true, değilse false
 */
const isIpAddress = (host) => {
  if (!host || typeof host !== 'string') return false;

  // IPv4 regex
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  // IPv6 regex (basitleştirilmiş)
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  return ipv4Regex.test(host) || ipv6Regex.test(host);
};

/**
 * İzleyici ayarlarını doğrular ve varsayılan değerleri ayarlar
 * @param {Object} deviceData - Cihaz verileri
 * @returns {Object} - Doğrulanmış ve varsayılan değerleri ayarlanmış cihaz verileri
 */
const validateAndSetDefaultMonitorSettings = (deviceData) => {
  // Monitors nesnesi yoksa oluştur
  if (!deviceData.monitors) {
    deviceData.monitors = {};
  }

  // ICMP izleyicisi her zaman etkin olmalı
  if (!deviceData.monitors.icmp) {
    deviceData.monitors.icmp = { enabled: true, interval: '5' };
  } else {
    deviceData.monitors.icmp.enabled = true;
    // Kontrol aralığı belirtilmemişse, varsayılan olarak 5 dakika kullan
    if (!deviceData.monitors.icmp.interval) {
      deviceData.monitors.icmp.interval = '5';
    }
  }

  // DNS izleyicisi etkinleştirilmişse
  if (deviceData.monitors.dns && deviceData.monitors.dns.enabled) {
    // DNS sunucusu belirtilmemişse, varsayılan olarak Google DNS kullan
    if (!deviceData.monitors.dns.server) {
      deviceData.monitors.dns.server = '*******';
    }

    // Sorgulanacak alan adı belirtilmemişse ve host bir IP adresi değilse
    if (!deviceData.monitors.dns.domain && !isIpAddress(deviceData.host)) {
      deviceData.monitors.dns.domain = deviceData.host;
    } else if (!deviceData.monitors.dns.domain) {
      // Host bir IP adresi ise ve domain belirtilmemişse, varsayılan olarak google.com kullan
      deviceData.monitors.dns.domain = 'google.com';
    }

    // Kayıt türü belirtilmemişse, varsayılan olarak A kullan
    if (!deviceData.monitors.dns.recordType) {
      deviceData.monitors.dns.recordType = 'A';
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 5 dakika kullan
    if (!deviceData.monitors.dns.interval) {
      deviceData.monitors.dns.interval = '5';
    }
  }

  // HTTP izleyicisi etkinleştirilmişse
  if (deviceData.monitors.http && deviceData.monitors.http.enabled) {
    // URL belirtilmemişse, host değerini kullanarak oluştur
    if (!deviceData.monitors.http.url) {
      const protocol = deviceData.monitors.http.useHttps ? 'https' : 'http';
      deviceData.monitors.http.url = `${protocol}://${deviceData.host}`;
    }

    // Metod belirtilmemişse, varsayılan olarak GET kullan
    if (!deviceData.monitors.http.method) {
      deviceData.monitors.http.method = 'GET';
    }

    // Beklenen durum kodu belirtilmemişse, varsayılan olarak 200 kullan
    if (!deviceData.monitors.http.expectedStatus) {
      deviceData.monitors.http.expectedStatus = 200;
    }
  }

  // SSL izleyicisi etkinleştirilmişse
  if (deviceData.monitors.ssl && deviceData.monitors.ssl.enabled) {
    // Port belirtilmemişse, varsayılan olarak 443 kullan
    if (!deviceData.monitors.ssl.port) {
      deviceData.monitors.ssl.port = 443;
    }

    // Host belirtilmemişse, cihazın host değerini kullan
    if (!deviceData.monitors.ssl.host) {
      deviceData.monitors.ssl.host = deviceData.host;
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 60 dakika kullan
    if (!deviceData.monitors.ssl.interval) {
      deviceData.monitors.ssl.interval = '60';
    }
  }

  // TCP izleyicisi etkinleştirilmişse
  if (deviceData.monitors.tcp && deviceData.monitors.tcp.enabled) {
    // Port belirtilmemişse, varsayılan olarak 80 kullan
    if (!deviceData.monitors.tcp.port) {
      deviceData.monitors.tcp.port = 80;
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 5 dakika kullan
    if (!deviceData.monitors.tcp.interval) {
      deviceData.monitors.tcp.interval = '5';
    }
  }

  // SNMP izleyicisi etkinleştirilmişse
  if (deviceData.monitors.snmp && deviceData.monitors.snmp.enabled) {
    // Community belirtilmemişse, varsayılan olarak public kullan
    if (!deviceData.monitors.snmp.community) {
      deviceData.monitors.snmp.community = 'public';
    }

    // OID belirtilmemişse, varsayılan olarak system uptime OID'sini kullan
    if (!deviceData.monitors.snmp.oid) {
      deviceData.monitors.snmp.oid = '1.3.6.1.2.1.1.3.0';
    }

    // Port belirtilmemişse, varsayılan olarak 161 kullan
    if (!deviceData.monitors.snmp.port) {
      deviceData.monitors.snmp.port = 161;
    }

    // Versiyon belirtilmemişse, varsayılan olarak 2c kullan
    if (!deviceData.monitors.snmp.version) {
      deviceData.monitors.snmp.version = '2c';
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 5 dakika kullan
    if (!deviceData.monitors.snmp.interval) {
      deviceData.monitors.snmp.interval = '5';
    }
  }

  // Veritabanı izleyicisi etkinleştirilmişse
  if (deviceData.monitors.database && deviceData.monitors.database.enabled) {
    // Tür belirtilmemişse, varsayılan olarak mysql kullan
    if (!deviceData.monitors.database.type) {
      deviceData.monitors.database.type = 'mysql';
    }

    // Host belirtilmemişse, cihazın host değerini kullan
    if (!deviceData.monitors.database.host) {
      deviceData.monitors.database.host = deviceData.host;
    }

    // Port belirtilmemişse, veritabanı türüne göre varsayılan port kullan
    if (!deviceData.monitors.database.port) {
      switch (deviceData.monitors.database.type) {
        case 'mysql':
          deviceData.monitors.database.port = 3306;
          break;
        case 'postgresql':
          deviceData.monitors.database.port = 5432;
          break;
        case 'mongodb':
          deviceData.monitors.database.port = 27017;
          break;
        case 'redis':
          deviceData.monitors.database.port = 6379;
          break;
        default:
          deviceData.monitors.database.port = 3306;
      }
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 5 dakika kullan
    if (!deviceData.monitors.database.interval) {
      deviceData.monitors.database.interval = '5';
    }
  }

  // API izleyicisi etkinleştirilmişse
  if (deviceData.monitors.api && deviceData.monitors.api.enabled) {
    // URL belirtilmemişse, host değerini kullanarak oluştur
    if (!deviceData.monitors.api.url) {
      deviceData.monitors.api.url = `http://${deviceData.host}/api`;
    }

    // Metod belirtilmemişse, varsayılan olarak GET kullan
    if (!deviceData.monitors.api.method) {
      deviceData.monitors.api.method = 'GET';
    }

    // Beklenen durum kodu belirtilmemişse, varsayılan olarak 200 kullan
    if (!deviceData.monitors.api.expectedStatus) {
      deviceData.monitors.api.expectedStatus = 200;
    }

    // Headers belirtilmemişse, boş bir nesne kullan
    if (!deviceData.monitors.api.headers) {
      deviceData.monitors.api.headers = {};
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 5 dakika kullan
    if (!deviceData.monitors.api.interval) {
      deviceData.monitors.api.interval = '5';
    }
  }

  // SMTP izleyicisi etkinleştirilmişse
  if (deviceData.monitors.smtp && deviceData.monitors.smtp.enabled) {
    // Protokol belirtilmemişse, varsayılan olarak smtp kullan
    if (!deviceData.monitors.smtp.protocol) {
      deviceData.monitors.smtp.protocol = 'smtp';
    }

    // Host belirtilmemişse, cihazın host değerini kullan
    if (!deviceData.monitors.smtp.host) {
      deviceData.monitors.smtp.host = deviceData.host;
    }

    // Port belirtilmemişse, protokol ve güvenlik ayarına göre varsayılan port kullan
    if (!deviceData.monitors.smtp.port) {
      const protocol = deviceData.monitors.smtp.protocol.toLowerCase();
      const secure = deviceData.monitors.smtp.secure || false;

      switch (protocol) {
        case 'smtp':
          deviceData.monitors.smtp.port = secure ? 465 : 25;
          break;
        case 'pop3':
          deviceData.monitors.smtp.port = secure ? 995 : 110;
          break;
        case 'imap':
          deviceData.monitors.smtp.port = secure ? 993 : 143;
          break;
        default:
          deviceData.monitors.smtp.port = 25;
      }
    }

    // Güvenlik ayarı belirtilmemişse, port numarasına göre otomatik belirle
    if (deviceData.monitors.smtp.secure === undefined) {
      const port = parseInt(deviceData.monitors.smtp.port);
      deviceData.monitors.smtp.secure = (port === 465 || port === 995 || port === 993);
    }

    // Timeout belirtilmemişse, varsayılan olarak 5000ms kullan
    if (!deviceData.monitors.smtp.timeout) {
      deviceData.monitors.smtp.timeout = 5000;
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 10 dakika kullan
    if (!deviceData.monitors.smtp.interval) {
      deviceData.monitors.smtp.interval = '10';
    }

    // Authentication bilgileri varsa doğrula
    if (deviceData.monitors.smtp.auth) {
      // Username ve password birlikte olmalı
      if (deviceData.monitors.smtp.auth.username && !deviceData.monitors.smtp.auth.password) {
        delete deviceData.monitors.smtp.auth.password;
      }
      if (!deviceData.monitors.smtp.auth.username && deviceData.monitors.smtp.auth.password) {
        delete deviceData.monitors.smtp.auth.username;
      }

      // Boş auth objesi varsa sil
      if (!deviceData.monitors.smtp.auth.username && !deviceData.monitors.smtp.auth.password) {
        delete deviceData.monitors.smtp.auth;
      }
    }
  }



  // IPMI izleyicisi etkinleştirilmişse
  if (deviceData.monitors.ipmi && deviceData.monitors.ipmi.enabled) {
    // Host belirtilmemişse, cihazın host değerini kullan
    if (!deviceData.monitors.ipmi.host) {
      deviceData.monitors.ipmi.host = deviceData.host;
    }

    // Username ve password zorunlu
    if (!deviceData.monitors.ipmi.username) {
      throw new Error('IPMI username is required');
    }
    if (!deviceData.monitors.ipmi.password) {
      throw new Error('IPMI password is required');
    }

    // Port belirtilmemişse, varsayılan olarak 623 kullan
    if (!deviceData.monitors.ipmi.port) {
      deviceData.monitors.ipmi.port = 623;
    }

    // Timeout belirtilmemişse, varsayılan olarak 10000ms kullan
    if (!deviceData.monitors.ipmi.timeout) {
      deviceData.monitors.ipmi.timeout = 10000;
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 30 dakika kullan
    if (!deviceData.monitors.ipmi.interval) {
      deviceData.monitors.ipmi.interval = '30';
    }
  }

  // Docker izleyicisi etkinleştirilmişse
  if (deviceData.monitors.docker && deviceData.monitors.docker.enabled) {
    // Host belirtilmemişse, cihazın host değerini kullan
    if (!deviceData.monitors.docker.host) {
      deviceData.monitors.docker.host = deviceData.host;
    }

    // Port belirtilmemişse, varsayılan olarak 2376 kullan
    if (!deviceData.monitors.docker.port) {
      deviceData.monitors.docker.port = 2376;
    }

    // Secure belirtilmemişse, varsayılan olarak false kullan
    if (deviceData.monitors.docker.secure === undefined) {
      deviceData.monitors.docker.secure = false;
    }

    // Timeout belirtilmemişse, varsayılan olarak 10000ms kullan
    if (!deviceData.monitors.docker.timeout) {
      deviceData.monitors.docker.timeout = 10000;
    }

    // Kontrol aralığı belirtilmemişse, varsayılan olarak 15 dakika kullan
    if (!deviceData.monitors.docker.interval) {
      deviceData.monitors.docker.interval = '15';
    }
  }

  return deviceData;
};

module.exports = {
  isIpAddress,
  validateAndSetDefaultMonitorSettings
};
