{"name": "netwatch", "version": "1.0.0", "description": "NetWatch - Agent gerektirmeyen ağ izleme yazılımı", "main": "server/app.js", "scripts": {"start": "node server/app.js", "server": "nodemon server/app.js", "client": "cd client && NODE_OPTIONS=--no-warnings npm start", "dev": "concurrently --kill-others-on-fail \"npm run server\" \"npm run client\"", "dev:server": "nodemon server/app.js", "dev:client": "cd client && NODE_OPTIONS=--no-warnings npm start", "install-all": "npm install && cd client && npm install"}, "keywords": ["network", "monitoring", "icmp", "snmp", "http", "tcp", "dns", "ssl", "database", "api"], "author": "", "license": "MIT", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dns-packet": "^5.6.1", "dockerode": "^4.0.7", "dotenv": "^16.5.0", "express": "^4.18.2", "generic-pool": "^3.9.0", "ioredis": "^4.30.0", "lodash": "^4.17.21", "mysql2": "^3.14.0", "net-ping": "^1.2.4", "net-snmp": "^3.22.0", "node-cron": "^3.0.3", "node-os-utils": "^1.3.7", "node-wmi": "^0.0.5", "nodemailer": "^6.10.1", "pg": "^8.14.1", "raw-socket": "^1.8.1", "socket.io": "^4.7.2", "sonner": "^2.0.3", "ssh2": "^1.16.0", "ssl-checker": "^2.0.10", "systeminformation": "^5.22.11"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.1.9"}}