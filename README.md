# NetWatch

Agent gerektirmeyen, ağ cihazları ve sunucuları izlemeye yönelik bir ağ izleme yazılımı.

## Özellikler

### İzleme Türleri
- **ICMP (Ping)** - Cihaz erişilebilirlik kontrolü
- **HTTP/HTTPS** - Web servisleri izleme
- **TCP Port** - Port erişilebilirlik kontrolü
- **SNMP** - Ağ cihazları izleme
- **DNS** - DNS çözümleme kontrolü
- **SSL Certificate** - SSL sertifika geçerlilik kontrolü
- **Database** - Veritabanı bağlantı kontrolü
- **API** - REST API endpoint izleme
- **SMTP/Email** - Email sunucu izleme
- **Windows System** - Windows sunucu sistem izleme (WinRM)
- **Linux System** - Linux sunucu sistem izleme (SSH)
- **Hardware (IPMI)** - Donanım durumu izleme (IPMI/BMC)
- **Container (Docker)** - Docker container izleme

### Sistem Özellikleri
- **Agent-less** - İzlenen cihazlarda agent kurulumu gerektirmez
- **Gerçek zamanlı dashboard** - Anlık durum görüntüleme
- **Akıllı bildirim sistemi** - E-posta ve in-app bildirimler
- **Responsive tasarım** - Mobil ve masaüstü uyumlu
- **Kullanıcı yönetimi** - Rol tabanlı erişim kontrolü
- **Veri saklama** - Geçmiş veriler ve raporlama

## Teknoloji Yığını

- **Frontend**: React.js + Material-UI
- **Backend**: Express.js + Socket.io
- **Veri Saklama**: Redis
- **Zamanlayıcı**: node-cron

## Kurulum

### Gereksinimler

- Node.js (v14+)
- Redis Server
- npm veya yarn

### Adımlar

1. Repoyu klonlayın:
   ```
   git clone https://github.com/yourusername/network-monitor.git
   cd network-monitor
   ```

2. Bağımlılıkları yükleyin:
   ```
   npm run install-all
   ```

3. `.env` dosyasını düzenleyin:
   ```
   cp .env.example .env
   # .env dosyasını düzenleyin
   ```

4. Uygulamayı başlatın:
   ```
   npm run dev
   ```

## Kullanım

- Web tarayıcınızda `http://localhost:3000` adresine gidin
- Cihazları ekleyin ve izlemeye başlayın

## Lisans

MIT
